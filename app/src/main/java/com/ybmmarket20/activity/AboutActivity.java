package com.ybmmarket20.activity;

import android.content.Intent;
import android.content.pm.PackageManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.UpdateManager;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.navigationbar.DefaultNavigationBar;
import com.ybmmarket20.utils.RoutersUtils;

import butterknife.Bind;

/**
 * 关于我们
 */
@Router("about")
public class AboutActivity extends BaseActivity {
    @Bind(R.id.about_version)
    TextView aboutVersion;
    @Bind(R.id.about_phone)
    TextView aboutPhone;
    @Bind(R.id.tv_unregister)
    TextView tvUnregister;
    private String versionName;
    private double clickTime;
    private int clickCount;

    @Override
    protected void initData() {
        setTitle("关于我们");

        aboutPhone.setText(getResources().getString(R.string.about_tv02));
        initVersion();
    }

    @Override
    protected void initHead() {
        super.initHead();
        new DefaultNavigationBar.Builder(this).setTitle("关于我们").build();
    }

    private void initVersion() {
        String packageName = this.getPackageName();
        try {
            versionName = this.getPackageManager().getPackageInfo(
                    packageName, 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        aboutVersion.setText("版本号:V" + UpdateManager.getVersion());
        if (BaseYBMApp.getApp().isDebug()) {
            aboutVersion.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(getApplicationContext(), DebugActivity.class));
                }
            });
        }
        tvUnregister.setOnClickListener(v -> {
            RoutersUtils.open("ybmpage://unregister");
        });
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_about;
    }

}
