package com.ybmmarket20.activity

import android.os.Bundle
import androidx.fragment.app.Fragment
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.fragments.AccountBasicInfoFragment
import com.ybmmarket20.fragments.AccountManagerFragment
import com.ybmmarket20.fragments.ShopManagerFragment
import kotlinx.android.synthetic.main.activity_account_basic_info.*
import java.util.*

const val EXTRA_BASIC_INFO_TABINDEX = "extra_basic_info_tabIndex"

@Router("accountbasicinfo")
class AccountBasicInfoActivity :BaseActivity(){
    private val titles = arrayOf("账号管理", "关联店铺", "基本信息")//, "发票信息")
    private var fragmentArrayList: ArrayList<Fragment>? = null
    private var basicInfoFragment: Fragment? = null
    private var invoiceInfoFragment: Fragment? = null
    private var accountManagerFragment: Fragment? = null

    override fun getContentViewId(): Int {
        return R.layout.activity_account_basic_info
    }

    override fun initData() {
        //setTitle("基本资料")
        iv_back.setOnClickListener {
            finish()
        }
        initTab()
    }

    private fun initTab() {
        fragmentArrayList = ArrayList()

        accountManagerFragment = AccountManagerFragment()
        val managerBundle = Bundle()
        managerBundle.putInt(EXTRA_BASIC_INFO_TABINDEX, 0)
        basicInfoFragment?.arguments=managerBundle

        val shopManagerFragment = ShopManagerFragment()

        basicInfoFragment = AccountBasicInfoFragment()
        val basicInfoBundle = Bundle()
        basicInfoBundle.putInt(EXTRA_BASIC_INFO_TABINDEX, 1)
        basicInfoFragment?.arguments=basicInfoBundle

//        invoiceInfoFragment = AccountBasicInfoFragment()
//        val invoiceInfoBundle = Bundle()
//        invoiceInfoBundle.putInt(EXTRA_BASIC_INFO_TABINDEX, 2)
//        invoiceInfoFragment?.arguments=invoiceInfoBundle


        fragmentArrayList?.add(accountManagerFragment as AccountManagerFragment)
        fragmentArrayList?.add(shopManagerFragment)
        fragmentArrayList?.add(basicInfoFragment as AccountBasicInfoFragment)
//        fragmentArrayList?.add(invoiceInfoFragment as AccountBasicInfoFragment)

        tabLayout.setViewPager(viewPager, titles, this, fragmentArrayList)


    }
}