package com.ybmmarket20.activity

import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.activity.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import butterknife.OnClick
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.Login
import com.ybmmarket20.bean.LoginInfo
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.SP_KEY_LOGIN_IS_KA
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.fragments.processLoginData
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.AccountStatus.updateStatus
import com.ybmmarket20.viewmodel.AccountInfoViewModel
import com.ybmmarketkotlin.activity.LoginVertificationActivity
import com.ybmmarketkotlin.utils.ChCrypto.aesEncrypt
import kotlinx.android.synthetic.main.activity_add_account.*

/**
 * 添加账户
 */
@Router("addaccount", "addaccount/:error")
class AddAccountActivity : BaseActivity() {

    //如果是密码错误重新登录，调用登录接口
    private var mReset = false
    private var loginData: Login? = null
    private val accountInfoViewModel: AccountInfoViewModel by viewModels()
    private var loginInfo: LoginInfo? = null
    override fun initData() {
        setTitle("添加账号")
        mReset = TextUtils.equals("1", intent.getStringExtra("error"))
        if (mReset) {
            tv_error!!.visibility = View.VISIBLE
            setTitle("账号登录")
            btn_add!!.text = "登录"
        }
        initObserver()
    }

    public override fun getContentViewId(): Int {
        return R.layout.activity_add_account
    }

    @OnClick(R.id.btn_add)
    fun clickTab(view: View?) {
        hideSoftInput()
        if (mReset) {
            reLogin()
        } else {
            fetchUserInfo()
        }
    }

//    LoginVertificationActivity.getStartIntent(this, SpUtil.getLoginPhone(), SpUtil.getMerchantid())
    private val launcher = this.registerForActivityResult(StartActivityForResult()) {
        if (it.data != null && it.data!!.getBooleanExtra(IntentCanst.LOGINVERTIFICATIONRESULT, false)) {
            processAccountLoginData(loginInfo)
        }
    }

    private fun initObserver() {
        accountInfoViewModel.loginLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                loginInfo = it.data
                if (loginInfo?.isCrawler == true) {
                    launcher.launch(LoginVertificationActivity.getStartIntent(this, SpUtil.getLoginPhone(), SpUtil.getMerchantid()))
                } else {
                    processAccountLoginData(loginInfo)
                }
            }
        }
    }

    private fun processAccountLoginData (loginInfo: LoginInfo?) {
        if (loginInfo?.loginSucceedUrl?.startsWith("ybmpage://main") == true) {
            processLoginData(this, loginInfo.loginSucceedUrl ?: "")
        } else {
            RoutersUtils.open(loginInfo?.loginSucceedUrl)
        }
        finish()
    }


    override fun onResume() {
        super.onResume()
        updateStatus(1)
    }

    private fun reLogin() {
        val userName = et_phone!!.text.toString().trim { it <= ' ' }
        val password = et_pwd!!.text.toString().trim { it <= ' ' }
        if (TextUtils.isEmpty(userName)) {
            ToastUtils.showShort("手机号不能为空")
            return
        }
        if (!UiUtils.isMobileNO(userName)) {
            ToastUtils.showShort(R.string.validate_mobile_error)
            return
        }
        if (TextUtils.isEmpty(password)) {
            ToastUtils.showShort("请输入密码")
            return
        }
        showProgress()
        accountInfoViewModel.login(userName, aesEncrypt(password))
    }

    private fun fetchUserInfo() {
        updateStatus(3)
        val phoneNum = et_phone!!.text.toString().trim { it <= ' ' }
        val password = et_pwd!!.text.toString().trim { it <= ' ' }
        if (TextUtils.isEmpty(phoneNum)) {
            ToastUtils.showShort("手机号不能为空")
            return
        }
        if (!UiUtils.isMobileNO(phoneNum)) {
            ToastUtils.showShort(R.string.validate_mobile_error)
            return
        }
        if (TextUtils.isEmpty(password)) {
            ToastUtils.showShort("请输入密码")
            return
        }
        showProgress()
        accountInfoViewModel.login(phoneNum, aesEncrypt(password))
    }

    private fun storeAndProcessLoginData(data: Login?, phoneNum: String, password: String) {
        AuditStatusSyncUtil.getInstance().setLicenseStatusOnly(data!!.licenseStatus)
        SpUtil.setValidityStatus(data.validity)
        SpUtil.setLoginPhone(phoneNum)
        //保存用户的信息
        saveLogin(data)
        //清除usersp电话信息
        SpUtil.writeString("phone", "")
        //清除密码强度的提示
        SpUtil.writeBoolean("already_mention", false)
        // 是否是ka用户
        SpUtil.writeBoolean(SP_KEY_LOGIN_IS_KA, data.isKa)
        //登陆广播
        LocalBroadcastManager.getInstance(applicationContext)
            .sendBroadcast(Intent(IntentCanst.ACTION_LOGIN))
        //更新购物车数量广播
        LocalBroadcastManager.getInstance(applicationContext)
            .sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
        //购物车商品缓存
        HandlerGoodsDao.getInstance().create4Sp()
        //通知"我的"页面重新刷新数据
        LocalBroadcastManager.getInstance(applicationContext)
            .sendBroadcast(Intent(IntentCanst.ACTION_SWITCH_USER))
        LocalBroadcastManager.getInstance(applicationContext)
            .sendBroadcast(Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE))
        //通知"全部药品列表"页面重新刷新数据
        LocalBroadcastManager.getInstance(applicationContext)
            .sendBroadcast(Intent(IntentCanst.ACTION_BRAND_SET))
        //后台线程获取用户信息并保存
        YbmCommand.getUserInfoForBack(phoneNum, password)
        //后台获取购物车的信息
        YbmCommand.getCartListForBack()
        YbmPushUtil.bindPushtoken()
        //重新获取消息数量
        Message.instance.findUnReadCount()
        //回到首页的"我的"页面
        RoutersUtils.open("ybmpage://main/4")
    }

    fun saveLogin(login: Login?) {
        if (login == null) {
            return
        }
        val merchantId = login.getMerchantId()
        if (merchantId != null && merchantId.toLong() > 0) {
            SpUtil.resetXyyReportSession()
            SpUtil.setMerchantid(merchantId + "")
            SpUtil.setToken(login.token)
        }
    }
}