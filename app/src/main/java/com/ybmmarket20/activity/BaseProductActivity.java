package com.ybmmarket20.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.annotation.CallSuper;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.ILicenseStatus;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarketkotlin.utils.RouterJump;

/**
 * 通用商品列表页面基类，用于增加采购单动画与点击跳转
 */
public abstract class BaseProductActivity extends CouponAnalysisGoodsActivity implements ILicenseStatus {
    protected GoodsListAdapter detailAdapter;
    protected RequestParams couponParams;
    protected TextView tvNum;
    protected View rlCart;
    protected TextView tvRight;
    protected String title;
    protected BroadcastReceiver br;
    private boolean showCart = true;
    private boolean needRefresh = false;//是否需要发送修改购物车数量的广播
    private boolean needSetTitle = true;//是否设置购物车的返回标题
    private AuditStatusSyncUtil.AuditStatusSyncListener licenseStatusListener = createListener();

    @CallSuper
    protected void initData() {
        super.initData();
        try {
            rlCart = getView(R.id.rl_cart);
            tvRight = getView(R.id.tv_right);
            if (rlCart != null) {
                rlCart.setVisibility(View.VISIBLE);
                tvNum = (TextView) getView(R.id.tv_num);
                if (tvNum != null) {
                    tvNum.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (rlCart != null) {
                                rlCart.getLocationInWindow(YBMAppLike.endLocationInCoupon);
                            }
                        }
                    }, 200);
                }
                getCartNumber();
                rlCart.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        cartClick();
                    }
                });
                br = new BroadcastReceiver() {
                    @Override
                    public void onReceive(Context context, Intent intent) {
                        if (intent != null) {
                            if (IntentCanst.ACTION_ADD_PRODUCT.equals(intent.getAction())) {
                                boolean isAdd = intent.getBooleanExtra("isAdd", false);
                                startAnim(isAdd);
                            } else if (IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL.equals(intent.getAction())) {//详情页面增加了商品
                                needRefresh = true;
                            } else if (IntentCanst.CART_NUM_CHANGED.equals(intent.getAction())) {//购物车数量修改了
                                getCartNumber();
                            }
                        }
                    }
                };
                IntentFilter intentFilter = new IntentFilter();
                intentFilter.addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL);
                intentFilter.addAction(IntentCanst.ACTION_ADD_PRODUCT);
                intentFilter.addAction(IntentCanst.CART_NUM_CHANGED);
                LocalBroadcastManager.getInstance(getApplicationContext()).registerReceiver(br, intentFilter);
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }

    }

    @Override
    protected void initCommon() {
        super.initCommon();
        if (onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().addLicenseStatusListener(licenseStatusListener);
    }

    protected void cartClick() {
        String name = getTitleName();
        if (TextUtils.isEmpty(name)) {
            name = "";
        }
        if (needSetTitle && !TextUtils.isEmpty(getRawAction())) {
            RoutersUtils.open("ybmpage://main?tab=2&name=" + name + "&id=" + RoutersUtils.encodeRAWUrl(getRawAction()));
        } else {
            RouterJump.INSTANCE.jump2ShopCar();
        }
    }

    protected String getTitleName() {
        try {
            TextView tv = (TextView) findViewById(R.id.tv_title);
            if (!TextUtils.isEmpty(tv.getText())) {
                return tv.getText().toString();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return "";
    }

    protected String getCartMobAgentId() {
        return "";
    }

    //购物车数量
    protected void getCartNumber() {
        int num = YBMAppLike.cartNum;
        if (num > 0) {
            if (tvNum != null) {
                if (num > 99) {
                    tvNum.setText(99 + "+");
                } else {
                    tvNum.setText(num + "");
                }
                tvNum.setVisibility(View.VISIBLE);
            }
        } else {
            if (tvNum != null) {
                tvNum.setText("");
                tvNum.setVisibility(View.GONE);
            }
        }
    }

    protected void startAnim(boolean isAdd) {
        if (rlCart != null) {
            Animation animation = null;
            if (isAdd) {
                animation = AnimationUtils.loadAnimation(this, R.anim.add_product);
            } else {
                animation = AnimationUtils.loadAnimation(this, R.anim.add_product);
            }
            rlCart.startAnimation(animation);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (needRefresh) {//同步商品数量修改
            try {
                if (detailAdapter != null) {
                    if (detailAdapter.getCurrPosition() >= 0) {
                        detailAdapter.notifyItemChanged(detailAdapter.getCurrPosition());
                    } else {
                        detailAdapter.notifyDataSetChanged();
                    }
                }
                refreshPosition();
            } catch (Throwable e) {
                BugUtil.sendBug(e);
                try {
                    if (detailAdapter != null) {
                        detailAdapter.notifyDataSetChanged();
                    }
                } catch (Throwable e1) {
                    BugUtil.sendBug(e1);
                }
            }
            needRefresh = false;
        }
    }

    //自己刷新数据
    protected void refreshPosition() {

    }

    protected void setShowCart(boolean showCart, String title) {
        this.title = title;
        setShowCart(showCart);
    }

    protected void setShowCart(boolean showCart) {
        this.showCart = showCart;
        if (showCart) {
            if (rlCart != null && tvRight != null) {
                rlCart.setVisibility(View.VISIBLE);
                tvRight.setVisibility(View.INVISIBLE);
            }
        } else {
            if (rlCart != null && tvRight != null) {
                rlCart.setVisibility(View.INVISIBLE);
                if (!TextUtils.isEmpty(title)) {
                    tvRight.setVisibility(View.VISIBLE);
                } else {
                    tvRight.setVisibility(View.INVISIBLE);
                }
            }
        }
    }

    //返回页面的url
    protected abstract String getRawAction();

    @Override
    protected void onDestroy() {
        needRefresh = false;
        if (br != null) {
            LocalBroadcastManager.getInstance(getApplicationContext()).unregisterReceiver(br);
        }
        if (onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().removeLicenseStatusListener(licenseStatusListener);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgOperationInfo(null);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgSearchRowsBean(null);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgSearchSomeField(null);
        super.onDestroy();
    }

    /**
     * 创建监听器
     */
    private AuditStatusSyncUtil.AuditStatusSyncListener createListener() {
        if (onLicenseStatusEnable()) return this::handleLicenseStatusChange;
        else return null;
    }

    /**
     * 更新审核状态
     *
     * @param status 当前状态
     */
    public void updateLicenseStatus(int status, AuditStatusSyncUtil.AuditStatusSyncListener currentListener) {
        AuditStatusSyncUtil.getInstance().updateLicenseStatus(status, currentListener);
    }

    /**
     * 获取当前监听器
     */
    protected AuditStatusSyncUtil.AuditStatusSyncListener getCurrentLicenesStatusListener() {
        return licenseStatusListener;
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return false;
    }

    @Override
    public void handleLicenseStatusChange(int status) {

    }

    protected boolean isSortnetSection(){
        return false;
    }
}
