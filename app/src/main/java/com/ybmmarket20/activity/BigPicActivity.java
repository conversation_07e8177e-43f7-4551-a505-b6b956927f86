package com.ybmmarket20.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.widget.LinearLayout;

import androidx.viewpager.widget.ViewPager;

import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.BigPicPagerAdapter;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.view.ZoomImageView;

import java.util.ArrayList;

import butterknife.Bind;
/*
* 图片查看
* */
@Router("BigPicActivity")
public class BigPicActivity extends BaseActivity {
    @Bind(R.id.vp_pic)
    androidx.viewpager.widget.ViewPager vpPic;
    @Bind(R.id.ll_pb)
    LinearLayout llPb;
    private String[] imageList;
    protected ArrayList<ZoomImageView> imageViewList;

    @Override
    protected void initData() {
        Intent intent = getIntent();
//        boolean isFromWeb = intent.getIntExtra("isFromWeb", 0) == 1;
        boolean isFromWeb = TextUtils.equals(intent.getStringExtra("isFromWeb"), "1");

        if (isFromWeb) {
            String urlListStr = intent.getStringExtra("imageListStr");
            if (TextUtils.isEmpty(urlListStr)) {
                imageList = new String[0];
            } else {
                try {
                    Gson gson = new Gson();
                    imageList = gson.fromJson(urlListStr, String[].class);
                } catch (Exception e) {
                    e.printStackTrace();
                    imageList = new String[0];
                }
            }
        } else {
            imageList = intent.getStringArrayExtra("imageList");
        }
        if (imageList == null) {
            imageList = new String[1];
            imageList[0] = "";
        }
        int currentPositionIntent = intent.getIntExtra("currentPosition", 0);
        String currentImageUrl = intent.getStringExtra("currentImageUrl");
        String title = intent.getStringExtra("title");
        if (TextUtils.isEmpty(title)) {
            title = "图片查看";
        }
        setTitle(title);
        int currentPosition = currentPositionIntent;
        imageViewList = new ArrayList<>();
        for (int i = 0; i < imageList.length; i++) {
            ZoomImageView iv = new ZoomImageView(this);
            imageViewList.add(iv);
            if (currentPositionIntent < 0 && !TextUtils.isEmpty(currentImageUrl) && currentImageUrl.equals(imageList[i])) {
                currentPosition = i;
            }
        }
        BigPicPagerAdapter mAdapter = new BigPicPagerAdapter(this, imageViewList, imageList);
        if(YBMAppLike.getApp().isLowDevice()){
            vpPic.setOffscreenPageLimit(1);
        }
        vpPic.setAdapter(mAdapter);
        vpPic.setCurrentItem(currentPosition);
        setRigthText((1 + currentPosition) + "/" + imageList.length);
        vpPic.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                setRigthText(1 + position + "/" + imageList.length);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_big_pic;
    }

    /**
     * 跳转需要传递的Intent
     * @param mContext
     * @return
     */
    public static Intent getIntent(Context mContext, String[] imageList, int currentPosition, String currentImageUrl, String title) {
        Intent intent = new Intent(mContext, BigPicActivity.class);
        intent.putExtra("imageList", imageList);
        intent.putExtra("currentPosition", currentPosition);
        intent.putExtra("currentImageUrl", currentImageUrl);
        intent.putExtra("title", title);
        return intent;
    }
}
