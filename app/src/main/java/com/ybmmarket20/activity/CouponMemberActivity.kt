package com.ybmmarket20.activity

import android.content.Context
import android.os.Bundle
import com.analysys.ANSAutoPageTracker
import com.flyco.tablayout.listener.OnTabSelectListener
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AgentOrderPageAdapter
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.fragments.CouponFragment
import kotlinx.android.synthetic.main.activity_coupon_member.*


/**
 * 我的优惠券
 */

const val COUPON_STATUS_UNRECEIVED = 1//1，未领取   2，已领取  3，已用完  4，失效   5，已删除  6，抢光了
const val COUPON_STATUS_UNUSED = 2
const val COUPON_STATUS_USED = 3
const val COUPON_STATUS_PAST_DUE = 4
const val COUPON_STATUS_KEY = "status"

@Router("couponmeber")
class CouponMemberActivity: BaseActivity(),ANSAutoPageTracker {

    companion object{
        fun jgTrackBtnClick(mContext: Context, btnName:String){
            val params = java.util.HashMap<String, Any>()
            params[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMineCoupon.PAGE_ID
            params[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMineCoupon.TITLE
            params[JGTrackManager.FIELD.FIELD_REFERRER] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_MODULE] = "功能"
            params[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName
            JGTrackManager.eventTrack(mContext, JGTrackManager.TrackMineWealth.EVENT_BTN_CLICK, params)
        }
    }
    override fun getContentViewId(): Int = R.layout.activity_coupon_member

    override fun initData() {
        val fragments = ArrayList<LazyFragment>()
        fragments.let {
            it.add(createFragments(COUPON_STATUS_UNUSED))
            it.add(createFragments(COUPON_STATUS_USED))
            it.add(createFragments(COUPON_STATUS_PAST_DUE))
        }
        val pageAdapter = AgentOrderPageAdapter(supportFragmentManager, fragments)
        nvp_my_coupon_list.offscreenPageLimit = 3
        nvp_my_coupon_list.adapter = pageAdapter
        nvp_my_coupon_list.setScroll(false)
        stl_my_coupon_list.setViewPager(nvp_my_coupon_list)
        stl_my_coupon_list.setIndicatorWidthEqualTitleHalf(true)
        stl_my_coupon_list.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                when(position){
                    0 -> { //COUPON_STATUS_UNUSED  未使用
                        jgTrackBtnClick(this@CouponMemberActivity,"未使用")
                    }

                    1->{ //COUPON_STATUS_USED 已使用
                        jgTrackBtnClick(this@CouponMemberActivity,"已使用")
                    }

                    2->{ //COUPON_STATUS_PAST_DUE  已失效
                        jgTrackBtnClick(this@CouponMemberActivity,"已失效")
                    }

                    else ->{

                    }
                }
            }

            override fun onTabReselect(position: Int) {
                when(position){
                    0 -> { //COUPON_STATUS_UNUSED  未使用
                        jgTrackBtnClick(this@CouponMemberActivity,"未使用")
                    }

                    1->{ //COUPON_STATUS_USED 已使用
                        jgTrackBtnClick(this@CouponMemberActivity,"已使用")
                    }

                    2->{ //COUPON_STATUS_PAST_DUE  已失效
                        jgTrackBtnClick(this@CouponMemberActivity,"已失效")
                    }

                    else ->{

                    }
                }
            }
        })
    }

    /**
     * 创建Fragment集合
     */
    private fun createFragments(couponStatus: Int): LazyFragment {
        return CouponFragment().apply {
            val bundle = Bundle()
            bundle.putInt(COUPON_STATUS_KEY, couponStatus)
            arguments = bundle
        }
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMineCoupon.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMineCoupon.TITLE
        return properties
    }

    override fun registerPageUrl(): String = this.getFullClassName()
}