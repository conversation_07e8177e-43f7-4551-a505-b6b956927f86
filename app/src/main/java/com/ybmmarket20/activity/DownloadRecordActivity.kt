package com.ybmmarket20.activity

import android.graphics.Color
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.DownloadRecordBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.DownloadRelatedAptitudeViewModel
import com.ybmmarketkotlin.utils.TimeUtils
import kotlinx.android.synthetic.main.activity_download_record.rv

/**
 * 资质下载记录
 */
@Router("downloadrecord/:orderNo")
class DownloadRecordActivity : BaseActivity() {

    private var mOrderNo: String? = ""

    private val mViewModel: DownloadRelatedAptitudeViewModel by viewModels()

    override fun getContentViewId(): Int = R.layout.activity_download_record

    override fun getBaseViewModel(): BaseViewModel = mViewModel


    override fun initData() {
        setTitle("下载记录")
        initObserver()
        mOrderNo = intent.getStringExtra("orderNo")
        mViewModel.getAptitudeDownloadRecordList(mOrderNo)

    }

    private fun initObserver() {
        mViewModel.recordLiveData.observe(this) {
            if (it.isSuccess) {
                val adapter = DownloadRecordAdapter(it.data)
                adapter.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无下载记录")
                rv.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
                rv.adapter = adapter
            }
        }
    }

    inner class DownloadRecordAdapter(data: List<DownloadRecordBean>) :
        YBMBaseAdapter<DownloadRecordBean>(R.layout.item_download_record, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: DownloadRecordBean?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->
                holder.setText(R.id.tvAptitudeTypeName, bean.qualificationTypeStr)
                holder.setText(R.id.tvMailContent, bean.email)
                holder.setText(R.id.tvDownloadTimeContent, TimeUtils.getFormatTime1(bean.createTime))
                holder.setText(R.id.tvFailureReasonContent, bean.failReason)
                val tvFailureReasonTitle = holder.getView<TextView>(R.id.tvFailureReasonTitle)
                val tvFailureReasonContent = holder.getView<TextView>(R.id.tvFailureReasonContent)
                holder.getView<RoundTextView>(R.id.rtvAptitudeStatus).apply {
                    setStrokeWidth(0.5f.dp)
                    when(bean.downloadStatus) {
                        //下载中
                        1 -> {
                            text = "下载中"
                            setTextColor(Color.parseColor("#FF9300"))
                            setBackgroundColor(Color.parseColor("#FEF4E5"))
                            setStrokeColor(Color.parseColor("#FF9300"))
                            tvFailureReasonTitle.isVisible = false
                            tvFailureReasonContent.isVisible = false
                        }
                        //成功
                        2 -> {
                            text = "下载成功"
                            setTextColor(Color.parseColor("#19B95A"))
                            setBackgroundColor(Color.parseColor("#EBF9F1"))
                            setStrokeColor(Color.parseColor("#3EC474"))
                            tvFailureReasonTitle.isVisible = false
                            tvFailureReasonContent.isVisible = false
                        }
                        //失败
                        3 -> {
                            text = "下载失败"
                            setTextColor(Color.parseColor("#F32B2B"))
                            setBackgroundColor(Color.parseColor("#FDE9E9"))
                            setStrokeColor(Color.parseColor("#F32B2B"))
                            tvFailureReasonTitle.isVisible = true
                            tvFailureReasonContent.isVisible = true
                        }
                    }
                }
            }
        }
    }
}