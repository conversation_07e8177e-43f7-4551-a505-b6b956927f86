package com.ybmmarket20.activity;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.utils.SpUtil;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 向导页面
 */
@Router({"guidepagea"})
public class GuidePageActivity extends BaseActivity {

    @Bind(R.id.guide_view_pager)
    ViewPager guideViewPager;
    @Bind(R.id.guide_button)
    TextView guideButton;

    int[] picReses = new int[]{R.drawable.guide_1, R.drawable.guide_2, R.drawable.guide_3};

    @Override
    protected void initData() {
        if (picReses.length <= 0) {
            SpUtil.writeInt(IntentCanst.GUIDE_RUN, IntentCanst.GUIDE_VERSION);
            goMainActivity();
            return;
        }
        guideViewPager.setOnPageChangeListener(pageListener);
        guideViewPager.setAdapter(pagerAdapter);
        pageListener.onPageSelected(0);
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_page_guide;
    }

    private PagerAdapter pagerAdapter = new PagerAdapter() {
        @Override
        public int getCount() {
            return picReses == null ? 0 : picReses.length;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, final int position) {
            ImageView iv = new ImageView(container.getContext());

            iv.setImageResource(picReses[position]);
            iv.setScaleType(ImageView.ScaleType.CENTER_CROP);
            iv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (position != pagerAdapter.getCount() - 1) {
                        guideViewPager.setCurrentItem(position + 1);
                    } else {
                        goMainActivity();
                    }
                }
            });
            container.addView(iv);
            return iv;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {

            container.removeView((View) object);
        }
    };

    private ViewPager.OnPageChangeListener pageListener = new ViewPager.OnPageChangeListener() {

        @Override
        public void onPageSelected(int position) {
            // 如果position是最后一个，button就显示，否则隐藏
            if (position == pagerAdapter.getCount() - 1) {
                guideButton.setVisibility(View.VISIBLE);
            } else {
                guideButton.setVisibility(View.GONE);
            }
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    };

    public void goMainActivity() {
        SpUtil.writeInt(IntentCanst.GUIDE_RUN, IntentCanst.GUIDE_VERSION);
        if (isLogin()) {
            gotoAtivity(MainActivity.class);
        } else {
            gotoAtivity(LoginActivity.class);
        }
        finish();
    }

    @OnClick({R.id.guide_button,R.id.btn_skip })
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.guide_button:
            case R.id.btn_skip:
                goMainActivity();
                break;
        }
    }
}
