package com.ybmmarket20.activity;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.MineVipGiftAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.GiftListBean;
import com.ybmmarket20.bean.GiftSkulistBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

@Router({"minevipgiftactivity"})
public class MineVipGiftActivity extends BaseProductActivity {

    @Bind(R.id.list_lv)
    CommonRecyclerView list;

    private int pageSize = 10;
    private int pager = 1;
    private String mEmptyStr;
    private List<GiftSkulistBean> rows;

    protected MineVipGiftAdapter adapter;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_mine_vip_gift;
    }

    @Override
    protected void initData() {
        super.initData();
        setTitle("物料心愿单");
        mEmptyStr = "您还没有物料心愿单";
        adapter = new MineVipGiftAdapter(R.layout.item_mine_vip_gift, rows);
        list.setEnabled(true);
        list.setAdapter(adapter);
//        adapter.openLoadMore(true);
        adapter.openLoadMore(pageSize, true);

        list.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, mEmptyStr);

        list.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getLoadMoreResponse(pager = 1);
            }

            @Override
            public void onLoadMore() {
                getLoadMoreResponse(pager);
            }
        });

    }

    @Override
    protected String getRawAction() {
        return "ybmpage://minevipgiftactivity/";
    }

    /**
     * 获取常用采购列表
     *
     * @param page 分页
     *             pageNum=&pageSize=&merchantId=
     */
    public void getLoadMoreResponse(final int page) {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("pageSize", String.valueOf(pageSize));
        params.put("pageNum", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.MINE_VIP_GIFT, params, new BaseResponse<GiftListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<GiftListBean> data, GiftListBean bean) {
                completion();
                if (data != null) {
                    if (data.isSuccess()) {

                        if (bean != null) {

                            boolean isLastPage = bean.isLastPage;

                            if (bean.list != null && bean.list.size() > 0) {
                                if (page <= 1) {
                                    MineVipGiftActivity.this.pager = 2;
                                } else {
                                    MineVipGiftActivity.this.pager++;
                                }
                            }
                            if (page <= 1) {

                                if (rows == null) {
                                    rows = new ArrayList<>();
                                }
                                rows.clear();
                                if (rows.size() <= 0 && bean.list != null) {
                                    rows.addAll(bean.list);
                                } else {
                                    if (bean.list == null || bean.list.isEmpty()) {

                                    } else {
                                        rows.addAll(0, bean.list);
                                    }
                                }
                                adapter.setNewData(rows);
//                                adapter.notifyDataChangedAfterLoadMore(rows.size() >= pageSize);
                                adapter.notifyDataChangedAfterLoadMore(!isLastPage);
                            } else {
                                if (bean.list == null || bean.list.size() <= 0) {
                                    adapter.notifyDataChangedAfterLoadMore(false);
                                } else {

                                    rows.addAll(bean.list);
                                    adapter.setNewData(rows);
//                                    adapter.notifyDataChangedAfterLoadMore(bean.list.size() >= pageSize);
                                    adapter.notifyDataChangedAfterLoadMore(!isLastPage);
                                }
                            }
                        }
                    } else {
                        adapter.setNewData(rows);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                completion();
                if (list != null) {
                    list.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                if (list != null) {
                                    adapter.setNewData(rows);
                                }
                            } catch (Throwable e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }, 300);
                }
            }
        });
    }

    private void completion() {
        if (list != null) {
            try {
                list.setRefreshing(false);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
        }
    }

}
