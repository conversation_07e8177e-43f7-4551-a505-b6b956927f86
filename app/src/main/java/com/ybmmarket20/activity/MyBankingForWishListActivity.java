package com.ybmmarket20.activity;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DecimalTextWatcher;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ButtonObserver;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 我的金融申请心愿单
 */
@Router({"mybankingforwishlist", "mybankingforwishlist/:shop_name/:name/:phone"})
public class MyBankingForWishListActivity extends BaseActivity {
    @Bind(R.id.tv_shop_name)
    TextView tvShopName;
    @Bind(R.id.et_phone)
    EditText etPhone;
    @Bind(R.id.tv_phone_tips)
    TextView tvPhoneTips;
    @Bind(R.id.et_name)
    EditText etName;
    @Bind(R.id.et_register_fund)
    EditText etRegisterFund;
    @Bind(R.id.et_loans)
    EditText etLoans;
    @Bind(R.id.et_remark)
    EditText etRemark;
    @Bind(R.id.btn_submit)
    ButtonObserver btnSubmit;

    @Bind(R.id.divider2)
    View divider2;
    @Bind(R.id.divider3)
    View divider3;
    @Bind(R.id.divider4)
    View divider4;
    @Bind(R.id.divider5)
    View divider5;
    @Bind(R.id.divider6)
    View divider6;

    private String name;
    private String shopName;
    private String phone;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_banking_for_wish_list;
    }

    @Override
    protected void initData() {
        setTitle("我的金融");
        editChangeListener();
        name = getIntent().getStringExtra("name");
        shopName = getIntent().getStringExtra("shop_name");
        phone = getIntent().getStringExtra("phone");
        initUi();

    }

    private void initUi() {
        etPhone.setText(phone);
        etName.setText(name);
        if (name != null) {
            etName.setSelection(name.length());
        }
        tvShopName.setText(shopName);
        //etRegisterFund.requestFocus();
    }

    private void editChangeListener() {
        btnSubmit.observer(etPhone, etName, etRegisterFund, etLoans);
        btnSubmit.setOnItemClickListener(isFlag -> btnSubmit.setEnabled(isFlag));
        etRegisterFund.addTextChangedListener(new DecimalTextWatcher());
        etLoans.addTextChangedListener(new DecimalTextWatcher());
        etPhone.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    divider3.setBackgroundResource(R.drawable.bg_banking_divider_selected);
                } else {
                    divider3.setBackgroundResource(R.drawable.bg_banking_divider_unselected);
                }
                if (hasFocus && tvPhoneTips.getVisibility() == View.VISIBLE) {
                    tvPhoneTips.setVisibility(View.GONE);
                }
            }
        });
        etPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                if (tvPhoneTips.getVisibility() == View.VISIBLE) {
                    tvPhoneTips.setVisibility(View.GONE);
                }
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        etName.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    divider2.setBackgroundResource(R.drawable.bg_banking_divider_selected);
                } else {
                    divider2.setBackgroundResource(R.drawable.bg_banking_divider_unselected);
                }
            }
        });
        etRegisterFund.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    divider4.setBackgroundResource(R.drawable.bg_banking_divider_selected);
                } else {
                    divider4.setBackgroundResource(R.drawable.bg_banking_divider_unselected);
                }
            }
        });
        etLoans.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    divider5.setBackgroundResource(R.drawable.bg_banking_divider_selected);
                } else {
                    divider5.setBackgroundResource(R.drawable.bg_banking_divider_unselected);
                }
            }
        });
        etRemark.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    divider6.setBackgroundResource(R.drawable.bg_banking_divider_selected);
                } else {
                    divider6.setBackgroundResource(R.drawable.bg_banking_divider_unselected);
                }
            }
        });
    }

    @OnClick({R.id.btn_submit})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.btn_submit:
                postSubmitContent();
                break;
        }
    }

    private void postSubmitContent() {
        final String phone = etPhone.getText().toString().trim();
        String shopName = tvShopName.getText().toString().trim();
        String name = etName.getText().toString().trim();
        final String loans = etLoans.getText().toString().trim();
        String registerFund = etRegisterFund.getText().toString().trim();
        String remark = etRemark.getText().toString().trim();

        if (TextUtils.isEmpty(shopName)) {
            ToastUtils.showShort(R.string.str_my_banking_validate_shop_name_error);
            return;
        }
        if (TextUtils.isEmpty(name)) {
            ToastUtils.showShort(R.string.str_my_banking_validate_name_error);
            return;
        }
        if (!UiUtils.isMobileNO(phone)) {
            tvPhoneTips.setVisibility(View.VISIBLE);
            ToastUtils.showShort(R.string.validate_mobile_error);
            return;
        }
        if (TextUtils.isEmpty(registerFund)) {
            ToastUtils.showShort(R.string.str_my_banking_validate_register_fund_error);
            return;
        }
        if (TextUtils.isEmpty(loans)) {
            ToastUtils.showShort(R.string.str_my_banking_validate_loans_error);
            return;
        }


        showProgress();
        btnSubmit.setEnabled(false);

        RequestParams params = new RequestParams();
        params.put("drugStore", shopName);
        params.put("userName", name);
        params.put("mobile", phone);
        params.put("registerMoney", registerFund);
        params.put("wishMoney", loans);
        params.put("remarks", remark);
        HttpManager.getInstance().post(AppNetConfig.MY_BANKING_WISH_LIST, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                btnSubmit.setEnabled(true);
                dismissProgress();
                if (null != obj) {
                    if (obj.isSuccess()) {
                        ToastUtils.showShort("提交成功");
                        finish();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                btnSubmit.setEnabled(true);
                dismissProgress();
            }
        });
    }
}
