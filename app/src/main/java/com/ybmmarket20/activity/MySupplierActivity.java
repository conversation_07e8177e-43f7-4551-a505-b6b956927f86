package com.ybmmarket20.activity;

import static com.ybmmarket20.constant.IntentCanst.JG_ENTRANCE;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER_TITLE;

import android.os.Bundle;

import com.analysys.ANSAutoPageTracker;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.MySupplierAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RequestSupplierBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 我的供应商
 */

@Router("mysupplier")
public class MySupplierActivity extends BaseActivity implements MySupplierAdapter.OnActionCallback, RecyclerRefreshLayout.OnRefreshListener, CommonRecyclerView.Listener, ANSAutoPageTracker {

    CommonRecyclerView rv;

    private MySupplierAdapter supplierAdapter;
    private List<RequestSupplierBean.SupplierBean> supplierList = new ArrayList<>();
    private int currentPage = 0;
    private int pageSize = 10;

    @Override
    public int getContentViewId() {
        return R.layout.activity_my_supplier;
    }

    private void findView() {
        rv = findViewById(R.id.rv);
    }

    @Override
    protected void initData() {
        findView();

        setTitle("我的供应商");

        supplierAdapter = new MySupplierAdapter(R.layout.item_my_supplier, supplierList, this);
        supplierAdapter.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, getResources().getString(R.string.no_data));
        rv.setAdapter(supplierAdapter);
        rv.setListener(this);
        rv.setLoadMoreEnable(false);

        showProgress("请求中...");
        requestData(0);
    }

    private void requestData(int p) {

        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("name", "");
        params.put("offset", String.valueOf(p));
        params.put("limit", String.valueOf(pageSize));
        HttpManager.getInstance().post(AppNetConfig.GET_MY_SUPPLIER_LIST, params, new BaseResponse<RequestSupplierBean>() {
            @Override
            public void onSuccess(String content, BaseBean<RequestSupplierBean> obj, RequestSupplierBean supplierBean) {
                dismissProgress();
                rv.setRefreshing(false);
                if (obj != null && obj.getData() != null && supplierBean.getRows() != null) {
                    currentPage = supplierBean.getCurrentPage();
                    if (p <= 0) {//刷新
                        supplierAdapter.setNewData(supplierBean.getRows());
                    } else {//是加载更多
                        supplierAdapter.addData(supplierBean.getRows());
                    }
                    supplierAdapter.notifyDataChangedAfterLoadMore(supplierAdapter.getItemCount() < supplierBean.getTotal());
                } else {
                    if (p <= 0) {//是刷新
//                        UiUtils.toast("无数据");
                    } else {
                        ToastUtils.showShort("没有更多数据");
                    }
                }

            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
                ToastUtils.showShort(error.message);
                rv.setRefreshing(false);
            }
        });
    }

    /**
     * 查看资质
     *
     * @param supplierBean
     */
    @Override
    public void onActionLookMore(RequestSupplierBean.SupplierBean supplierBean) {
        Bundle bundle = new Bundle();
        bundle.putSerializable("supplierBean", supplierBean);
        gotoAtivity(LookSupplierQualificationActivity.class, bundle);

        jgBtnClickTrack("查看资质");
    }

    /**
     * 进入店铺
     *
     * @param supplierBean
     */
    @Override
    public void onActionIntoShop(RequestSupplierBean.SupplierBean supplierBean) {
//        ShopActivity.launch(this, supplierBean.getOrgId());
        String mUrl = "ybmpage://shopactivity?orgId=" + supplierBean.getOrgId();
        HashMap<String,String> mParams = new HashMap<>();
        mParams.put(JG_REFERRER,AppUtilKt.getFullClassName(this));
        mParams.put(JG_REFERRER_TITLE,JGTrackManager.TrackMineSupplier.TITLE);
        mParams.put(JG_ENTRANCE,JGTrackManager.TrackMineSupplier.TITLE);
        mUrl = JGTrackTopLevelKt.splicingUrlWithParams(mUrl,mParams);
        RoutersUtils.open(mUrl);

        jgBtnClickTrack("进店");
    }

    private void jgBtnClickTrack(String btnName){
        HashMap<String,String> mParams = new HashMap<>();
        mParams.put(JGTrackManager.FIELD.FIELD_URL,AppUtilKt.getFullClassName(this));
        mParams.put(JGTrackManager.FIELD.FIELD_URL_DOMAIN,AppUtilKt.getFullClassName(this));
        mParams.put(JGTrackManager.FIELD.FIELD_REFERRER,AppUtilKt.getFullClassName(this));
        mParams.put(JGTrackManager.FIELD.FIELD_REFERRER_TITLE,JGTrackManager.TrackMineSupplier.TITLE);
        mParams.put(JGTrackManager.FIELD.FIELD_PAGE_ID,JGTrackManager.TrackMineSupplier.PAGE_ID);
        mParams.put(JGTrackManager.FIELD.FIELD_TITLE,JGTrackManager.TrackMineSupplier.TITLE);
        mParams.put(JGTrackManager.FIELD.FIELD_MODULE,"功能");
        mParams.put(JGTrackManager.FIELD.FIELD_BTN_NAME,btnName);
    }

    @Override
    public void onRefresh() {
        requestData(0);
    }

    @Override
    public void onLoadMore() {
        requestData(currentPage + 1);
    }

    @Override
    public Map<String, Object> registerPageProperties() {

        Map<String, Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackMineSupplier.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackMineSupplier.TITLE);
        return properties;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(this);
    }
}
