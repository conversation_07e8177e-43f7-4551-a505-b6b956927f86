package com.ybmmarket20.activity

import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.YBMPayUtil
import com.ybmmarket20.xyyreport.page.payResult.PayResultReport

abstract class PayResultAnalysisActivity: BaseActivity() {

    override fun initData() {
        val payway = intent.getStringExtra("payway")
        if (payway == YBMPayUtil.PAY_TRAN) return
        val orderNo = intent.getStringExtra("orderNo")
        PayResultReport.pvTrack(this, orderNo)
    }
}