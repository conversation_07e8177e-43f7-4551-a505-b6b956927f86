package com.ybmmarket20.activity;

import static com.ybmmarket20.activity.jdpay.AddBankCardActivityKt.BIND_RESULT_FROM_PAYMENT;
import static com.ybmmarket20.activity.jdpay.SetPayPwActivityKt.SET_PAY_PASSWORD_SETTING_PAY;
import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_AGENT_ORDER_REJECT_ORDER;
import static com.ybmmarket20.viewmodel.PayWayV2ViewModelKt.PAY_LAYOUT_TYPE_BANK_CARD;
import static com.ybmmarket20.viewmodel.PayWayV2ViewModelKt.PAY_LAYOUT_TYPE_NONG;
import static com.ybmmarket20.viewmodel.PayWayV2ViewModelKt.PAY_TYPE_NONG;
import static com.ybmmarket20.viewmodel.PayWayV2ViewModelKt.VIRTUAL_PAY_ID_ADD_BANK_CARD;
import static com.ybmmarket20.viewmodel.PayWayV2ViewModelKt.VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.lifecycle.Observer;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.luck.picture.lib.tools.ScreenUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.PaymentNewAdapter;
import com.ybmmarket20.adapter.ShoppingGoldRechargeAdapter;
import com.ybmmarket20.bean.AddressListBean;
import com.ybmmarket20.bean.AgentOrderListRowBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.BuySomethingCasuallyInfo;
import com.ybmmarket20.bean.BuySomethingUpdataBean;
import com.ybmmarket20.bean.CartDataBean;
import com.ybmmarket20.bean.CartGoodsInfo;
import com.ybmmarket20.bean.JgRequestParams;
import com.ybmmarket20.bean.PayTypeBankCard;
import com.ybmmarket20.bean.PayTypeConfigV2Bean;
import com.ybmmarket20.bean.PayWayBean;
import com.ybmmarket20.bean.PaymentBean;
import com.ybmmarket20.bean.PaymentOrderBean;
import com.ybmmarket20.bean.PaymentSuiXinPinQtData;
import com.ybmmarket20.bean.PaymentSuiXinPinSkusBean;
import com.ybmmarket20.bean.PaymentSuiXinPinSkusItemBean;
import com.ybmmarket20.bean.QueryOrderBean;
import com.ybmmarket20.bean.RecommendSuiXinPinBean;
import com.ybmmarket20.bean.RecommendShunShouMaiBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.bean.ReqUrlJsonBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.bean.ShopInfoSxpList;
import com.ybmmarket20.bean.SpellGroupGoodsItem;
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean;
import com.ybmmarket20.bean.TagBean;
import com.ybmmarket20.bean.cart.CartBean;
import com.ybmmarket20.bean.cart.CartItemBean;
import com.ybmmarket20.bean.payment.PaymentBalanceBean;
import com.ybmmarket20.bean.payment.PaymentBillBean;
import com.ybmmarket20.bean.payment.PaymentCompanyBean;
import com.ybmmarket20.bean.payment.PaymentCompanyListBean;
import com.ybmmarket20.bean.payment.PaymentItemBean;
import com.ybmmarket20.bean.payment.PaymentNewsBean;
import com.ybmmarket20.bean.payment.PaymentPayTypeBean;
import com.ybmmarket20.bean.payment.PaymentSettleBean;
import com.ybmmarket20.bean.payment.PaymentShopBean;
import com.ybmmarket20.bean.payment.PaymentShoppingGroupBean;
import com.ybmmarket20.bean.payment.PaymentSortedBean;
import com.ybmmarket20.bean.payment.PaymentSuiXinPins;
import com.ybmmarket20.bean.payment.VirtualGoldRechargeBean;
import com.ybmmarket20.bean.payment.VirtualGoldRechargeTextBean;
import com.ybmmarket20.common.AgentOrderRejectDialog;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JgOperationPositionInfo;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarket20.utils.AlertDialogHtml;
import com.ybmmarket20.utils.PopShowUtilKt;
import com.ybmmarket20.utils.PaymentBuyWithoutPopWindow;
import com.ybmmarket20.utils.RecommendSuiXinPinPopWindow;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.YBMPayUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.BankCardPopWindow;
import com.ybmmarket20.view.BaseBottomPopWindow;
import com.ybmmarket20.view.GiftSelectBottomDialog;
import com.ybmmarket20.view.InvoiceinformPopWindow;
import com.ybmmarket20.view.PayTypeLayout;
import com.ybmmarket20.view.PaymentBottomPayItemShowView;
import com.ybmmarket20.view.PaymentBuyWithoutGoodsView;
import com.ybmmarket20.view.PaymentSpellGroupRecommendGoodsView;
import com.ybmmarket20.view.payType.PayTypeLayoutV2;
import com.ybmmarket20.viewmodel.PayWayV2ViewModel;
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel;
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel;
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModelKt;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;
import com.ybmmarketkotlin.utils.AptitudeTipsUtils;
import com.ydmmarket.report.manager.TrackManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;
import kotlin.Pair;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * 提交订单，选择支付方式
 */
@Router({"payment/:tranNo/:shopIndex", "payment/:tranNo", "payment", "payment/:tranNo/:from/:agentOrderId"})
public class PaymentActivity extends PaymentAnalysisActivity {

    @Bind(R.id.tv_userName)
    TextView tvUserName;
    @Bind(R.id.tv_address)
    TextView tvAddress;
    @Deprecated(since = "现在使用 PayTypeLayoutV2")
    @Bind(R.id.ptl)
    PayTypeLayout payTypeLayout;
    @Bind(R.id.tv_bill)
    TextView tvBill;
    @Bind(R.id.ll_bill)
    LinearLayout llBill;
    @Bind(R.id.tv_coupon)
    TextView tvCoupon;
    @Bind(R.id.tv_coupon_num_value)
    TextView tvCouponNumValue;
    @Bind(R.id.ll_coupon)
    LinearLayout llCoupon;
    @Bind(R.id.payment_message)
    LinearLayout paymentMessage;
    @Bind(R.id.payment_message_leave_et)
    EditText paymentMessageLeaveEt;
    @Bind(R.id.tv_total)
    TextView tvTotal;
    @Bind(R.id.tv_total_num)
    TextView tvTotalNum;
    @Bind(R.id.ll_order_total)
    LinearLayout llOrderTotal;
    @Bind(R.id.tv_freight)
    TextView tvFreight;
    @Bind(R.id.tv_freight_num)
    TextView tvFreightNum;
    @Bind(R.id.ll_order_freight)
    LinearLayout llOrderFreight;
    @Bind(R.id.tv_order_coupon)
    TextView tvOrderCoupon;
    @Bind(R.id.tv_order_coupon_num)
    TextView tvOrderCouponNum;
    @Bind(R.id.ll_order_coupon)
    LinearLayout llOrderCoupon;
    @Bind(R.id.tv_order_fl)
    TextView tvOrderFl;
    @Bind(R.id.tv_order_fl_num)
    TextView tvOrderFlNum;
    @Bind(R.id.ll_order_fl)
    LinearLayout llOrderFl;
    @Bind(R.id.tv_order_rebate)
    TextView tvOrderRebate;
    @Bind(R.id.tv_order_rebate_num)
    TextView tvOrderRebateNum;
    @Bind(R.id.ll_order_rebate)
    LinearLayout llOrderRebate;
    @Bind(R.id.ll_order_details)
    LinearLayout llOrderDetails;
    @Bind(R.id.tv_pay_amount)
    TextView tvPayAmount;
    @Bind(R.id.btn_ok)
    Button btnOk;
    @Bind(R.id.tv_bill_tips)
    TextView tvBillTips;
    @Bind(R.id.ll_balance)
    LinearLayout mLlBalance;
    @Bind(R.id.tv_balance)
    TextView mTvBalance;
    @Bind(R.id.tv_balance_tips)
    TextView mTvBalanceTips;
    @Bind(R.id.balance_on_off)
    CheckBox mBalanceOnOff;
    @Bind(R.id.tv_order_balance)
    TextView mTvOrderBalance;
    @Bind(R.id.tv_order_balance_num)
    TextView mTvOrderBalanceNum;
    @Bind(R.id.ll_order_balance)
    LinearLayout mLlOrderBalance;
    @Bind(R.id.iv_balance)
    ImageView mIvBalance;
    @Bind(R.id.tv_bill_type)
    TextView mTvBillType;
    @Bind(R.id.tv_pay_balance)
    TextView mTvPayBalance;
    @Bind(R.id.lv_product)
    RecyclerView rvProduct;
    @Bind(R.id.ll_order_one_price)
    LinearLayout llOrderOnePrice;
    @Bind(R.id.tv_order_one_price_num)
    TextView tvOrderOnePriceNum;
    @Bind(R.id.btn_reject)
    Button btn_reject;
    @Bind(R.id.tv_freight_bottom_des)
    TextView tvFreightBottomDes;

    @Bind(R.id.layout_aptitude_tip)
    ConstraintLayout layoutAptitudeTip;
    @Bind(R.id.tv_aptitude_tip)
    TextView tvAptitudeTip;
    /**********购物金**********/
    @Bind(R.id.cl_shopping_gold)
    ConstraintLayout clShoppingGold;
    @Bind(R.id.cl_shopping_gold_tips)
    ConstraintLayout clShoppingGoldTips;
    @Bind(R.id.iv_shopping_gold_tips)
    ImageView ivShoppingGoldTips;
    @Bind(R.id.tv_shopping_gold_tips)
    TextView tvShoppingGoldTips;
    @Bind(R.id.tv_virtual_money)
    TextView tvVirtualMoney;
    @Bind(R.id.tv_virtual_money_tips)
    TextView tvVirtualMoneyTips;
    @Bind(R.id.rv_shopping_gold)
    RecyclerView rvShoppingGold;
    @Bind(R.id.cl_shopping_gold_detail)
    ConstraintLayout clShoppingGoldDetail;
    @Bind(R.id.tv_shopping_gold_detail)
    TextView tvShoppingGoldDetail;
    @Bind(R.id.iv_shopping_gold_detail)
    ImageView ivShoppingGoldDetail;
    @Bind(R.id.cb_virtual_money_on_off)
    CheckBox cbVirtualMoneyOnOff;
    @Bind(R.id.ll_order_virtual_money)
    LinearLayout llOrderVirtualMoney;
    @Bind(R.id.ll_order_virtual_money_recharge)
    LinearLayout llOrderVirtualMoneyRecharge;
    @Bind(R.id.tv_order_virtual_money_num)
    TextView tvOrderVirtualMoneyNum;
    @Bind(R.id.tv_order_virtual_money_recharge_num)
    TextView tvOrderVirtualMoneyRechargeNum;
    /**********购物金**********/
    /**********红包**********/
    @Bind(R.id.ll_red_envelope)
    LinearLayout llRedEnvelope;
    @Bind(R.id.tv_red_envelope)
    TextView tvRedEnvelope;
    @Bind(R.id.tv_red_envelope_tips)
    TextView tvRedEnvelopeTips;
    @Bind(R.id.iv_red_envelope)
    ImageView ivRedEnvelope;
    @Bind(R.id.cb_red_envelope_on_off)
    CheckBox cbRedEnvelopeOnOff;
    @Bind(R.id.ll_order_red_envelope)
    LinearLayout llOrderRedEnvelope;
    @Bind(R.id.tv_order_red_envelope_num)
    TextView tvOrderRedEnvelopeNum;
    /**********红包**********/

    /**********支付优惠**********/
    @Bind(R.id.ll_order_pay_discount)
    LinearLayout llOrderPayDiscount;
    @Bind(R.id.tv_order_pay_discount)
    TextView tvOrderPayDiscount;
    @Bind(R.id.tv_order_pay_discount_num)
    TextView tvOrderPayDiscountNum;
    /**********支付优惠**********/
    //随心拼
    @Bind(R.id.spell_group_recommend_goods)
    PaymentSpellGroupRecommendGoodsView paymentSpellGroupRecommendGoodsView;
    //顺手买
    @Bind(R.id.spell_group_without_goods)
    PaymentBuyWithoutGoodsView spellGroupWithoutGoodsView;
    @Bind(R.id.payType)
    PayTypeLayoutV2 payTypeV2;
    @Bind(R.id.bottomView)
    PaymentBottomPayItemShowView bottomView;
    @Bind(R.id.nsvPayment)
    NestedScrollView nsvPayment;

    private static int REQUESTCODE = 1;//跳转请求码
    private static int REQUESTCODE_TO_AVAILABLE_VOUCHER = 2;//跳转请求码 跳转到选择优惠券
    public static int REQUESTCODE_TO_FREIGHT_ADD_ON_ITEM = 100;//跳转请求码 跳转到运费包邮凑单页
    private int payType = 1;//1 在线支付 2货到付款 3 线下转账
    private int billType = 0;//useBalance 1普通 2//专用
    private int maxCount;//查询订单次数
    private int stepTime;//查询订单间隔
    private int jumpType; //跳转类型 1 只有自营跳支付 2 包含第三方订单跳订单列表,3-调支付接口
    private String peerType = "";
    private String tranNo;
    private String bizProducts; // 组合购、加价购 下单、提单需要传
    private String shoppingCartImgUUID;
    private String mVoucherIds = "";
    private String giftId = "";
    private String skuId;
    private String productNum;
    private String isPgby = null;
    private String skus;//商品id:小计,商品id:小计,
    private String xyyMoneyForVoucherCheck;//用来校验自营叠加券的自营店铺金额
    private PaymentOrderBean order;
    private RequestParams params;
    private AgentOrderListRowBean agentOrderBean;//代下单bean，保存相关数据
    protected AddressListBean address;
    protected PaymentSettleBean settleBean;
    InvoiceinformPopWindow invoiceinformPop;
    private List<PaymentItemBean> newList = new ArrayList<>();
    List<RefundProductListBean> RefundProductList = new ArrayList<>();
    private PaymentNewAdapter mAdapter;

    protected boolean getNewAddress = false;
    private boolean useBalance = true; // 是否使用余额 true是 ，false否
    private boolean mIsVoucher = false;
    // 是否有可使用红包提醒之后 页面数据的 刷新
    private boolean isRefreshAfterCouponNotification = false;
    private boolean isFirstRefreshAfterCouponNotification = false;
    private boolean isExpanded;
    private float virtualGold;
    private float redEnvelopeGold;
    private boolean isShowRedEnvelope = false;
    private boolean isShowVirtualGold = false;

    public static final int SHOW_REJECT_YES = 1; //显示驳回按钮

    private String offlineMsg;// 线下支付多商户
    private String notSubmitOrderOrgIds;
    private SpellGroupRecommendGoodsViewModel mViewModel;
    private PaymentGoodsViewModel paymentGoodsViewModel;
    private PaymentNewsBean mPaymentNewBean;
    private SpellGroupRecommendGoodsBean currSpellGroupRecommendGoodsBean;
    //是否来自购物车
    private String isFromCart = "0";
    //是否来自列表
    private boolean isFromList = false;
    //是否来自商详
    private boolean isFromProductDetail = false;

    private String shopCode;
    private PayWayV2ViewModel payWayV2ViewModel;

    private List<PayWayBean> mPayWayBeans;

    private String mPayCode;
    private String mPayId;

    private String jgEntrance = "";
    private String jgActivityEntrance = "";
    private String direct = "0";

    private int mPayItemType;
    //临时的payCode,使用后会重置
    private String mTempPayCode;

    // 调用getData()是否传递cardId
    private boolean isPassCardId = true;
    private boolean isFirstBuySome = true;
    private SpellGroupRecommendGoodsBean mSpellGroupRecommendGoodsBean;
    private SpellGroupRecommendGoodsBean mSpellGroupWithoutGoodsBean;
    private String shopInfoSxp;
    //type=1 代表随心拼，type=2 代表顺手买
    private int type;
    private String isSupportOldSxp = "";
    //顺手买数据接口入参
    private ArrayList<ShopInfoSxpList> shopInfoSxpList = new ArrayList<>();

    private String mShoppingGoldTips = "";

    private BuySomethingUpdataBean buySomethingUpdataBean;
    //public static final int SHOW_REJECT_NO = 2; //不显示驳回按钮

    private ShoppingGoldRechargeAdapter shoppingGoldRechargeAdapter;

    //购物金checkBox状态变化时 是否需要回调
    private boolean mNeedShoppingGoldCbCallBack = true;
    //红包checkBox状态变化时 是否需要回调
    private boolean mNeedRedEnvelopeCbCallBack = true;
    private String offlinePaySelectedTips = null;
    private boolean isShowBottomSuixinpin = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReceiver();
        setLeft(v -> RoutersUtils.open(mViewModel.getFinishRouter(SpellGroupRecommendGoodsViewModelKt.CURRENT_PAGE_PAYMENT, new HashMap<>())));
    }

    @Override
    protected void initData() {
        super.initData();
        mViewModel = new ViewModelProvider(GlobalViewModelStore.Companion.get().getGlobalViewModelStore(), new SavedStateViewModelFactory(
                getApplication(),
                PaymentActivity.this
        )).get(SpellGroupRecommendGoodsViewModel.class);
        paymentGoodsViewModel = new ViewModelProvider(GlobalViewModelStore.Companion.get().getGlobalViewModelStore(), new SavedStateViewModelFactory(
                getApplication(),
                PaymentActivity.this
        )).get(PaymentGoodsViewModel.class);
        payWayV2ViewModel = new ViewModelProvider(this).get(PayWayV2ViewModel.class);
        setTitle("待确认订单");
        tranNo = getIntent().getStringExtra("tranNo");
        bizProducts = getIntent().getStringExtra(IntentCanst.COMBINED_PRODUCTS);
        payWayV2ViewModel.setTranNo(tranNo);
        shopCode = getIntent().getStringExtra("shopCode");
        agentOrderBean = getIntent().getParcelableExtra("agentOrderBean");
        skuId = getIntent().getStringExtra("skuId");
        productNum = getIntent().getStringExtra("productNum");
        isPgby = getIntent().getStringExtra("isPgby");
        notSubmitOrderOrgIds = getIntent().getStringExtra("notSubmitOrderOrgIds");
        notSubmitOrderOrgIds = getIntent().getStringExtra("notSubmitOrderOrgIds");
        isFromCart = getIntent().getStringExtra("isFromCart");
        isFromList = Objects.equals(getIntent().getStringExtra("isFromList"), "1");
        isFromProductDetail = Objects.equals(getIntent().getStringExtra("isFromProductDetail"), "1");
        jgEntrance = getIntent().getStringExtra(IntentCanst.JG_ENTRANCE);
        jgActivityEntrance = getIntent().getStringExtra(IntentCanst.ACTIVITY_ENTRANCE);
        shopInfoSxp = getIntent().getStringExtra("shopInfoSxpList");
        isSupportOldSxp = getIntent().getStringExtra("isSupportOldSxp");

        if (getIsFromCart()) {
            direct = "3";
        }else if (isFromList){
            direct = "1";
        }else if(isFromProductDetail){
            direct = "2";
        }

        getData(billType, 0, mVoucherIds, true);//第一次 paytype = 0;
        initListener();
        payTypeLayout.setListener((view, bean, position) -> {
            if (bean.payType == 3) {
                if (cbVirtualMoneyOnOff.isChecked() && cbVirtualMoneyOnOff.isEnabled() && virtualGold != 0) {
                    ToastUtils.showLong("线下转账暂不支持购物金抵扣，订单金额已发生变化，请确认");
                }
            }
            payType = bean.payType;
            getData(billType, payType, mVoucherIds, useBalance);
        });

        mAdapter = new PaymentNewAdapter(newList, paymentGoodsViewModel);
        mAdapter.setAgentOrderBean(agentOrderBean);
        mAdapter.setGiftSelect(group -> {
            if (group != null && group.isCanGoToGiftPool() && group.getGiftPoolActTotalSelectedNum()>0){
                String promoId = "";
                int bizSource =0;
                if (mPaymentNewBean != null && mPaymentNewBean.getCart() != null){
                    bizSource = mPaymentNewBean.getCart().getBizSource();
                }
                if (group.getPromoId() != null){
                    promoId = group.getPromoId();
                }
                GiftSelectBottomDialog giftSelectBottomDialog = new GiftSelectBottomDialog(this,group.getGiftPoolActTotalSelectedNum(),promoId,bizSource);
                giftSelectBottomDialog.setConfirmClickCallBack(() -> {
                    getData(billType, 0, mVoucherIds, true);//第一次 paytype = 0;  全部刷新
                    return null;
                });
                giftSelectBottomDialog.setCloseCallBack(() -> {
                    getData(billType, 0, mVoucherIds, true);//第一次 paytype = 0;  全部刷新
                    return null;
                });
                giftSelectBottomDialog.show();
            }
        });
        rvProduct.setNestedScrollingEnabled(false);
        rvProduct.setAdapter(mAdapter);
        rvProduct.setLayoutManager(new WrapLinearLayoutManager(getMySelf()));
        rvProduct.setEnabled(false);
        ((SimpleItemAnimator) rvProduct.getItemAnimator()).setSupportsChangeAnimations(false);
        mAdapter.setOnItemClickListener(rows -> this.isExpanded = rows.isExpanded());
        if (isKaUser) {//ka用户 隐藏优惠券 余额 运费
            mLlBalance.setVisibility(View.GONE);
            llOrderFreight.setVisibility(View.GONE);
            llOrderCoupon.setVisibility(View.GONE);
            mLlOrderBalance.setVisibility(View.GONE);
        }
        int expandClickRange = ConvertUtils.dp2px(3);
        AptitudeTipsUtils.Companion.initAptitudeOverdueTip(this, layoutAptitudeTip, tvAptitudeTip, XyyIoUtil.PAGE_CONFIRMEDORDER);
        initObserver();
    }

    private boolean getIsFromCart() {
        if (TextUtils.isEmpty(isFromCart)) {
            return false;
        } else {
            return isFromCart.equals("1");
        }
    }

    private boolean getIsSupportOldSxp() {
        if (TextUtils.isEmpty(isSupportOldSxp)) {
            return false;
        } else {
            return isSupportOldSxp.equals("1");
        }
    }
    /**
     * livedata添加监听
     */
    private void initObserver() {
        mViewModel.getSpellGroupRecommendGoodsLiveData().observe(this, new Observer<SpellGroupRecommendGoodsBean>() {
            @Override
            public void onChanged(SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean) {
//                if (currSpellGroupRecommendGoodsBean == spellGroupRecommendGoodsBean) {
//                    return;
//                }
                if (spellGroupRecommendGoodsBean.isUpdateData()) {
                    setShoppingGoldCheckedWithoutCallback(false);
                    setMCurrentVirtualGoldRechargeBean(null);
                    if (!isShowBottomSuixinpin) {
                        getData(billType, payType, mVoucherIds, useBalance);
                    } else {
                        dismissProgress();
                    }
                } else {
                    currSpellGroupRecommendGoodsBean = spellGroupRecommendGoodsBean;
                    if (!isShowBottomSuixinpin) {
                        paymentSpellGroupRecommendGoodsView.setData(spellGroupRecommendGoodsBean);
                    } else {
                        dismissProgress();
                    }
                    mSpellGroupRecommendGoodsBean = spellGroupRecommendGoodsBean;
                }
            }
        });
        mViewModel.getSpellGroupWithoutGoodsLiveData().observe(this, new Observer<SpellGroupRecommendGoodsBean>() {
            @Override
            public void onChanged(SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean) {
//                if (currSpellGroupRecommendGoodsBean == spellGroupRecommendGoodsBean) {
//                    return;
//                }
                if (spellGroupRecommendGoodsBean.getGoodsIdMapping().isEmpty()){
                    return;
                }
                if (spellGroupRecommendGoodsBean.isUpdateData()) {
                    setShoppingGoldCheckedWithoutCallback(false);
                    setMCurrentVirtualGoldRechargeBean(null);
                    if (!isShowBottomSuixinpin) {
                        getData(billType, payType, mVoucherIds, useBalance);
                    } else {
                        dismissProgress();
                    }
                } else {
                    if (!isShowBottomSuixinpin) {
                        spellGroupWithoutGoodsView.setData(spellGroupRecommendGoodsBean);
                    } else {
                        dismissProgress();
                    }
                    mSpellGroupWithoutGoodsBean = spellGroupRecommendGoodsBean;
                }
            }
        });
        mViewModel.getChangeCartForPromotionBeanLiveData().observe(this, new Observer<BaseBean<CartDataBean>>() {
            @Override
            public void onChanged(BaseBean<CartDataBean> dataBean) {
                dismissProgress();
                if (dataBean.isSuccess()){
                    spellGroupWithoutGoodsView.upCartData(dataBean.getData());
                }
            }
        });
        payWayV2ViewModel.getConvertPayTypeLiveData().observe(this, payWayBeans -> {
            mPayWayBeans = payWayBeans;
            payTypeV2.setData(payWayBeans, payWayV2ViewModel);
            if (mPayWayBeans != null) {
                for (PayWayBean payWayBean : mPayWayBeans) {
                    if (!TextUtils.isEmpty(payWayBean.unpcaTips) && payWayBean.checked) {
                        AlertDialogEx dialogEx = new AlertDialogEx(this);
                        dialogEx.setMessage(payWayBean.unpcaTips)
                                .setCancelButton("确定", new AlertDialogEx.OnClickListener() {
                                    @Override
                                    public void onClick(AlertDialogEx dialog, int button) {
                                        dialog.dismiss();
                                    }
                                })
                                .setCancelable(false)
                                .setCanceledOnTouchOutside(false)
                                .setTitle(null)
                                .show();
                    }
                }
            }
            mPayWayV2Adapter = payTypeV2.getPayWayV2Adapter();
            payTypeV2.setPayTypeClickCallback((selectedPayType, s2, payTypeTitleTips) -> {

                if (Integer.parseInt(selectedPayType) == 3) {
                    if (cbVirtualMoneyOnOff.isChecked() && cbVirtualMoneyOnOff.isEnabled() && virtualGold != 0) {
                        ToastUtils.showLong("线下转账暂不支持购物金抵扣，订单金额已发生变化，请确认");
                    }
                }
                setMCurrentVirtualGoldRechargeBean(null); //清空购物金充值项记录
                payType = Integer.parseInt(selectedPayType);
                getData(billType, payType, mVoucherIds, useBalance);
                if (TextUtils.equals(selectedPayType, PayWayBean.PAY_TYPE_ONLINE+"")) return null;
                bottomView.setData(new PaymentBottomPayItemShowView.PayItemShowBean(
                        null, "", s2, payTypeTitleTips
                ));
                return null;
            });
            payTypeV2.setChangeCardCallback(() -> {
                BankCardPopWindow bankCardPopWindow = new BankCardPopWindow(PaymentActivity.this, payWayV2ViewModel.getBankCards(), payWayV2ViewModel);
                bankCardPopWindow.setPopWindowSelectCardCallback(s -> {
//                    payWayV2ViewModel.setSelectedPayType(s);
                    payWayV2ViewModel.switchBankCard(s);
                    return null;
                });
                bankCardPopWindow.show(payTypeV2);
                return null;
            });
            payTypeV2.setPayWayClickCallback((payCode, payId, itemType) -> {
                if (payCode.equals(PAY_TYPE_NONG)) {
                    showProgress();
                    if(getMOrderNo()!=null){
                        payWayV2ViewModel.checkAbChinaLoan(getMOrderNo());
                    }else{
                        payWayV2ViewModel.checkAbChinaLoan("");
                    }
                } else {
                    mPayCode = payCode;
                    mPayId = payId;
                    mPayItemType = itemType;
                    getData(billType, payType, mVoucherIds, useBalance);
                }
                return null;
            });
            payTypeV2.setShowTipsPopCallback(callBack -> {
                if (!TextUtils.isEmpty(offlinePaySelectedTips)) {
                    PopShowUtilKt.showPayTipsPop(PaymentActivity.this, offlinePaySelectedTips, "我已了解风险，仍使用线下转账支付", true, () -> {
                        callBack.invoke();
                        return null;
                    });
                }else {
                    callBack.invoke();
                }
                return null;
            });
        });

        payWayV2ViewModel.getPaymentListBeanLiveData().observe(this, new Observer<PayTypeConfigV2Bean>() {
            @Override
            public void onChanged(PayTypeConfigV2Bean payTypeConfigV2Bean) {
                dismissProgress();
                if (mPayWayV2Adapter == null) {
                    if (payTypeConfigV2Bean.getPayTypeList() == null || payTypeConfigV2Bean.getPayTypeList().isEmpty()) return;
                }
                if (mPayWayBeans != null) {
                    for (PayWayBean payWayBean : mPayWayBeans) {
                        if (payWayBean.payType == PayWayBean.PAY_TYPE_ONLINE
                                && payWayBean.cashier != null) {
                            payWayBean.cashier.payTypeEntryList = payTypeConfigV2Bean.getPayTypeList();
                        }
                    }
                }
                payTypeV2.setData(mPayWayBeans, payWayV2ViewModel);
                mPayWayV2Adapter = payTypeV2.getPayWayV2Adapter();
                mPayCode = payWayV2ViewModel.getMSelectedPayCode();
            }
        });
        mViewModel.getBuySomethingBeanLiveData().observe(this, new Observer<BaseBean<List<PaymentSuiXinPinSkusItemBean>>>() {
            @Override
            public void onChanged(BaseBean<List<PaymentSuiXinPinSkusItemBean>> dataBean) {
                dismissProgress();
                if (dataBean.isSuccess() && dataBean.data != null && !dataBean.data.isEmpty()) {
                    type = 2;
                    spellGroupWithoutGoodsView.setVisibility(View.VISIBLE);
                    PaymentSuiXinPinSkusBean bean = new PaymentSuiXinPinSkusBean(0, 0, 0, "", dataBean.getData());
                    SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = bugSomethingMappingData(bean);
                    if (spellGroupRecommendGoodsBean != null) {
                        mViewModel.updateWithoutData(spellGroupRecommendGoodsBean, false);
                    }
                } else {
                    spellGroupWithoutGoodsView.setVisibility(View.GONE);
                }
            }
        });

        payWayV2ViewModel.getBottomPayItemLiveData().observe(this, payItemShowBean -> bottomView.setData(payItemShowBean));
        payWayV2ViewModel.getNongPayItemLiveData().observe(this, bean -> {
                    dismissProgress();
                    if (bean.isSuccess()) {
                        if (bean.data != null) {
                            if (bean.data.getCheckResult()) {
                                //选中逻辑
                                mPayCode = PAY_TYPE_NONG;
                                mPayId = String.valueOf(PAY_LAYOUT_TYPE_NONG);
                                mPayItemType = PAY_LAYOUT_TYPE_NONG;
                                getData(billType, payType, mVoucherIds, useBalance);
                            } else {
                                switch (bean.data.getCheckStatus()) {
                                    case "1": {
                                        String title = "支付失败";
                                        String contentStr = "农行链e贷已被以下订单锁定待支付，暂不可用。" +
                                                "请额度申请人前往【中国农业银行】App完成支付后再次尝试，或选用其他支付渠道进行支付";
                                        String cancelStr = "使用其他支付渠道";
                                        String confirmStr = "打开农行APP";
                                        String orderNum = bean.data.getOrderNo();
                                        String amount = bean.data.getAmount();
                                        PopShowUtilKt.showPayPop(this, title, contentStr,
                                                orderNum, amount, cancelStr, confirmStr, null, new View.OnClickListener() {
                                                    @Override
                                                    public void onClick(View v) {
                                                        ReqUrlJsonBean reqUrl = new Gson().fromJson(bean.data.getAbchinaLoanAppDirectUrl(), ReqUrlJsonBean.class);
                                                        Intent intent = getPackageManager().getLaunchIntentForPackage(
                                                                reqUrl.getAndroidScheme() != null ? reqUrl.getAndroidScheme() : ""
                                                        );
                                                        if (intent != null) {
                                                            // 如果找到了对应的Intent，则启动该应用
                                                            startActivity(intent);
                                                        } else {
                                                            RoutersUtils.open(reqUrl.getAbchinaDirectUrl());
                                                        }
                                                    }
                                                });
                                        break;
                                    }
                                    case "2": {
                                        String title = "支付失败";
                                        String contentStr = "农行链e贷为您以下订单放款中，暂不可用。请等待放款成功后重试，" +
                                                "或选用其他支付渠道进行支付";
                                        String cancelStr = "使用其他支付渠道";
                                        String confirmStr = "";
                                        String orderNum = bean.data.getOrderNo();
                                        String amount = bean.data.getAmount();
                                        PopShowUtilKt.showPayPop(this, title, contentStr,
                                                orderNum, amount, cancelStr, confirmStr, null, new View.OnClickListener() {
                                                    @Override
                                                    public void onClick(View v) {
                                                        ReqUrlJsonBean reqUrl = new Gson().fromJson(bean.data.getAbchinaLoanAppDirectUrl(), ReqUrlJsonBean.class);
                                                        Intent intent = getPackageManager().getLaunchIntentForPackage(
                                                                reqUrl.getAndroidScheme() != null ? reqUrl.getAndroidScheme() : ""
                                                        );
                                                        if (intent != null) {
                                                            // 如果找到了对应的Intent，则启动该应用
                                                            startActivity(intent);
                                                        } else {
                                                            RoutersUtils.open(reqUrl.getAbchinaDirectUrl());
                                                        }
                                                    }
                                                });
                                        break;
                                    }
                                    default: {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
        );
        setBaseObserver();
    }

    private void initListener() {
        mBalanceOnOff.setOnCheckedChangeListener((compoundButton, isChecked) -> {
            PaymentActivity.this.useBalance = isChecked;
            getData(billType, payType, mVoucherIds, isChecked);
        });

        cbVirtualMoneyOnOff.setOnCheckedChangeListener((compoundButton, isChecked) -> {
            if (mNeedShoppingGoldCbCallBack){
                getData(billType, payType, mVoucherIds, useBalance);
            }
        });
        cbRedEnvelopeOnOff.setOnCheckedChangeListener((compoundButton, isChecked) -> {
            if (mNeedRedEnvelopeCbCallBack){
                getData(billType, payType, mVoucherIds, useBalance);
            }
        });
        paymentSpellGroupRecommendGoodsView.setOnBuyMoreClickListener(view -> {
//            //点击购买更多
//            try {
//                PaymentSuiXinPinSkusBean suiXinPinSkus = mPaymentNewBean.getCart().getCompanys().get(0).suiXinPinSkus;
//                Map<String, String> routerParams = new HashMap<>();
//                if (mPaymentNewBean == null || suiXinPinSkus == null || TextUtils.isEmpty(suiXinPinSkus.getMainShopCode())) return null;
//                // 这里不能清理数量为0的推荐品，下一页面可以直接返回，如果这里清理了，那么当前页列表的推荐品就没了
////                mViewModel.clearRecommendGoods();
//                RoutersUtils.open(mViewModel.getJumpRouter(SpellGroupRecommendGoodsViewModelKt.CURRENT_PAGE_PAYMENT, routerParams));
//            } catch (Exception e) {
//                e.printStackTrace();
//            }

            RecommendSuiXinPinPopWindow recommendSuiXinPinPopWindow = new RecommendSuiXinPinPopWindow(PaymentActivity.this, new Function1<List<RecommendSuiXinPinBean>, Unit>() {
                @Override
                public Unit invoke(List<RecommendSuiXinPinBean> recommendSuiXinPinBeans) {
//                    if (recommendSuiXinPinBeans.isEmpty()) return null;
//                    mViewModel.addSpellGroupRecommendCartForList(recommendSuiXinPinBeans);
                    return null;
                }
            });
            StringBuilder excludeIds = new StringBuilder();
            String mainGoodsPId = mViewModel.getMainGoodsPId();
            excludeIds.append(mainGoodsPId);
            excludeIds.append(",");
            if (mSpellGroupRecommendGoodsBean != null) {
                for (Map.Entry<String, Integer> stringIntegerEntry : mSpellGroupRecommendGoodsBean.getGoodsIdMapping().entrySet()) {
                    if (stringIntegerEntry.getValue() != 0) {
                        excludeIds.append(stringIntegerEntry.getKey());
                        excludeIds.append(",");
                    }
                }
            }
            String excludeIdsStr = excludeIds.substring(0, excludeIds.length() - 1);
            recommendSuiXinPinPopWindow.setData(excludeIdsStr, mViewModel);
            recommendSuiXinPinPopWindow.show(paymentSpellGroupRecommendGoodsView);
            isShowBottomSuixinpin = true;
            recommendSuiXinPinPopWindow.setMPopWindowDismiss(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    getData(billType, payType, mVoucherIds, useBalance);
                    isShowBottomSuixinpin = false;
                    return null;
                }
            });
            return null;
        });
        spellGroupWithoutGoodsView.setOnBuyMoreClickListener(view -> {
            PaymentBuyWithoutPopWindow buyWithoutPopWindow = new PaymentBuyWithoutPopWindow(PaymentActivity.this, new Function1<List<RecommendShunShouMaiBean>, Unit>() {
                @Override
                public Unit invoke(List<RecommendShunShouMaiBean> recommendSuiXinPinBeans) {
                    return null;
                }
            });
            ArrayList<String> buySomethingCasuallySkusList = new ArrayList<>();
            if (mSpellGroupWithoutGoodsBean != null) {
                for (Map.Entry<String, Integer> stringIntegerEntry : mSpellGroupWithoutGoodsBean.getGoodsIdMapping().entrySet()) {
                    if (stringIntegerEntry.getValue() != 0) {
                        buySomethingCasuallySkusList.add(stringIntegerEntry.getKey());
                    }
                }
            }
            if (shopInfoSxpList.isEmpty()) {
                ArrayList<String> skuidsList = new ArrayList<>();
                skuidsList.add(mViewModel.getMainGoodsSkuId());
                shopInfoSxpList.add(new ShopInfoSxpList(TextUtils.isEmpty(shopCode) ? mViewModel.getShopCode() : shopCode, skuidsList));
                buyWithoutPopWindow.setData(buySomethingCasuallySkusList, shopInfoSxpList, mViewModel);
            } else {
                buyWithoutPopWindow.setData(buySomethingCasuallySkusList, shopInfoSxpList, mViewModel);
            }
            buyWithoutPopWindow.show(spellGroupWithoutGoodsView);
            isShowBottomSuixinpin = true;
            buyWithoutPopWindow.setMPopWindowDismiss(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    getData(billType, payType, mVoucherIds, useBalance);
                    isShowBottomSuixinpin = false;
                    return null;
                }
            });
            return null;
        });

        bottomView.setBottomChangePayTypeCallback(view -> {
            int[] location = new int[2];
            payTypeV2.getLocationInWindow(location);
            int offset = ScreenUtils.dip2px(PaymentActivity.this, 44) + ScreenUtils.getStatusBarHeight(PaymentActivity.this);
            int top = location[1];
            if (top == offset) return null;
            nsvPayment.smoothScrollBy(0, top - offset);
            return null;
        });
    }

    /**
     * 去结算订单接口 url = "/orders/settle"
     *
     * @param billType    发票类型
     * @param payType     支付方式
     * @param voucher_ids 优惠卷ids
     * @param useBalance  是否使用余额
     */
    private void getData(final int billType, final int payType, String voucher_ids, final boolean useBalance) {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("version", "1.0"); // 版本控制
        if (payType > 0) {
            params.put("payType", "" + payType);
        }
        params.put("storeStatus", "true"); //storeStatus => 是否使用勾选商品结算
        if (isKaUser) {//ka用户隐藏余额 运费 优惠券
//            params.put("useBalance", "" + false);
        } else {
//            params.put("useBalance", "" + useBalance);
            if (!TextUtils.isEmpty(voucher_ids) || mIsVoucher) {
                if (voucher_ids == null) {
                    voucher_ids = "";
                }
                params.put("voucherIds", voucher_ids);
            }
        }
        if (payType == 3) {
            params.put("useVirtualGold", "false");
        } else {
            params.put("useVirtualGold", String.valueOf(cbVirtualMoneyOnOff.isChecked() && cbVirtualMoneyOnOff.isEnabled()));
            try {
                if (cbVirtualMoneyOnOff.isChecked() && cbVirtualMoneyOnOff.isEnabled() && getMCurrentVirtualGoldRechargeBean() != null) { //在线支付时传
                    params.put("rechargeAmount", String.valueOf(getMCurrentVirtualGoldRechargeBean().getAmount()));
                    if (getMCurrentVirtualGoldRechargeBean().isCanUse()) {
                        params.put("rechargeRedPacket", getMCurrentVirtualGoldRechargeBean().getRightAmount());
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        params.put("useRedPacket", String.valueOf(cbRedEnvelopeOnOff.isChecked()));
        params.put("useBalance", "false");
        if (!TextUtils.isEmpty(tranNo)) {
            params.put("tranNo", tranNo);
        }
        if (billType > 0) {
            params.put("billType", billType + "");//发票类型
        }
        if (!TextUtils.isEmpty(peerType)) {
            params.put("peerType", peerType + "");//随货同行
        }

        if (!TextUtils.isEmpty(giftId)) {
            params.put("giftIds", giftId);

        }

        if (isRefreshAfterCouponNotification) {
            params.put("voucherMonitor", "1");
        }

        //切换地址传递参数
        if (address != null) {
            params.put("addressId", address.id + "");//地址更新
            if (!TextUtils.isEmpty(address.addressType))
                params.put("addressType", address.addressType);//点击某个地址，跳到结算页时，app请求了  去结算接口/settle   这个动作也记得把addressType传给结算接口
        }

        if (isFirstRefreshAfterCouponNotification) {
            params = getRefreshParams();
        }

        if (isFromAgentOrder()) {
            params.put("purchaseNo", agentOrderBean.getPurchaseNo());
        }

        if (isFromSpellGroupOrPgby()) {
            params.put("skuId", skuId);
            params.put("productNum", productNum);

            params.put("direct", "5");
            params.put("sptype", mFlowData.getSpType());
            params.put("spid", mFlowData.getSpId());
            params.put("sid", mFlowData.getSId());
            params.put("isSupportOldSxp", String.valueOf(getIsSupportOldSxp()));

            XyyIoUtil.checkSpTypeField(mFlowData, false);
        }

        if (!TextUtils.isEmpty(notSubmitOrderOrgIds)) {
            params.put("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
        }
        params.put("shopCode", TextUtils.isEmpty(shopCode)?mViewModel.getShopCode():shopCode);
        if (mViewModel != null && !TextUtils.isEmpty(mViewModel.getMainGoodsSkuId())) {
            params.put("skuId", mViewModel.getMainGoodsSkuId());
        }
        if (mViewModel != null && !TextUtils.isEmpty(mViewModel.getMainGoodsPId())) {
            params.put("pid", mViewModel.getMainGoodsPId());
        }

        if (!TextUtils.isEmpty(mTempPayCode)) {
            params.put("payChannelCode", mTempPayCode);
            mTempPayCode = "";
        } else if (!TextUtils.isEmpty(mPayCode) && payType == 1) {
            params.put("payChannelCode", mPayCode);
        }
        if (isPassCardId) {
            String cardId = payWayV2ViewModel.getShowBankCardId();
            if (mPayItemType == PAY_LAYOUT_TYPE_BANK_CARD && mPayId != null) {
                params.put("cardId", mPayId);
                mPayId = null;
            } else if (!TextUtils.isEmpty(cardId)) {
                params.put("cardId", cardId);
            }
        }
        if (TextUtils.isEmpty(mPayId)) {
            mPayId = payWayV2ViewModel.getPayId();
        }
        if (!TextUtils.isEmpty(mPayId) && (TextUtils.equals(mPayId, VIRTUAL_PAY_ID_ADD_BANK_CARD) || TextUtils.equals(mPayId, VIRTUAL_PAY_ID_RECOMMEND_BANK_CARD))) {
            params.put("virtualCardId", mPayId);
        }
        isPassCardId = true;
        //添加随心拼数据
        suiXinPinAddParams(params, true);

        if (!TextUtils.isEmpty(isPgby)) {
            params.put("isPiGou", "1");
        }

        JgRequestParams jgRequestParams = new JgRequestParams();
        if (jgEntrance != null && !jgEntrance.isEmpty()) {
//            params.put("entrance", jgEntrance);
            jgRequestParams.setEntrance(jgEntrance);
        }
        if (jgActivityEntrance != null && !jgActivityEntrance.isEmpty()) {
//            params.put("activityEntrance", jgActivityEntrance);
            jgRequestParams.setActivity_entrance(jgActivityEntrance);
        }
        if (JGTrackManager.Companion.getSuperProperty(this, JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null) {
            String searchSortStrategyCode = (String) JGTrackManager.Companion.getSuperProperty(this, JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID);
//            params.put("searchSortStrategyCode",searchSortStrategyCode);
            jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null) {
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId() != null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), skuId)) {
                if (mJgOperationInfo.getOperationId() != null) {
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null) {
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null) {
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(skuId))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);
                jgRequestParams.setDirect(direct);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        jgRequestParams.setSession_id(TrackManager.getSessionId(YBMAppLike.getAppContext()));
        params.put("mddata", new Gson().toJson(jgRequestParams));

        if (jgEntrance != null && jgEntrance.contains(JGTrackManager.TrackShoppingCart.TITLE)) { //购物车只传个direct = "3"
            params.getParamsMap().remove("mddata");
        }
        // 组合购、品团购传组合商品列表
        if(!TextUtils.isEmpty(bizProducts)){
            params.put("bizProducts",bizProducts);
        }
        String url = isFromAgentOrder() ? AppNetConfig.order_sprout_v2_settle : (isFromSpellGroupOrPgby() ? AppNetConfig.ORDER_V1_GROUPPURCHASESETTLE : AppNetConfig.ORDERS_V1_SETTLE);
        HttpManager.getInstance().post(url, params, new BaseResponse<PaymentNewsBean>() {
            @Override
            public void onSuccess(String content, BaseBean<PaymentNewsBean> data, PaymentNewsBean paymentNewBean) {
                dismissProgress();
                if (data != null) {
                    if (!data.isSuccess()) {
                        if (data.code == 9999) {
                            //自有账期请求失败
                            try {
                                payTypeLayout.setData(mPaymentNewBean.getPayType().getPayWayList());
                                for (PayWayBean payWayBean : mPaymentNewBean.getPayType().getPayWayList()) {
                                    if (payWayBean.checked) {
                                        PaymentActivity.this.payType = payWayBean.payType;
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if (data.isSuccess()) {
                        mPaymentNewBean = paymentNewBean;
                        handlePayTypeByRemoteData(paymentNewBean);
                        setVirtualMoney(paymentNewBean);
                        setRedEnvelope(paymentNewBean);
                        setPayDiscount(paymentNewBean);
                        if (paymentNewBean != null) {
                            payWayV2ViewModel.convertPaymentList(paymentNewBean.getPayType());
                            if (paymentNewBean.getPayType() != null) {
                                offlinePaySelectedTips= paymentNewBean.getPayType().getOfflinePaySelectedTips();
                                setPayTypeLayout(paymentNewBean.getPayType());
                                offlineMsg = paymentNewBean.getPayType().offlinePayTips;
                            }
                            if (paymentNewBean.getBase() != null) {
                                shoppingCartImgUUID = paymentNewBean.getBase().getShoppingCartImgUUID();
                                btn_reject.setVisibility(paymentNewBean.getBase().getIsShowOrderSprout() == SHOW_REJECT_YES ? View.VISIBLE : View.GONE);
                            }
                            if (paymentNewBean.getAddress() != null) {
                                address = paymentNewBean.getAddress();
                                showAddressInfo(address);
                            }
                            if (paymentNewBean.getBalance() != null) {
                                setTips(paymentNewBean.getBalance());
                            }
                            if (paymentNewBean.getBillType() != null) {
                                showBill(paymentNewBean.getBillType());
                            }
                            if (paymentNewBean.getSettle() != null) {
                                settleBean = paymentNewBean.getSettle();
                            }
                            if (paymentNewBean.getCart() != null) {
                                //设置选择优惠券需要的参数
                                setSelectCouponParams(paymentNewBean.getCart());
                                setPaymentList2Data(paymentNewBean.getCart());
                            }
                            if (getIsFromCart() && isFirstBuySome) {
                                ArrayList<String> integers = new ArrayList<>();
                                if (!TextUtils.isEmpty(shopInfoSxp)) {
                                    try {
                                        shopInfoSxpList.addAll(new Gson().fromJson(shopInfoSxp, new TypeToken<ArrayList<ShopInfoSxpList>>() {
                                        }.getType()));
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                BuySomethingCasuallyInfo buySomethingCasuallyInfo = new BuySomethingCasuallyInfo(shopInfoSxpList, integers, false);
                                String json = new Gson().toJson(buySomethingCasuallyInfo);
                                isFirstBuySome = false;
                                mViewModel.getBuySomethingData(json);
                            }
                            //随心拼
                            try {
//                                ArrayList<PaymentSuiXinPinSkusBean> paymentSuiXinPinSkusBeans = new ArrayList<>();
//                                PaymentSuiXinPinSkusBean suiXinPinSkus = paymentNewBean.getCart().getCompanys().get(0).suiXinPinSkus;
//                                for (PaymentCompanyBean itemsBean : paymentNewBean.getCart().getCompanys()) {
//                                    if (itemsBean.suiXinPinSkus != null) {
//                                        paymentSuiXinPinSkusBeans.add(itemsBean.suiXinPinSkus);
//                                    }
//                                }
                                PaymentSuiXinPins paymentSuiXinPins = paymentNewBean.getCart().suiXinPinSkus;
                                if (paymentSuiXinPins != null && !TextUtils.isEmpty(paymentSuiXinPins.getInValidMsg())) {
                                    ToastUtils.showShort(paymentSuiXinPins.getInValidMsg());
                                }
//                                if (suiXinPinSkus != null && suiXinPinSkus.getItems() != null && !suiXinPinSkus.getItems().isEmpty()) {
                                if (paymentSuiXinPins != null && paymentSuiXinPins.getItems() != null && !paymentSuiXinPins.getItems().isEmpty()) {
                                    if (paymentSuiXinPins.getItems() != null
                                            && paymentSuiXinPins.getItems().get(0) != null
                                            && paymentSuiXinPins.getItems().get(0).getType() != null
                                            && paymentSuiXinPins.getItems().get(0).getType() == 1) {
                                        type = 1;
                                        paymentSpellGroupRecommendGoodsView.setVisibility(View.VISIBLE);
                                        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = suiXinPinMappingData(paymentSuiXinPins);
                                        mViewModel.updateData(spellGroupRecommendGoodsBean, false);
                                    } else if (paymentSuiXinPins.getItems() != null
                                            && paymentSuiXinPins.getItems().get(0) != null
                                            && paymentSuiXinPins.getItems().get(0).getType() != null
                                            && paymentSuiXinPins.getItems().get(0).getType() == 2) {
                                        //顺手买
                                        type = 2;
                                        spellGroupWithoutGoodsView.setVisibility(View.VISIBLE);
                                        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = shunShouMaiMappingData(paymentSuiXinPins);
                                        if (spellGroupRecommendGoodsBean != null) {
                                            mViewModel.updateWithoutData(spellGroupRecommendGoodsBean, false);
                                        }
                                    }
                                } else {
                                    paymentSpellGroupRecommendGoodsView.setVisibility(View.GONE);
                                    spellGroupWithoutGoodsView.setVisibility(View.GONE);
                                    //清空viewmodle总的共享商品数据
                                    SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = mViewModel.getSpellGroupRecommendGoodsLiveData().getValue();
//                                    SpellGroupRecommendGoodsBean withoutGoodsBean = mViewModel.getSpellGroupWithoutGoodsLiveData().getValue();
                                    if (spellGroupRecommendGoodsBean != null) {
                                        if (!spellGroupRecommendGoodsBean.getRowsBean().isEmpty()) {
                                            spellGroupRecommendGoodsBean.getRowsBean().clear();
                                        }
                                        mViewModel.updateData(spellGroupRecommendGoodsBean, false);
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            setOrderDetails(paymentNewBean);
                        }
                        // 如果是可用优惠券提示，第一次刷新后，跳转到优惠券选择页面。
                        if (isFirstRefreshAfterCouponNotification) {
                            isFirstRefreshAfterCouponNotification = false;
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    /**
     * 根据服务端的数据设置当前支付方式的payType
     * @param mPaymentNewBean
     */
    private void handlePayTypeByRemoteData(PaymentNewsBean mPaymentNewBean) {
        try {
            for (PayWayBean payWayBean : mPaymentNewBean.getPayType().getPayWayList()) {
                if (payWayBean.checked) {
                    payType = payWayBean.payType;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加随心拼或顺手买请求数据
     * @param params
     * @param containEmpty 是否添加数量为0的商品
     */
    private void suiXinPinAddParams(RequestParams params, boolean containEmpty) {
        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = null;
        if (type == 1) {
            spellGroupRecommendGoodsBean = mViewModel.getSpellGroupRecommendGoodsLiveData().getValue();
        } else if (type == 2) {
            spellGroupRecommendGoodsBean = mViewModel.getSpellGroupWithoutGoodsLiveData().getValue();
        }
        if (spellGroupRecommendGoodsBean != null
                && !spellGroupRecommendGoodsBean.getRowsBean().isEmpty()) {
            List<SpellGroupGoodsItem> itemList = spellGroupRecommendGoodsBean.getRowsBean();
            List<SpellGroupGoodsItem> desItemList = new ArrayList<>();
            if (isShowBottomSuixinpin) {
                for (SpellGroupGoodsItem spellGroupGoodsItem : itemList) {
                    if (spellGroupRecommendGoodsBean.getGoodsIdMapping().get(spellGroupGoodsItem.getSkuId())!=0) {
                        desItemList.add(spellGroupGoodsItem);
                    }
                }

            } else {
                desItemList = itemList;
            }
            JSONArray arr = new JSONArray();
            for (SpellGroupGoodsItem spellGroupGoodsItem : desItemList) {
                try {
                    if (spellGroupGoodsItem == null || TextUtils.isEmpty(spellGroupGoodsItem.getSkuId())) continue;
                    int selectedGoodsAmount = spellGroupRecommendGoodsBean.getGoodsIdMapping().get(spellGroupGoodsItem.getSkuId());
                    if (!containEmpty && selectedGoodsAmount == 0 && desItemList.size() > 1) {
                        continue;
                    }
                    JSONObject obj = new JSONObject();
                    obj.put("skuId", spellGroupGoodsItem.getSkuId());
                    obj.put("quantity", selectedGoodsAmount);
                    obj.put("type", type);
                    if (spellGroupGoodsItem.getQtData() != null) {
                        try {
                            PaymentSuiXinPinQtData qtData = spellGroupGoodsItem.getQtData();
                            JSONObject jo = new JSONObject();
                            jo.put("scmId", qtData.getScmId());
                            jo.put("expId", qtData.getExpId());
                            jo.put("rank", qtData.getRank());
                            jo.put("qtListData", qtData.getQtListData());
                            jo.put("qtSkuData", qtData.getQtSkuData());
                            obj.put("qtData", jo);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if (containEmpty) {
                        Integer skuStartNum = 1;
                        if (null != spellGroupGoodsItem.getActPt()) {
                            skuStartNum = spellGroupGoodsItem.getActPt().getSkuStartNum();
                        } else if (null != spellGroupGoodsItem.getActPgby()) {
                            skuStartNum = spellGroupGoodsItem.getActPgby().getSkuStartNum();
                        }
                        obj.put("skuStartNum", skuStartNum);
                        obj.put("showName", spellGroupGoodsItem.getGoodsTitle());
                        if (spellGroupGoodsItem.getTagList() != null && spellGroupGoodsItem.getTagList().size() > 0) {
                            TagBean tagBean = spellGroupGoodsItem.getTagList().get(0);
                            String json = new Gson().toJson(tagBean);
                            obj.put("tag", json);
                        }
                    }
//                    obj.put("pid", mViewModel.getMainGoodsPId());
                    if (selectedGoodsAmount > -1) { //弱网可能减到-1 导致接口错误  这里过滤掉
                        arr.put(obj);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            params.put("suiXinPinSkus", arr.toString());
        }
    }

    /**
     * 随心拼数据转换
     * @param suiXinPinSkus 返回的数据
     * @return
     */
    private SpellGroupRecommendGoodsBean suiXinPinMappingData(PaymentSuiXinPins suiXinPinSkus) {
        if (suiXinPinSkus == null || suiXinPinSkus.getItems() == null || suiXinPinSkus.getItems().isEmpty()) return null;
        List<SpellGroupGoodsItem> goodsItems = new ArrayList<>();
        HashMap<String, Integer> mapping = new HashMap<>();
        int goodsCategoriesCount = 0;
        int goodsTotalCount = 0;
        double totalPrice = 0;
        for (PaymentSuiXinPinSkusItemBean bean : suiXinPinSkus.getItems()) {
            SpellGroupGoodsItem item = new SpellGroupGoodsItem(bean.getImageUrl(), bean.getMarkerUrl(), bean.getShowName(), bean.getPrice(), bean.getProductUnit(), bean.getFob(), bean.getQty(), bean.getSkuId(), bean.getMediumPackageNum(), bean.isSplit(), 0, "0", bean.getSpid(), bean.getSptype(), bean.getSid(), bean.getSourceType(), bean.getNearEffect(), bean.getTagList(), bean.getActPt(), bean.getActPgby());
            item.setQtData(bean.getQtData());
            if (bean.getSkuId() == null) bean.setSkuId("");
            mapping.put(bean.getSkuId(), bean.getQty());
            goodsItems.add(item);
        }
        goodsCategoriesCount += suiXinPinSkus.getVarietyNum();
        goodsTotalCount += suiXinPinSkus.getTotalNum();
        totalPrice += suiXinPinSkus.getTotalAmount();
        CartGoodsInfo  cartGoodsInfo = new CartGoodsInfo(goodsCategoriesCount, goodsTotalCount, totalPrice + "");
        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = mViewModel.getSpellGroupRecommendGoodsLiveData().getValue();
        SpellGroupGoodsItem mainRowsBean = null;
        if (spellGroupRecommendGoodsBean != null) {
            mainRowsBean = spellGroupRecommendGoodsBean.getMainRowsBean();
        }
        return new SpellGroupRecommendGoodsBean(goodsItems, mainRowsBean, mapping, cartGoodsInfo, false);
    }

    /**
     * 顺手买数据转换  settle 接口返回 和 order/v1/buySomethingCasuallyQuery 接口返回的数据结构不一样
     *
     * @param suiXinPinSkus 返回的数据
     * @return
     */
    private SpellGroupRecommendGoodsBean shunShouMaiMappingData(PaymentSuiXinPins suiXinPinSkus) {
        if (suiXinPinSkus == null || suiXinPinSkus.getItems() == null || suiXinPinSkus.getItems().isEmpty()) return null;
        List<SpellGroupGoodsItem> goodsItems = new ArrayList<>();
        HashMap<String, Integer> mapping = new HashMap<>();
        int goodsCategoriesCount = 0;
        int goodsTotalCount = 0;
        double totalPrice = 0;
        for (PaymentSuiXinPinSkusItemBean bean : suiXinPinSkus.getItems()) {
            String price = "";
            if (bean.getPrice() == null || bean.getPrice().isEmpty()) {
                price = bean.getFob();
            } else {
                price = bean.getPrice();
            }
            SpellGroupGoodsItem item = new SpellGroupGoodsItem(bean.getImageUrl(), bean.getMarkerUrl(), bean.getShowName(), price, bean.getProductUnit(), bean.getFob(), bean.getQty(), bean.getSkuId(), bean.getMediumPackageNum(), bean.isSplit(), 0, "0", bean.getSpid(), bean.getSptype(), bean.getSid(), bean.getSourceType(), bean.getNearEffect(), bean.getTagList(), bean.getActPt(), bean.getActPgby());
            item.setQtData(bean.getQtData());
            if (bean.getSkuId() == null) bean.setSkuId("");
            mapping.put(bean.getSkuId(), bean.getQty());
            goodsItems.add(item);
        }
        goodsCategoriesCount += suiXinPinSkus.getVarietyNum();
        goodsTotalCount += suiXinPinSkus.getTotalNum();
        totalPrice += suiXinPinSkus.getTotalAmount();
        CartGoodsInfo  cartGoodsInfo = new CartGoodsInfo(goodsCategoriesCount, goodsTotalCount, totalPrice + "");
        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = mViewModel.getSpellGroupWithoutGoodsLiveData().getValue();
        SpellGroupGoodsItem mainRowsBean = null;
        if (spellGroupRecommendGoodsBean != null) {
            mainRowsBean = spellGroupRecommendGoodsBean.getMainRowsBean();
        }
        return new SpellGroupRecommendGoodsBean(goodsItems, mainRowsBean, mapping, cartGoodsInfo, false);
    }

    /**
     * 顺手买数据转换  order/v1/buySomethingCasuallyQuery 接口返回 和 settle 接口返回的数据结构不一样
     *
     * @param suiXinPinSkus 返回的数据
     * @return
     */
    private SpellGroupRecommendGoodsBean bugSomethingMappingData(PaymentSuiXinPinSkusBean suiXinPinSkus) {
        if (suiXinPinSkus == null) return null;
        if (suiXinPinSkus.getItems() == null || suiXinPinSkus.getItems().isEmpty()) return null;
        List<SpellGroupGoodsItem> goodsItems = new ArrayList<>();
        HashMap<String, Integer> mapping = new HashMap<>();
        for (PaymentSuiXinPinSkusItemBean bean : suiXinPinSkus.getItems()) {
            String price = "";
            String skuid = "";
            if (TextUtils.isEmpty(bean.getPrice())) {
                price = bean.getFob();
            } else {
                price = bean.getPrice();
            }
            if (TextUtils.isEmpty(bean.getSkuId())) {
                skuid = bean.getId();
            } else {
                skuid = bean.getSkuId();
            }
            SpellGroupGoodsItem item = new SpellGroupGoodsItem(bean.getImageUrl(), bean.getMarkerUrl(), bean.getShowName(), price, bean.getProductUnit(), bean.getRetailPrice(), bean.getQty(), skuid, bean.getMediumPackageNum(), bean.isSplit(), 0, "0", bean.getSpid(), bean.getSptype(), bean.getSid(), bean.getSourceType(), bean.getNearEffect(), bean.getTagList(), bean.getActPt(), bean.getActPgby());
            item.setQtData(bean.getQtData());
//            if (bean.getSkuId() == null) bean.setSkuId("");;
            mapping.put(skuid, bean.getQty());
            goodsItems.add(item);
        }
        CartGoodsInfo cartGoodsInfo = new CartGoodsInfo(suiXinPinSkus.getVarietyNum(), suiXinPinSkus.getTotalNum(), suiXinPinSkus.getTotalAmount() + "");
        SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = mViewModel.getSpellGroupWithoutGoodsLiveData().getValue();
        SpellGroupGoodsItem mainRowsBean = null;
        if (spellGroupRecommendGoodsBean != null) {
            mainRowsBean = spellGroupRecommendGoodsBean.getMainRowsBean();
        }
        return new SpellGroupRecommendGoodsBean(goodsItems, mainRowsBean, mapping, cartGoodsInfo, false);
    }


    //购物金是否显示在屏幕内
    private boolean isShoppingGoldInWindow(){
        if (clShoppingGold == null){
            return false;
        }
        int[] location = new int[2];
        clShoppingGold.getLocationInWindow(location); // 获取View在窗口中的位置
        int viewTop = location[1]; // View的顶部位置
        int viewBottom = viewTop + clShoppingGold.getHeight(); // View的底部位置

        // 屏幕的顶部和底部位置（相对于窗口）
        int screenTop = 0;
        int screenBottom = screenTop + getWindowManager().getDefaultDisplay().getHeight();

        // 判断View是否至少部分显示在屏幕上
//        boolean isPartiallyVisible = (viewTop < screenBottom) && (viewBottom > screenTop);
        // 判断View是否至少部分显示在屏幕上 只需要在面时候才算部分可见
        boolean isPartiallyVisible = (viewTop < screenBottom);

        return isPartiallyVisible;
    }

    /**
     * 设置购物金
     */
    private void setVirtualMoney(PaymentNewsBean paymentNewBean) {
        clearVirtualMoney();
        if (paymentNewBean == null)  return;
        setShoppingGoldBottomTips(paymentNewBean);
        setVirtualMoneyContent(paymentNewBean);
        setVirtualMoneyTipsDialog(paymentNewBean);
        setShoppingGoldDetail(paymentNewBean);
        ((AnimationDrawable) ivShoppingGoldTips.getBackground()).start();
    }

    //后端返回-购物金提示弹窗
    private void setVirtualMoneyTipsDialog(PaymentNewsBean paymentNewBean){ //购物金提示弹窗
        if (paymentNewBean.getPayType()!=null && paymentNewBean.getPayType().getPayWayList() !=null && paymentNewBean.getPayType().getPayWayList().size()>0){
            for (PayWayBean payWayBean : paymentNewBean.getPayType().getPayWayList()) {
                //在线支付
                if (payType == 1 && payWayBean.cashier != null && payWayBean.cashier.getPrompt() != null && !payWayBean.cashier.getPrompt().isEmpty()){
                    showShoppingGoldChangeTipsDialog(payWayBean.cashier.getPrompt());
                }
            }
        }
    }

    /**
     * 购物金文案
     * @param paymentNewBean
     */
    private void setVirtualMoneyContent(PaymentNewsBean paymentNewBean) {
        if (paymentNewBean.getBalance() != null && !TextUtils.isEmpty(paymentNewBean.getBalance().virtualGoldTips)) {
            clShoppingGold.setVisibility(View.VISIBLE);
            isShowVirtualGold = true;
            //购物金 内容文案
            tvVirtualMoneyTips.setText(paymentNewBean.getBalance().virtualGoldTips);
            if (paymentNewBean.getBalance().isVirtualGoldEnabled()){
                setVirtualMoneyStyleA(paymentNewBean);
            }else {
                setVirtualMoneyStyleB(paymentNewBean);
            }
            setShoppingGoldCheckedWithoutCallback(paymentNewBean.getBalance().isSelected());

        } else {
            clShoppingGold.setVisibility(View.GONE);
            isShowVirtualGold = false;
        }
    }

    //购物金选项+充值选项
    private void setVirtualMoneyStyleA(PaymentNewsBean paymentNewBean) {
        cbVirtualMoneyOnOff.setEnabled(true);
        ArrayList<VirtualGoldRechargeBean> virtualGoldRechargeList = new ArrayList<>();
        if (paymentNewBean != null && paymentNewBean.getBalance()!=null && paymentNewBean.getBalance().getRechargeList()!=null){
            virtualGoldRechargeList = paymentNewBean.getBalance().getRechargeList();
        }
        clShoppingGold.setVisibility(View.VISIBLE);
        if (virtualGoldRechargeList.isEmpty()){
            rvShoppingGold.setVisibility(View.GONE);
            rvShoppingGold.setAdapter(null);
        }else {
            rvShoppingGold.setVisibility(View.VISIBLE);
            rvShoppingGold.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

            for (VirtualGoldRechargeBean bean:
                    virtualGoldRechargeList) {
                if (bean.isSelected()){
                    setMCurrentVirtualGoldRechargeBean(bean);
                }
            }

            shoppingGoldRechargeAdapter = new ShoppingGoldRechargeAdapter(virtualGoldRechargeList);
            shoppingGoldRechargeAdapter.setOnItemClickListener((virtualGoldRechargeBean,isReadSelected) -> {

                if (isReadSelected){
                    setMCurrentVirtualGoldRechargeBean(null);
                }else {
                    setMCurrentVirtualGoldRechargeBean(virtualGoldRechargeBean);
                }
                if (virtualGoldRechargeBean.isCanUse()){
                    setRedEnvelopeCheckedWithoutCallback(true);
                }

                if (cbVirtualMoneyOnOff.isChecked()){
                    setCbVirtualMoneyStatus(true,true);
                }else {
                    setCbVirtualMoneyStatus(true,false);
                }
                return null;
            });
            rvShoppingGold.setAdapter(shoppingGoldRechargeAdapter);
        }
    }


    private void setShoppingGoldCheckedWithoutCallback(boolean checked) {
        mNeedShoppingGoldCbCallBack = false;
        cbVirtualMoneyOnOff.setChecked(checked);
        mNeedShoppingGoldCbCallBack = true;
    }

    private void setRedEnvelopeCheckedWithoutCallback(boolean checked) {
        mNeedRedEnvelopeCbCallBack = false;
        cbRedEnvelopeOnOff.setChecked(checked);
        mNeedRedEnvelopeCbCallBack = true;
    }


    private void setCbVirtualMoneyStatus(boolean isSelect, boolean needRequest){
        //状态一样的时候  不会在change的监听里面请求接口 手动请求接口
        cbVirtualMoneyOnOff.setChecked(isSelect);
        if (needRequest){
            getData(billType, payType, mVoucherIds, useBalance);
        }
    }

    //购物金选项不可选中+提示
    private void setVirtualMoneyStyleB(PaymentNewsBean paymentNewBean){
        clShoppingGold.setVisibility(View.VISIBLE);
        setShoppingGoldCheckedWithoutCallback(false);
        cbVirtualMoneyOnOff.setEnabled(false);

        if (!TextUtils.isEmpty(paymentNewBean.getBalance().getGoldMsg())){
            clShoppingGoldDetail.setVisibility(View.VISIBLE);
            tvShoppingGoldDetail.setText(paymentNewBean.getBalance().getGoldMsg());
            if (!TextUtils.isEmpty(paymentNewBean.getBalance().getGoldTips())){
                ivShoppingGoldDetail.setOnClickListener(v -> {
                    showShoppingGoldCantUseTipsDialog(paymentNewBean.getBalance().getGoldTips());
                });
            }
        }else {
            clShoppingGoldDetail.setVisibility(View.GONE);
        }
    }


    /**
     * 设置订单详情里面的购物金信息
     * @param paymentNewBean
     */
    private void setShoppingGoldDetail(PaymentNewsBean paymentNewBean){
        //购物金抵扣
        if (paymentNewBean.getSettle() != null && paymentNewBean.getSettle().virtualGold > 0) {
            llOrderVirtualMoney.setVisibility(View.VISIBLE);
            virtualGold = paymentNewBean.getSettle().virtualGold;
            String balanceNum = "-¥" + UiUtils.transform(paymentNewBean.getSettle().virtualGold);
            tvOrderVirtualMoneyNum.setText(balanceNum);
        } else {
            llOrderVirtualMoney.setVisibility(View.GONE);
        }

        //购物金充值
        if (paymentNewBean.getSettle() != null && paymentNewBean.getSettle().getRechargeAmount() > 0) {
            llOrderVirtualMoneyRecharge.setVisibility(View.VISIBLE);
            String balanceNum = "+¥" + UiUtils.transform(paymentNewBean.getSettle().getRechargeAmount());
            tvOrderVirtualMoneyRechargeNum.setText(balanceNum);
        } else {
            llOrderVirtualMoneyRecharge.setVisibility(View.GONE);
        }

    }

    /**
     * 设置底部购物金滑动浮窗
     */
    private void setShoppingGoldBottomTips(PaymentNewsBean paymentNewBean){
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        if (paymentNewBean.getBase()!=null && paymentNewBean.getBase().getRechargeTextList() != null){
            ArrayList<VirtualGoldRechargeTextBean> rechargeTextList = paymentNewBean.getBase().getRechargeTextList();
            for (VirtualGoldRechargeTextBean bean:
                  rechargeTextList) {
                if (bean.getText() != null && !bean.getText().isEmpty()){
                    String mText = bean.getText();
                    int mCurrentLength = spannableStringBuilder.length();
                    int mColor = Color.BLACK;
                    try {
                        if (bean.getColor() != null && !bean.getColor().isEmpty()){
                            mColor = Color.parseColor(bean.getColor());
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }

                    spannableStringBuilder.append(mText);
                    spannableStringBuilder.setSpan(
                            new ForegroundColorSpan(mColor),
                            mCurrentLength,
                            mCurrentLength+mText.length(),
                            0
                    );
                }
            }
        }

        tvShoppingGoldTips.setText(spannableStringBuilder);

        mShoppingGoldTips = tvShoppingGoldTips.getText().toString();

        if (!mShoppingGoldTips.isEmpty()){
            try {
                clShoppingGoldTips.postDelayed(()->{
                    clShoppingGoldTips.setVisibility(isShoppingGoldInWindow() ? View.GONE : View.VISIBLE);
                },200);
                nsvPayment.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                    if (mShoppingGoldTips == null || mShoppingGoldTips.isEmpty()) return;
                    clShoppingGoldTips.setVisibility(isShoppingGoldInWindow() ? View.GONE : View.VISIBLE);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        clShoppingGoldTips.setOnClickListener(v -> {
            if (clShoppingGold.getVisibility() == View.GONE){
                clShoppingGoldTips.setVisibility(View.GONE);
                return;
            }
            int[] location = new int[2];
            clShoppingGold.getLocationInWindow(location);
            int offset = ScreenUtils.dip2px(PaymentActivity.this, 70) + ScreenUtils.getStatusBarHeight(PaymentActivity.this);
            int top = location[1];
            if (top == offset) {
                clShoppingGoldTips.setVisibility(View.GONE);
                return;
            }
            nsvPayment.smoothScrollBy(0, top - offset);
        });
    }

    /**
     * 清空购物金相关数据
     */
    private void clearVirtualMoney(){

        clShoppingGold.setVisibility(View.GONE);
        clShoppingGoldTips.setVisibility(View.GONE);
        rvShoppingGold.setVisibility(View.GONE);
        rvShoppingGold.setAdapter(null);
        clShoppingGoldDetail.setVisibility(View.GONE);
        llOrderVirtualMoney.setVisibility(View.GONE);
        llOrderVirtualMoneyRecharge.setVisibility(View.GONE);
        nsvPayment.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) null);
        setMCurrentVirtualGoldRechargeBean(null);

    }

    private void showShoppingGoldCantUseTipsDialog(String content) {

        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(content).setCancelButton("确定", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    private void showShoppingGoldChangeTipsDialog(String content) {

        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(content).setCancelButton("我知道啦", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    /**
     * 设置红包
     */
    private void setRedEnvelope(PaymentNewsBean paymentNewBean) {
        if (paymentNewBean == null) {
            llRedEnvelope.setVisibility(View.GONE);
            llOrderRedEnvelope.setVisibility(View.GONE);
            return;
        }
        if (paymentNewBean.getBalance() != null && !TextUtils.isEmpty(paymentNewBean.getBalance().redPacketTips)) {
            llRedEnvelope.setVisibility(View.VISIBLE);
            isShowRedEnvelope = true;
        } else {
            llRedEnvelope.setVisibility(View.GONE);
            isShowRedEnvelope = false;
        }
        if (paymentNewBean.getSettle() != null && paymentNewBean.getSettle().redPacketAmount > 0) {
            llOrderRedEnvelope.setVisibility(View.VISIBLE);
        } else {
            llOrderRedEnvelope.setVisibility(View.GONE);
        }
        if (paymentNewBean.getBalance() != null && !TextUtils.isEmpty(paymentNewBean.getBalance().redPacketTips)) {
            redEnvelopeGold = paymentNewBean.getSettle().redPacketAmount;
            tvRedEnvelopeTips.setText(paymentNewBean.getBalance().redPacketTips);
            String redPacketNum = "-¥" + UiUtils.transform(paymentNewBean.getSettle().redPacketAmount);
            tvOrderRedEnvelopeNum.setText(redPacketNum);
        }
    }

    /**
     * 支付优惠
     * @param paymentNewBean
     */
    private void setPayDiscount(PaymentNewsBean paymentNewBean) {
        if (paymentNewBean == null || paymentNewBean.getSettle() == null) {
            llOrderPayDiscount.setVisibility(View.GONE);
            return;
        }
        if (TextUtils.isEmpty(paymentNewBean.getSettle().payDiscount)) {
            llOrderPayDiscount.setVisibility(View.GONE);
        } else {
            llOrderPayDiscount.setVisibility(View.VISIBLE);
            String payDiscountText = "-¥" + UiUtils.transform(paymentNewBean.getSettle().payDiscount);
            tvOrderPayDiscountNum.setText(payDiscountText);
        }
    }

    /**
     * 获取刷新本页面的参数信息
     */
    private RequestParams getRefreshParams() {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("version", "1.0"); // 版本控制
        if (!TextUtils.isEmpty(tranNo)) {
            params.put("tranNo", tranNo);
        }
        if (!TextUtils.isEmpty(giftId)) {
            params.put("giftIds", giftId);

        }
        params.put("storeStatus", "true"); //storeStatus => 是否使用勾选商品结算
//        if (isKaUser) {
//            params.put("useBalance", "" + false);
//        } else {
//            params.put("useBalance", "" + true);
//        }
        params.put("useBalance", "false");
        // 如果是优惠券使用提醒后的刷新，加这个参数，后台用来对此订单加标识
        params.put("voucherMonitor", "1");
        return params;
    }

    /*
     *  设置商品list
     * */
    private void setPaymentList2Data(PaymentCompanyListBean companys) {

        if (companys == null || companys.getCompanys() == null || companys.getCompanys().isEmpty()) {
            return;
        }
        if (newList == null) {
            newList = new ArrayList<>();
        }
        newList.clear();
        if (RefundProductList == null) {
            RefundProductList = new ArrayList<>();
        }
        RefundProductList.clear();

        boolean isSelectStatus = false;
        int id = 0;
        List<String> gift = new ArrayList<>();

        /*-----------公司组-头部-----------*/
        List<PaymentCompanyBean> companyList = companys.getCompanys();
        if (companyList != null && companyList.size() > 0) {
            PaymentCompanyBean company;
            for (int i = 0; i < companyList.size(); i++) {
                company = companyList.get(i);
                if (company == null) {
                    continue;
                }
                PaymentItemBean itemGroup = new PaymentItemBean();
                itemGroup.isFbpShop = company.isFbpShop();

                itemGroup.setIsThirdCompany(company.getIsThirdCompany());
                if (!company.isNotThirdCompany()) { // 为了给pop店铺增加优惠券
                    if (company.getShops() != null && !company.getShops().isEmpty()) {
                        PaymentShopBean shop = company.getShops().get(0);
                        itemGroup.setShopCode(shop.getShopCode());
                        itemGroup.setShopName(shop.getShopName());
                        itemGroup.setShopType(shop.getShopType());
                        itemGroup.setAvailBalanceAmt(shop.getAvailBalanceAmt());
                        itemGroup.setDefaultShowProducts(shop.getDefaultShowProducts());
                        itemGroup.setPayAmount(shop.getPayAmount());
                        itemGroup.setPromoTotalAmt(shop.getPromoTotalAmt());
                        itemGroup.setDiscountAmount(shop.getDiscountAmount());
                        itemGroup.setTotalAmount(shop.getTotalAmount());
                        itemGroup.setVouchers(shop.getVouchers());
                        itemGroup.setVoucherTip(shop.getVoucherTip());
                        itemGroup.setProductVarietyNum(shop.getProductVarietyNum());
                        itemGroup.setShopPatternCode(shop.getShopPatternCode());
                        itemGroup.setGiftTotalAmount(shop.getGiftTotalAmount());
                        itemGroup.setAllPayAmount(company.getPayAmount());
                        itemGroup.setCompanyCode(company.getCompanyCode());
                        itemGroup.setFreightTotalAmt(company.getFreightTotalAmt());
                        itemGroup.setSelectVoucherIds(shop.getSelectVoucherIds());
                        itemGroup.setSelectDjVoucherList(shop.getSelectDjVoucherList());
                        itemGroup.setSelectNormalVoucherList(shop.getSelectNormalVoucherList());
                        itemGroup.freightTipsShowStatus = company.freightTipsShowStatus;
                        itemGroup.freeFreightDiffTips = company.freeFreightDiffTips;
                        itemGroup.freightIconShowStatus = company.freightIconShowStatus;
                        itemGroup.freightUrlText = company.freightUrlText;
                        itemGroup.nextDayDeliveryDto = company.nextDayDeliveryDto;
                    }
                } else {
                    if (company.getShops() != null && !company.getShops().isEmpty()) {
                    PaymentShopBean shop = company.getShops().get(0);
                    itemGroup.setShopCode(shop.getShopCode());
                    itemGroup.setShopName(shop.getShopName());
                    }
                }
                itemGroup.setCompanyCode(company.getCompanyCode());
                itemGroup.setCompanyName(company.getCompanyName());
                itemGroup.setCompanyType(company.getCompanyType());
                itemGroup.setAvailBalanceAmt(company.getAvailBalanceAmt());
                itemGroup.setDefaultShowProducts(company.getDefaultShowProducts());
                itemGroup.setFreightTotalAmt(company.getFreightTotalAmt());
                // 不理解为什么这么写，这里直接设置company.getPayAmount()的话，那上面为了给pop店铺增加优惠券这段逻辑就没用，永远会被下面覆盖
                itemGroup.setPayAmount(company.getPayAmount());
                itemGroup.setProductCount(company.getProductCount());
                itemGroup.setProductVarietyNum(company.getProductVarietyNum());
                itemGroup.setPromoTotalAmt(company.getPromoTotalAmt());
                itemGroup.setTotalAmount(company.getTotalAmount());
                itemGroup.setDiscountAmount(company.getDiscountAmount());
                itemGroup.freightTipsShowStatus = company.freightTipsShowStatus;
                itemGroup.freeFreightDiffTips = company.freeFreightDiffTips;
                itemGroup.freightIconShowStatus = company.freightIconShowStatus;
                itemGroup.freightUrlText = company.freightUrlText;
                itemGroup.nextDayDeliveryDto = company.nextDayDeliveryDto;
                itemGroup.getSubItems().clear();
                itemGroup.setGroup(true);
                itemGroup.setExpanded(false);
                if (company.isNotThirdCompany()) {
                    itemGroup.setExpanded(isExpanded);
                }

                List<RefundProductListBean> RefundProductCompany = new ArrayList<>();
                /*-----------店铺组-内容-----------*/
                List<PaymentShopBean> shops = company.getShops();
                if (shops != null && shops.size() > 0) {

                    if (company.isNotThirdCompany() && shops.size() <= 1) {
                        itemGroup.setExpanded(true);
                    }

                    PaymentShopBean shop;
                    for (int a = 0; a < shops.size(); a++) {
                        shop = shops.get(a);
                        if (shop == null) {
                            continue;
                        }
                        itemGroup.setPayAmount(shop.getPayAmount());// 合计价格取shop下的payamount，详情咨询红木
                        itemGroup.setProductVarietyNum(shop.getProductVarietyNum());// 同上
                        PaymentItemBean children = new PaymentItemBean();
                        children.setShopCode(shop.getShopCode());
                        children.setShopName(shop.getShopName());
                        children.setShopType(shop.getShopType());
                        children.setAvailBalanceAmt(shop.getAvailBalanceAmt());
                        children.setDefaultShowProducts(shop.getDefaultShowProducts());
                        children.setPayAmount(shop.getPayAmount());
                        children.setPromoTotalAmt(shop.getPromoTotalAmt());
                        children.setDiscountAmount(shop.getDiscountAmount());
                        children.setTotalAmount(shop.getTotalAmount());
                        children.setVouchers(shop.getVouchers());
                        children.setVoucherTip(shop.getVoucherTip());
                        children.setProductVarietyNum(shop.getProductVarietyNum());
                        children.setShopPatternCode(shop.getShopPatternCode());
                        children.setGiftTotalAmount(shop.getGiftTotalAmount());
                        children.setAllPayAmount(company.getPayAmount());
                        children.setCompanyCode(company.getCompanyCode());
                        children.setFreightTotalAmt(company.getFreightTotalAmt());
                        children.setSelectVoucherIds(shop.getSelectVoucherIds());
                        children.setSelectDjVoucherList(shop.getSelectDjVoucherList());
                        children.setSelectNormalVoucherList(shop.getSelectNormalVoucherList());
                        children.freightTipsShowStatus = company.freightTipsShowStatus;
                        children.freeFreightDiffTips = company.freeFreightDiffTips;
                        children.freightIconShowStatus = company.freightIconShowStatus;
                        children.freightUrlText = company.freightUrlText;
                        itemGroup.nextDayDeliveryDto = company.nextDayDeliveryDto;
                        children.isFbpShop = company.isFbpShop();

                        if (a == (shops.size() - 1)) {
                            children.setLast(true);
                        }
                        if (company.getIsThirdCompany() == 0 && shops.size() <= 1) {
                            children.setShopCount(true);
                        }

                        itemGroup.getSubItems().add(children);
                        /*-------------------------------商品明细-------------------------------------*/
                        List<RefundProductListBean> RefundProductShop = new ArrayList<>();
                        //group组
                        List<PaymentShoppingGroupBean> groupList = shop.getGroups();
                        if (groupList != null && groupList.size() > 0) {

                            PaymentShoppingGroupBean group;
                            for (int j = 0; j < groupList.size(); j++) {
                                group = groupList.get(j);
                                if (group == null) {
                                    continue;
                                }
                                if (!getIsFromCart()) { //不是来自购物车
                                    itemGroup.setGroup(group);
                                    for (Object o : itemGroup.getSubItems().toArray()) {
                                        PaymentItemBean itemBean = (PaymentItemBean) o;
                                        itemBean.setGroup(group);
                                    }
                                }

                                //大礼包-title
                                if (group.getType() == 5) {
                                    if (id != group.getId()) {
                                        gift.add(group.getId() + "");
                                        RefundProductListBean listBean_title = new RefundProductListBean();
                                        listBean_title.setItemType(RefundProductListBean.ITEMTYPE_GIFT_CONTENT);
                                        listBean_title.id = group.getId();
                                        listBean_title.type = group.getType();
                                        listBean_title.selectStatus = group.getSelectStatus();
                                        listBean_title.setChannelCode(group.getChannelCode());
                                        RefundProductShop.add(listBean_title);
                                        id = group.getId();
                                        if (group.getSelectStatus() == 1) {
                                            isSelectStatus = true;
                                        }
                                    }
                                }

                                if (!gift.contains(giftId)) {
                                    if (group.getType() == 5) {
                                        if (group.getId() > 0) {
                                            if (group.getSelectStatus() == 1) {
                                                if (!giftId.equals(group.getId() + "")) {
                                                    giftId = group.getId() + "";
                                                }
                                            }
                                        }
                                    }
                                }

                                List<PaymentSortedBean> sorted = group.getSorted();
                                setRefundProduct(RefundProductShop, group, sorted);
                            }
                        }
                        //shop-商品明细
                        children.setDetailList(RefundProductShop);
                        RefundProductCompany.addAll(RefundProductShop);
                        //是否显示会员礼包
                        for (RefundProductListBean refundProductListBean : RefundProductShop) {
                            if (itemGroup.getIsThirdCompany() == 0) {
                                if (refundProductListBean.type == 5) {
                                    children.setType(refundProductListBean.type);
                                    children.setSelectStatus(isSelectStatus);
                                }
                            }
                        }
                    }
                    //company-商品明细
                    itemGroup.setDetailList(RefundProductCompany);
                    RefundProductList.addAll(RefundProductCompany);
                    //是否显示会员礼包
                    for (RefundProductListBean refundProductListBean : RefundProductCompany) {
                        if (itemGroup.getIsThirdCompany() == 0) {
                            if (refundProductListBean.type == 5) {
                                itemGroup.setType(refundProductListBean.type);
                                itemGroup.setSelectStatus(isSelectStatus);
                            }
                        }
                    }
                }
                newList.add(itemGroup);
            }
        }
//        paymentGoodsViewModel.setPaymentItemList(this.newList);
        try {
            //添加随心拼商品到随货资质需求列表
            paymentGoodsViewModel.addSuiXinPinGoods(companys.getCompanys().get(0).suiXinPinSkus, companys.getCompanys().get(0).getCompanyCode());
        } catch (Exception e) {
            e.printStackTrace();
        }
        paymentGoodsViewModel.getLicenseResultLiveData().observe(this, o -> {
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        });
        mAdapter.notifyGroupDataChanged(this.newList);
        paymentGoodsViewModel.setPaymentItemList(this.newList);
    }


    private void setRefundProduct(List<RefundProductListBean> refundProductShop, PaymentShoppingGroupBean group, List<PaymentSortedBean> sorted) {
        if (sorted != null && sorted.size() > 0) {
            PaymentSortedBean groupBean;
            for (int c = 0; c < sorted.size(); c++) {
                groupBean = sorted.get(c);
                if (groupBean == null) {
                    continue;
                }
                int itemType = groupBean.getItemType();
                CartItemBean item = groupBean.getItem();

                /*---------------------商品------------------------*/
                if (itemType == 3) {//套餐商品
                    if (item != null) {//套餐描述不能空
                        //套餐顶部标题
                        RefundProductListBean listBean = new RefundProductListBean();
                        listBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE);
                        listBean.productName = item.getName();
                        if (TextUtils.isEmpty(listBean.productName)) {
                            listBean.productName = "搭配套餐";
                        }
                        listBean.productId = item.getPackageId() + "";
                        listBean.productAmount = item.getAmount() + "";
                        listBean.subtotal = item.getSubtotal() + "";
                        listBean.productPrice = item.getPrice();
                        listBean.setChannelCode(item.getChannelCode());
                        if (item.getSku() != null && item.getSku().activityTag != null) {
                            listBean.productActivityTag = item.getSku().activityTag;
                        }
                        if (groupBean.getSubItemList() != null) {
                            listBean.subSize = groupBean.getSubItemList().size();
                        }
                        refundProductShop.add(listBean);
                        //套餐商品
                        if (groupBean.getSubItemList() != null && !groupBean.getSubItemList().isEmpty()) {
                            for (int b = 0; b < groupBean.getSubItemList().size(); b++) {
                                CartItemBean bean = groupBean.getSubItemList().get(b);
                                listBean = new RefundProductListBean();
                                listBean.setItemType(ITEMTYPE_PACKAGE_CONTENT);
                                listBean.imageUrl = bean.getImageUrl();
                                listBean.productName = bean.getName();
                                listBean.productId = bean.getSkuId() + "";
                                listBean.productPrice = bean.getPrice();
                                listBean.subtotal = bean.getSubtotal() + "";
                                listBean.spec = bean.getSpec() + "";
                                listBean.productAmount = bean.getAmount() + "";
                                listBean.blackProductText = bean.getBlackSkuText();
                                listBean.tagList = bean.getTagList();
                                listBean.tagWholeOrderList = bean.getTagWholeOrderList();
                                listBean.tagTitle = bean.getTagTitle();
                                listBean.balanceAmount = bean.getBalanceAmount();
                                listBean.realPayAmount = bean.getRealPayAmount();
                                listBean.useBalanceAmount = bean.getUseBalanceAmount();
                                listBean.discountAmount = bean.getDiscountAmount();
                                listBean.rkPrice = bean.getPurchasePrice();
                                listBean.gift = bean.isGift();
                                listBean.costPrice = bean.getCostPrice();
                                listBean.setChannelCode(bean.getChannelCode());
                                if (bean.getSku() != null && bean.getSku().activityTag != null) {
                                    listBean.productActivityTag = bean.getSku().activityTag;
                                }
                                refundProductShop.add(listBean);
                            }
                        }
                        //套餐底部
                        listBean = new RefundProductListBean();
                        listBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE);
                        listBean.subtotal = item.getSubtotal() + "";
                        listBean.productPrice = item.getPrice();
                        listBean.setChannelCode(item.getChannelCode());
                        refundProductShop.add(listBean);
                    }
                } else {
                    if (groupBean.getItem() != null) {
                        if (item != null) {

                            RefundProductListBean listBean = new RefundProductListBean();
                            listBean.setItemType(RefundProductListBean.ITEMTYPE_CONTENT);
                            listBean.imageUrl = item.getImageUrl();
                            listBean.productName = item.getName();
                            listBean.productId = item.getSkuId() + "";
                            listBean.productPrice = item.getPrice();
                            listBean.subtotal = item.getSubtotal() + "";
                            listBean.spec = item.getSpec() + "";
                            listBean.productAmount = item.getAmount() + "";
                            listBean.blackProductText = item.getBlackSkuText();
                            listBean.tagList = item.getTagList();
                            listBean.tagWholeOrderList = item.getTagWholeOrderList();
                            listBean.tagTitle = item.getTagTitle();
                            listBean.balanceAmount = item.getBalanceAmount();
                            listBean.realPayAmount = item.getRealPayAmount();
                            listBean.useBalanceAmount = item.getUseBalanceAmount();
                            listBean.discountAmount = item.getDiscountAmount();
                            listBean.rkPrice = item.getPurchasePrice();
                            listBean.gift = item.isGift();
                            listBean.costPrice = item.getCostPrice();
                            listBean.setChannelCode(item.getChannelCode());
                            listBean.priceDes = item.priceDesc;
                            if (item.getSku() != null && item.getSku().activityTag != null) {
                                listBean.productActivityTag = item.getSku().activityTag;
                            }
                            listBean.type = item.getType();
                            listBean.id = item.getId();
                            if (group.getType() == 5 && group.getId() > 0) {
                                listBean.type = group.getType();
                                listBean.id = group.getId();
                            }
                            refundProductShop.add(listBean);
                        }
                    }
                }
            }
        }
    }

    /*
     * 支付方式
     * */
    private void setPayTypeLayout(PaymentPayTypeBean bean) {

        if (bean.getPayWayList() == null || bean.getPayWayList().isEmpty()) {//1 在线支付 2货到付款 3 线下转账，的默认支付方式
            List<PayWayBean> payWayList = new ArrayList<>(3);
            //在线支付
            PayWayBean beanOL = new PayWayBean();
            beanOL.payway = "在线支付";
            beanOL.payType = 1;
            beanOL.tips = bean.getPayTips();
            beanOL.checked = true;
            payWayList.add(beanOL);
            if (bean.getIsShow() == 1) {// == 1  显示  ==0 不显示
                //货到付款
                PayWayBean beanfL = new PayWayBean();
                beanfL.payway = "货到付款";
                beanfL.payType = 2;
                beanfL.tips = bean.getPadPayTips();
                beanfL.msg = bean.getOfflineMessage();
                payWayList.add(beanfL);
            }
            //线下转账
            PayWayBean beanbfl = new PayWayBean();
            beanbfl.payway = "线下转账";
            beanbfl.payType = 3;
            beanbfl.tips = bean.getTranPayTips();
            payWayList.add(beanbfl);

            //他人代付
            if ("1".equals(bean.getShowOthersPayState())) {//1显示 0不显示
                PayWayBean beanPayForAnoter = new PayWayBean();
                beanPayForAnoter.payway = "他人代付";
                beanPayForAnoter.payType = 6;
                //beanPayForAnoter.tips=bean.get;
                payWayList.add(beanPayForAnoter);
            }

            bean.setPayWayList(payWayList);
        }
        payTypeLayout.setData(bean.getPayWayList());
    }

    /*
     * 结算页余额相关
     * */
    private void setTips(PaymentBalanceBean bean) {

        if (TextUtils.isEmpty(bean.getBalanceTips())) {
            bean.setBalanceTips("");
        }
        mTvBalanceTips.setText(bean.getBalanceTips());

        mTvPayBalance.setVisibility(View.GONE);
        if (!TextUtils.isEmpty(bean.getReturnBalanceTips())) {
            mTvPayBalance.setVisibility(View.VISIBLE);
            mTvPayBalance.setText(bean.getReturnBalanceTips());
        }

    }

    /*
     * 结算页结算数据
     * */
    private void setOrderDetails(PaymentNewsBean data) {
        if (data == null) {
            return;
        }

        //设置提单按钮文本
        if (data.getBase() != null && !TextUtils.isEmpty(data.getBase().btnText)) {
            btnOk.setText(data.getBase().btnText);
        }

        if (data.getSettle() != null) {
            //底部栏运费
            String freightDes = "(" + data.getSettle().getFreightTips() + ")";
//            tvFreightBottomDes.setVisibility(isKaUser ? View.GONE : View.VISIBLE);
//            tvFreightBottomDes.setText(freightDes);
            String payAmount = UiUtils.getPriceWithFormat("¥" + UiUtils.transform(data.getSettle().getPayAmount()), 12).toString();
            tvPayAmount.setText(payAmount);
            String totalNum = "¥" + UiUtils.transform(data.getSettle().getTotalAmount());
            tvTotalNum.setText(totalNum);
            String freightNum = "+¥" + UiUtils.transform(data.getSettle().getFreightTotalAmt());
            tvFreightNum.setText(freightNum);
//            tvFreightBottomDes.setText("(含运费:"+UiUtils.transform(data.getSettle().getFreightTotalAmt())+"元)");
//            tvFreightBottomDes.setVisibility(View.VISIBLE);
            //改为“全场优惠券”
            if (!isKaUser) {//增加ka用户不显示优惠券
                llOrderCoupon.setVisibility(View.VISIBLE);
                String couponNum = "-¥" + UiUtils.transform(data.getSettle().getVoucherTotalAmt());
                tvOrderCouponNum.setText(couponNum);
            } else {
                llOrderCoupon.setVisibility(View.GONE);
            }
            if (data.getSettle().isHideCoupon == 1) {
                llOrderCoupon.setVisibility(View.GONE);
            } else {
                llOrderCoupon.setVisibility(View.VISIBLE);
            }
            //改为“全场满减”
            if (data.getSettle().getPromoTotalAmt() >= 0.0001) {
                llOrderFl.setVisibility(View.VISIBLE);
                String flNum = "-¥" + UiUtils.transform(data.getSettle().getPromoTotalAmt());
                tvOrderFlNum.setText(flNum);
            } else {
                llOrderFl.setVisibility(View.GONE);
            }
            //一口价
            if (data.getSettle().getFixedPriceAmount() >= 0.0001) {
                llOrderOnePrice.setVisibility(View.VISIBLE);
                String onePriceNum = "-¥" + UiUtils.transform(data.getSettle().getFixedPriceAmount());
                tvOrderOnePriceNum.setText(onePriceNum);
            } else {
                llOrderOnePrice.setVisibility(View.GONE);
            }
            //使用余额金额
//            if (data.getBalance().getBalanceAmount() >= 0.0001 && (data.getCart() != null && data.getCart().isUseBalance())) {
//                mLlOrderBalance.setVisibility(View.VISIBLE);
//                String balanceNum = "-¥" + UiUtils.transform(data.getBalance().getBalanceAmount());
//                mTvOrderBalanceNum.setText(balanceNum);
//            } else {
//                mLlOrderBalance.setVisibility(View.GONE);
//            }
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_payment;
    }

    /**
     * 更改地址
     */
    private void updateAddress() {
        Intent intent = new Intent(PaymentActivity.this, AddressListActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("source", "pay");
        bundle.putSerializable("data", address);
        intent.putExtras(bundle);
        startActivityForResult(intent, REQUESTCODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (TextUtils.equals(getViewModel().getMSelectedPayCode(), YBMPayUtil.PAY_YLSDK)) {
            //银联支付不在这里处理
            return;
        }
        if (requestCode == REQUESTCODE && resultCode == RESULT_OK) {
            address = (AddressListBean) data.getSerializableExtra("addressIndex");
            getData(billType, payType, mVoucherIds, useBalance);
        } else if (requestCode == REQUESTCODE_TO_AVAILABLE_VOUCHER && resultCode == RESULT_OK) {//选择优惠卷 重新计算
            String ids = data.getStringExtra("voucherIds");
            if (ids == null) {
                ids = "";
            }
            mVoucherIds = ids;
            getData(billType, payType, mVoucherIds, true, useBalance);
        } else if (requestCode == REQUESTCODE_TO_FREIGHT_ADD_ON_ITEM && resultCode == RESULT_OK) {
            //从运费凑单页去购物车需要关闭此页面
            finish();
        }
    }

    private String getResultVoucherIds(String mVoucherIds, String ids, String shopCode) {
        Gson gson = new Gson();
        Map<String, String> mVoucherIdsMap;
        Map<String, String> idsMap;
        String newVoucherIds = mVoucherIds;
        try {
            mVoucherIdsMap = gson.fromJson(mVoucherIds
                    , new TypeToken<Map<String, String>>() {
                    }.getType());
            idsMap = gson.fromJson(ids
                    , new TypeToken<Map<String, String>>() {
                    }.getType());
            if (mVoucherIdsMap == null || mVoucherIdsMap.isEmpty()) {
                newVoucherIds = ids;
                return newVoucherIds;
            }
            //先清除掉所有店铺优惠券里的相关店铺优惠券再重新添加
            Iterator<Map.Entry<String, String>> iterator = mVoucherIdsMap.entrySet().iterator();
            while (iterator.hasNext()) {
                if (iterator.next().getValue().split(":")[0].equals(shopCode)) {
                    iterator.remove();
                }
            }
            if (idsMap == null || idsMap.isEmpty()) {
                newVoucherIds = gson.toJson(mVoucherIdsMap);
                return newVoucherIds;
            }
            //重新添加所选店铺的优惠券
            for (Map.Entry<String, String> entry : idsMap.entrySet()) {
                mVoucherIdsMap.put(entry.getKey(), entry.getValue());
            }
            newVoucherIds = gson.toJson(mVoucherIdsMap);
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return newVoucherIds;
    }

    private void getData(final int billType, final int payType, final String voucher_ids, boolean isVoucher, final boolean useBalance) {
        this.mIsVoucher = isVoucher;
        getData(billType, payType, voucher_ids, useBalance);
    }

    private void getData(final int billType, final int payType, final String voucher_ids, String peerType, final boolean useBalance) {
        this.peerType = peerType;
        getData(billType, payType, voucher_ids, useBalance);
    }

    /**
     * 发票选择
     * 1=普通发票，2=专用发票
     */
    private void showBill(PaymentBillBean bean) {

        if (TextUtils.isEmpty(bean.getBillTips())) {
            bean.setBillTips("");
        }
        tvBillTips.setText(Html.fromHtml(bean.getBillTips()));

        int invoiceType = bean.getInvoiceType();
        String invoiceText = bean.getInvoiceText();
        tvBill.setText(invoiceText);
        billType = invoiceType;
    }

    /*
     * 确认订单
     * */
    private void btnOk() {
        if (address == null || address.id == 0) {
            showAddAddress();
        } else {
            if (payType == 3 && !TextUtils.isEmpty(offlineMsg)) {
                // 线下转账并且存在多商户提示语
                new AlertDialogEx(this)
                        .setMessage(offlineMsg)
                        .setTitle("")
                        .setConfirmButton("我知道了", (dialog, button) -> {
                            payment();
                            dialog.dismiss();
                        })
                        .show();
            } else {
                payment();// 取消待确认订单二次确认
            }
        }
    }

    /*
     * 添加收货地址
     * */
    private void showAddAddress() {
        final AlertDialogEx alert = new AlertDialogEx(PaymentActivity.this);
        alert.setMessage("请添加收货地址");
        alert.setConfirmButton("去添加", (AlertDialogEx.OnClickListener) (dialog, button) -> {
            RoutersUtils.open("ybmpage://newaddress");
            getNewAddress = true;
        });
        alert.show();
    }

    @Override
    public void showProgress(String msg, boolean showMsg, boolean cancelable) {//禁止取消
        super.showProgress(msg, showMsg, false);
    }

    /*
     * 获取备注信息
     * */
    public String getRemarks() {
        String remarks = "";
        if (newList != null && newList.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            for (int i = 0; i < newList.size(); i++) {
                PaymentItemBean bean = newList.get(i);
                if (!TextUtils.isEmpty(bean.getRemarks())) {
                    try {
                        jsonObject.put(bean.getCompanyCode(), bean.getRemarks());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
            remarks = jsonObject.toString();
        }
        return remarks;
    }

    /**
     * 确认订单
     * url => "/orders"
     */
    public void payment() {
        //确认数据是否正确
        if (btnOk == null || null == address) {
            return;
        }
        btnOk.setEnabled(false);
        showProgress("提单中");
        //  确认下单上传服务器，删除本地数据库
        RequestParams params = new RequestParams();
        if (payType == 3) {
            params.put("useVirtualGold", "false");
        } else {
            if (isShowVirtualGold) {
                params.put("useVirtualGold", String.valueOf(cbVirtualMoneyOnOff.isChecked()));
                params.put("token", payWayV2ViewModel.getToken());
            } else {
                params.put("useVirtualGold", "false");
            }
        }
        if (isShowRedEnvelope) {
            params.put("useRedPacket", String.valueOf(cbRedEnvelopeOnOff.isChecked()));
        } else {
            params.put("useRedPacket", "false");
        }
        params.put("merchantId", merchant_id);
        params.put("storeStatus", "true");
        params.put("payType", payType + "");//支付类型
        if (billType > 0) {
            params.put("billType", billType + "");//发票类型
        }
        if (!TextUtils.isEmpty(peerType)) {
            params.put("peerType", peerType + "");//随货同行
        }
        if (!TextUtils.isEmpty(shoppingCartImgUUID)) {
            params.put("shoppingCartImgUUID", shoppingCartImgUUID);
        }
        if (!TextUtils.isEmpty(tranNo)) {
            params.put("tranNo", tranNo);
        }

        if (!TextUtils.isEmpty(giftId)) {
            params.put("giftIds", giftId);
        } else {
            params.put("giftIds", "-1");
        }

        params.put("version", "1.0");
        if (isKaUser) {//ka用户隐藏余额 运费 优惠券
//            params.put("useBalance", "" + false);
        } else {
//            params.put("useBalance", "" + useBalance);//是否使用余额
            if (!TextUtils.isEmpty(mVoucherIds)) {
                params.put("voucherIds", "" + mVoucherIds);
            }
        }
        params.put("useBalance", "" + false);
        if (address == null) {
            ToastUtils.showShort("请选择收货地址");
            return;
        } else {
            params.put("addressId", String.valueOf(address.id));
            if (!TextUtils.isEmpty(address.addressType))
                params.put("addressType", address.addressType);
        }
        String message = getRemarks();
        if (TextUtils.isEmpty(message)) {
            params.put("remark", "未填写备注信息");//备注
        } else {
            params.put("remark", message);//备注
        }
        if (isFromAgentOrder()) {
            params.put("purchaseNo", agentOrderBean.getPurchaseNo());
        }

        if (isFromSpellGroupOrPgby()) {
            params.put("skuId", skuId);
            params.put("productNum", productNum);
        }

        if (!TextUtils.isEmpty(notSubmitOrderOrgIds)) {
            params.put("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
        }

        //统计来源
        YBMAppLike app = (YBMAppLike) getApplication();
        if (!TextUtils.isEmpty(app.saasOrderSourcePath)) {
            params.put("saasOrderSourcePath", app.saasOrderSourcePath);
        }
        String payCode = payWayV2ViewModel.getMSelectedPayCode();
        if (!TextUtils.isEmpty(payCode)) {
            params.put("payChannelCode", payCode);
        }
        String productCredential = paymentGoodsViewModel.getGoodsLicenseJson();
        if (!TextUtils.isEmpty(productCredential)) {
            params.put("productCredential", productCredential);
        }
        String enterpriseCredential = paymentGoodsViewModel.getCompanyLicenseJson();
        if (!TextUtils.isEmpty(enterpriseCredential)) {
            params.put("corpCredential", enterpriseCredential);
        }
        //添加随心拼数据
        suiXinPinAddParams(params, false);

        if (!TextUtils.isEmpty(isPgby)) {
            params.put("isPiGou", "1");
        }
        if (!TextUtils.isEmpty(getMPayReqNo())) {
            params.put("payReqNo",getMPayReqNo());
        }

        JgRequestParams jgRequestParams = new JgRequestParams();

        if (jgEntrance != null && !jgEntrance.isEmpty()) {
//            params.put("entrance", jgEntrance);
            jgRequestParams.setEntrance(jgEntrance);
        }
        if (jgActivityEntrance != null && !jgActivityEntrance.isEmpty()) {
//            params.put("activityEntrance", jgActivityEntrance);
            jgRequestParams.setActivity_entrance(jgActivityEntrance);
        }
        if (JGTrackManager.Companion.getSuperProperty(this,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
            String searchSortStrategyCode = (String)JGTrackManager.Companion.getSuperProperty(this,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//            params.put("searchSortStrategyCode",searchSortStrategyCode);
            jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), skuId)){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }

        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(skuId))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);
                jgRequestParams.setDirect(direct);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        jgRequestParams.setSession_id(TrackManager.getSessionId(YBMAppLike.getAppContext()));
        params.put("mddata",new Gson().toJson(jgRequestParams));
        if (jgEntrance!= null && jgEntrance.contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            JgRequestParams mJgRequestParams = new JgRequestParams();
            mJgRequestParams.setSession_id(TrackManager.getSessionId(YBMAppLike.getAppContext()));
            mJgRequestParams.setDirect("3");
            params.put("mddata",new Gson().toJson(mJgRequestParams));
        }
        //添加埋点qtdata
        trackOrderConfirmParams(this, params);
        if(!TextUtils.isEmpty(bizProducts)){
            params.put("bizProducts",bizProducts);
        }
        HttpManager.getInstance().post(isFromAgentOrder() ? AppNetConfig.ORDER_SPROUT_V2_ORDERCONFIRM : (isFromSpellGroupOrPgby() ? AppNetConfig.ORDER_V1_GROUPPURCHASEORDERCONFIRM : AppNetConfig.ORDER_V1_ORDERCONFIRM), params, new BaseResponse<PaymentBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                btnOk.setEnabled(true);
            }

            @Override
            public void onSuccess(String content, BaseBean<PaymentBean> data, PaymentBean paymentBean) {
                if (null != data && data.isSuccess()) {

                    // 如果有更合适的优惠券，则先刷新页面，然后跳转优惠券页面
                    if (paymentBean.status == 40000) {
                        showCouponAvailableDialog(paymentBean.title, paymentBean.msg);
                        btnOk.setEnabled(true);
                        return;
                    }
                    if (agentOrderBean != null)
                        EventBusUtil.sendEvent(new Event<String>(IntentCanst.RX_BUS_AGENT_ORDER_CONFIRM_ORDER, agentOrderBean.getId()));
                    // 结算订单删除数据库
                    order = paymentBean.getOrder();
                    setMOrderId(order.id + "");
                    setMOrderNo(order.orderNo);
                    setMPayRoute("0");
                    maxCount = paymentBean.retryTimes;//次数
                    stepTime = paymentBean.retryInterval;
                    jumpType = paymentBean.callType;
                    HandlerGoodsDao.getInstance().deleteItems();//目前全部清空用户下的商品
                    // 提交成功，广播通知购物车刷新
                    LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));
                    LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
//                    if (stepTime > 0) {//当时间大于0,等待时间后查询
//                        queryOrder(1);
//                    } else {
//                        toPay();
//                    }
                    queryOrder(1);
                    // 提交订单埋点
                    XyyIoUtil.track(XyyIoUtil.ACTION_SUBMITORDER);



                } else {
                    dismissProgress();
                    btnOk.setEnabled(true);
                    if (isFromAgentOrder()) finish();
                }
            }
        });
    }


    /**
     * 是否选中最优优惠券信息
     */
    private void showCouponAvailableDialog(String title, String msg) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setTitle(title)
                .setMessage(msg)
                .setCancelButton("放弃", (dialog, button) -> {
                    // 不选择优惠券则继续支付
                    payment();
                })
                .setCancelButtonTextColor(UiUtils.getColor(R.color.text_cancel))
                .setConfirmButton("确认", (dialog, button) -> {
                    dismissProgress();
                    isRefreshAfterCouponNotification = true;
                    isFirstRefreshAfterCouponNotification = true;
                    getData(billType, 0, mVoucherIds, true);
                })
                .setCanceledOnTouchOutside(false)
                .show();
    }

    /*
     * 提单后的去支付
     * */
    private void toPay() {
        dismissProgress();

        if (isVirtualGoldRechargePay()){ //购物金流程完成
            RoutersUtils.open("ybmpage://myorderlist/0");
            finish();
        }

        if (payType == 1) {
            //在线支付
            if (jumpType == 2) {
                RoutersUtils.open("ybmpage://myorderlist/0");
                finish();
                return;
            } else if (jumpType == 3) {
                showProgress();
                payWayV2ViewModel.getPayType(isUseVirtualMoney());
                return;
            }
        } else if (payType == 3) {
            //线下转账
            if (jumpType == 1) {
                RoutersUtils.open("ybmpage://payresultactivity/" + order.id + "/" + YBMPayUtil.PAY_TRAN + "/" + order.money + "/" + order.orderNo);
                finish();
                return;
            } else if (jumpType == 2) {
                RoutersUtils.open("ybmpage://myorderlist/0");
                finish();
                return;
            }
        } else if (payType == 6) {
            //他人代付
            if (jumpType == 1) {
                RoutersUtils.open("ybmpage://payforanother/" + order.orderNo);
                finish();
                return;
            } else if (jumpType == 2) {
                RoutersUtils.open("ybmpage://myorderlist/0");
                finish();
                return;
            }
        }
        RoutersUtils.open("ybmpage://myorderlist/0");
        finish();
//
//        if (payType == 1) {
//            RoutersUtils.open("ybmpage://paywayactivity?orderId=" + order.id + "&amount=" + order.money + "&orderNo=" + order.orderNo + "&payRoute=0");
//        } else if (payType == 2) {
//            RoutersUtils.open("ybmpage://payresultactivity/" + order.id + "/" + YBMPayUtil.PAY_PAD + "/" + order.money + "/" + order.orderNo);
//        } else if (payType == 3) {
//            RoutersUtils.open("ybmpage://payresultactivity/" + order.id + "/" + YBMPayUtil.PAY_TRAN + "/" + order.money + "/" + order.orderNo);
//        } else if (payType == 6) {//他人代付
//            RoutersUtils.open("ybmpage://payforanother/" + order.orderNo);
//        }
    }

    private void toTimeOut() {
        dismissProgress();
        final AlertDialogEx alert = new AlertDialogEx(PaymentActivity.this);
        alert.setCancelable(false);
        alert.setAutoDismiss(true);
        alert.setCanceledOnTouchOutside(false);
        alert.setTitle("订单创建中");
        alert.setMessage("部分商品库存发生变化，请您返回购物车确认后再提交");
        alert.setCancelButton("我知道了", (dialog, button) -> {
            dismissProgress();
            RoutersUtils.open("ybmpage://main/2");
            finish();
        });

//        alert.setConfirmButton("立即支付", (AlertDialogEx.OnClickListener) (dialog, button) -> {
//            showProgress("订单创建中");
//            queryOrder(1);
//        });
        alert.show();
    }

    private void queryOrder(final int count) {
        if (count > maxCount) {//次数到了，显示超时对方
            toTimeOut();
            return;
        }
        if (btnOk != null) {
            btnOk.postDelayed(() -> {
                if (btnOk == null) {
                    return;
                }
                if (params == null) {
                    params = new RequestParams();
                    params.put("merchantId", merchant_id);
                    params.put("orderNo", order.orderNo + "");
                }
                HttpManager.getInstance().post(AppNetConfig.ORDER_V1_FINDORDER, params, new BaseResponse<QueryOrderBean>() {
                    @Override
                    public void onFailure(NetError error) {
                        queryOrder(count + 1);
                    }

                    @Override
                    public void onSuccess(String content, BaseBean<QueryOrderBean> data, QueryOrderBean orderBean) {
                        if (data != null && data.isSuccess() && orderBean != null && orderBean.id > 0) {
                            order.setId(orderBean.id);
                            setMOrderId(orderBean.id + "");
                            showProgress("提单成功");
                            toPay();
                        } else {
                            queryOrder(count + 1);
                        }
                    }
                });
            }, count > 2 ? 100 : stepTime);
        } else {
            finish();
        }
    }

    /**
     * 显示收货人姓名地址等信息
     *
     * @param bean 地址bean
     */
    private void showAddressInfo(AddressListBean bean) {
        if (bean == null) {
            return;
        }
        String userName = bean.getContactor() + " " + bean.getMobile();
        tvUserName.setText(userName);
        String address = bean.getFullAddress();
        tvAddress.setText(address);
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
            /*隐藏软键盘*/
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null && inputMethodManager.isActive()) {
                if (PaymentActivity.this.getCurrentFocus() != null) {
                    inputMethodManager.hideSoftInputFromWindow(PaymentActivity.this.getCurrentFocus().getWindowToken(), 0);
                }
            }

            String trim = paymentMessageLeaveEt.getText().toString().trim();
            paymentMessageLeaveEt.setText(trim);
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @OnClick({R.id.ll_address, R.id.ll_bill, R.id.btn_ok, R.id.payment_message, R.id.iv_balance, R.id.btn_reject, R.id.ll_order_coupon})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ll_address:
                updateAddress();
                break;
            case R.id.ll_bill:
                invoiceinformPop = new InvoiceinformPopWindow();
                invoiceinformPop.setPeerType(peerType);
                invoiceinformPop.setBillType(billType);
                invoiceinformPop.setOnSelectListener(new BaseBottomPopWindow.OnSelectListener() {
                    @Override
                    public void getValue(SearchFilterBean show) {

                        try {
                            String billTypeStr = show.realName;
                            billType = Integer.parseInt(billTypeStr);
                        } catch (NumberFormatException e) {
                            billType = 0;
                        }

                        String peerType = show.id;
                        getData(billType, payType, mVoucherIds, peerType, useBalance);

                    }

                    @Override
                    public void OnDismiss() {

                    }
                });
                invoiceinformPop.show(llBill);
                break;
            case R.id.btn_ok:
                trackSubmitOrderClick(btnOk.getText().toString());
                btnClickJgTrack();

                if (!getIsFromCart() && mPaymentNewBean != null && mPaymentNewBean.getCart() != null) {
                    if (mPaymentNewBean.getCart().getNeedToBePerfectedActList()!=null && mPaymentNewBean.getCart().getNeedToBePerfectedActList().size()>0){
                        int bizSource = mPaymentNewBean.getCart().getBizSource();
                        GiftSelectBottomDialog giftSelectBottomDialog ;
                        if(mPaymentNewBean.getCart().getNeedToBePerfectedActList().size()==1){
                            CartBean.NeedToBePerfectedActBean bean = mPaymentNewBean.getCart().getNeedToBePerfectedActList().get(0);
                            giftSelectBottomDialog = new GiftSelectBottomDialog(this,bean.getGiftPoolActTotalSelectedNum(),bean.getPromoId(),bizSource);
                        }else {
                            giftSelectBottomDialog = new GiftSelectBottomDialog(this,(ArrayList<CartBean.NeedToBePerfectedActBean>) mPaymentNewBean.getCart().getNeedToBePerfectedActList(),bizSource);
                        }

                        giftSelectBottomDialog.setConfirmClickCallBack(() -> {
                            toDoBtnOkClick();
                            return null;
                        });
                        giftSelectBottomDialog.setCloseCallBack(() -> {
                            getData(billType, 0, mVoucherIds, true);//第一次 paytype = 0;  全部刷新
//                            toDoBtnOkClick();
                            return null;
                        });
                        giftSelectBottomDialog.show();
                        return ;
                    }
                }
                toDoBtnOkClick();
                break;
            case R.id.iv_balance:
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.BALANCE_EXPLAIN);
                break;
            case R.id.btn_reject:
                //驳回
                AgentOrderRejectDialog dialog = new AgentOrderRejectDialog(this, agentOrderBean.getId(), agentOrderBean.getPurchaseNo());
                dialog.setOnRejectListener(s -> {
                    EventBusUtil.sendEvent(new Event<AgentOrderListRowBean>(RX_BUS_AGENT_ORDER_REJECT_ORDER, agentOrderBean));
                    finish();
                    return null;
                });
                dialog.show();
                break;
            case R.id.ll_order_coupon://选择优惠券
                updateVoucher();
                break;
        }
    }

    private void btnClickJgTrack() {
        HashMap<String,Object> params = new HashMap<>();
        params.put(JGTrackManager.FIELD.FIELD_PAGE_ID,JGTrackManager.TrackOrderSubmitDetail.PAGE_ID);
        params.put(JGTrackManager.FIELD.FIELD_TITLE,JGTrackManager.TrackOrderSubmitDetail.TITLE);
        if (btnOk!=null && btnOk.getText() != null){
            params.put(JGTrackManager.FIELD.FIELD_BTN_NAME,btnOk.getText().toString());
        }
        JGTrackManager.Companion.eventTrack(this,JGTrackManager.TrackOrderSubmitDetail.EVENT_BTN_CLICK,params);
    }

    private void toDoBtnOkClick(){
        setCheckedPwForVirtualGold(false);
        if (isUseVirtualMoney()) {
            if (payType == 3) {
                ToastUtils.showLong("线下转账暂不支持购物金抵扣，订单金额已发生变化，请确认");
                return;
            }
            showProgress();
            payWayV2ViewModel.queryPayPWSettingStatus();
        } else if (payWayV2ViewModel.isSelectedBankCard()) {
            //选中银行卡
            showProgress();
            payWayV2ViewModel.queryPayPWSettingStatus();
//                    payWayV2ViewModel.queryPWSettingStatus();
        } else {
            checkSubmitOrder();
        }
    }

    @Override
    public void checkSubmitOrder() {
        Pair<String, String> pairParams = payWayV2ViewModel.getSelectedPayRouter(bindFromKey(), null);
         if (payType == 5) {
            //自有账期二次提示
            AlertDialogEx dialogEx = new AlertDialogEx(this);
            dialogEx.setTitle("温馨提示");
            dialogEx.setMessage("您当前所选支付方式为【自有账期】，提交订单后将会自动支付并出库，请确认是否继续提交？");
            dialogEx.setCancelButton("取消", (AlertDialogEx.OnClickListener) (dialog, button) -> {})
                    .setConfirmButton("确定", (AlertDialogEx.OnClickListener) (dialog, button) -> btnOk())
                    .show();
        } else if (mPaymentNewBean!=null && mPaymentNewBean.getSettle() != null && mPaymentNewBean.getSettle().getPayAmount() == 0) {
             payWayV2ViewModel.getPayType(isUseVirtualMoney());
        } else if (pairParams != null) {
             if (TextUtils.isEmpty(pairParams.getFirst())) {
                 if (!TextUtils.isEmpty(pairParams.getSecond())) {
                     RoutersUtils.open(pairParams.getSecond());
                 }
             } else {
                 AlertDialogHtml.showAlertDialogAuthorization(this,
                         pairParams.getFirst() == null? "": pairParams.getFirst(),
                         pairParams.getSecond() == null? "": pairParams.getSecond(),
                         s -> {
                             RoutersUtils.open(s);
                             return null;
                         });
             }
         } else if (isUseVirtualMoney()) {
             payWayV2ViewModel.getPayType(isUseVirtualMoney());
         } else {
            btnOk();
        }
    }

    /**
     * 设置选择优惠券的参数
     *
     * @param paymentCompanyListBean
     */
    private void setSelectCouponParams(PaymentCompanyListBean paymentCompanyListBean) {
        //重新设置优惠id
        mVoucherIds = paymentCompanyListBean.getSelectVoucherIds();
        skus = paymentCompanyListBean.getSkus();
        xyyMoneyForVoucherCheck = paymentCompanyListBean.getXyyMoneyForVoucherCheck();
    }

    /**
     * 更改优惠券
     */
    private void updateVoucher() {
        String routerUrl = "ybmpage://voucheravailable?selectVoucherIds=" + mVoucherIds + "&skus=" + skus + "&/:xyyMoneyForVoucherCheck=" + xyyMoneyForVoucherCheck;
        if (agentOrderBean != null) {//代下单过来的
            routerUrl = routerUrl + "&isFromAgentOrder=true&purchaseNo=" + agentOrderBean.getPurchaseNo();
        }
        RoutersUtils.openForResult(routerUrl, REQUESTCODE_TO_AVAILABLE_VOUCHER);
    }

    private Handler paymentHandler = new Handler();
    private Runnable paymentRunnable;

    @Override
    protected void onResume() {
        super.onResume();
        if (paymentRunnable != null) {
            paymentHandler.removeCallbacks(paymentRunnable);
        }
        paymentRunnable = () -> {
            if (isVirtualGoldRechargePay() || !TextUtils.isEmpty(getMOrderId())) {
                String tempPayRoute = "";
                if (!TextUtils.isEmpty(getMPayRoute())) {
                    tempPayRoute = getMPayRoute();
                }
                payWayV2ViewModel.getPayResultQueryParams(getMOrderId(), getMOrderNo(), tempPayRoute, getReqScene());
            }
        };
        paymentHandler.postDelayed(paymentRunnable, 300);
        if (getNewAddress) {
            getData(billType, payType, mVoucherIds, useBalance);
            getNewAddress = false;
        }
        if (rvProduct != null) {
            rvProduct.setFocusable(false);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (paymentRunnable != null) {
            paymentHandler.removeCallbacks(paymentRunnable);
        }
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_GIFT_PAYMENT);
        intentFilter.addAction(IntentCanst.ACTION_GIFT_PAYMENT_2);
        LocalBroadcastManager.getInstance(this.getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_GIFT_PAYMENT.equals(action)) {
                String id = intent.getStringExtra("giftId");
                if (!TextUtils.isEmpty(id)) {

                    if (id.equals(giftId)) {
                        giftId = "";
                    }
                }
                getData(billType, payType, mVoucherIds, useBalance);
            } else if (IntentCanst.ACTION_GIFT_PAYMENT_2.equals(action)) {
                String id = intent.getStringExtra("giftId");
                if (!TextUtils.isEmpty(id)) {
                    giftId = id;
                }
                getData(billType, payType, mVoucherIds, useBalance);
            }
        }
    };


    @Override
    protected void onDestroy() {
        super.onDestroy();
        ButterKnife.unbind(this);
        paymentGoodsViewModel.clearData();
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(this.getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }

    /**
     * 是否来自代下单
     *
     * @return true 来自代下单
     */
    private boolean isFromAgentOrder() {
        return agentOrderBean != null;
    }

    /**
     * 拼团或者批购包邮
     *
     * @return true 拼团
     */
    private boolean isFromSpellGroupOrPgby() {
        return !TextUtils.isEmpty(skuId) && !TextUtils.isEmpty(productNum);
    }

    @Override
    public void switchBankCard() {
        super.switchBankCard();
        getData(billType, payType, mVoucherIds, useBalance);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            RoutersUtils.open(mViewModel.getFinishRouter(SpellGroupRecommendGoodsViewModelKt.CURRENT_PAGE_PAYMENT, new HashMap<>()));
        }
        return true;
    }

    @NonNull
    @Override
    public PayWayV2ViewModel getViewModel() {
        return payWayV2ViewModel;
    }

    @NonNull
    @Override
    public String getReqScene() {
        return "settle";
    }

    @NonNull
    @Override
    public String bindFromKey() {
        return BIND_RESULT_FROM_PAYMENT;
    }

    @Override
    public void jumpSetPw() {
        super.jumpSetPw();
        RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=" + SET_PAY_PASSWORD_SETTING_PAY, 100);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        try {
            String bindResultFrom = intent.getStringExtra("bindResultFrom");
            if (!TextUtils.isEmpty(bindResultFrom)) {
                try {
                    String status = intent.getStringExtra("status");
                    if (TextUtils.equals(status, "4")) {
                        return;
                    } else {
                        if (!TextUtils.equals(status, "2")) {
                            return;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        isPassCardId = false;

        String status = "";
        try {
            status = intent.getStringExtra("status");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //绑卡成功
        if (TextUtils.equals(status, "2")) {
            mTempPayCode = "jdCardPay";
            mPayId = null;
        }
        getData(billType, payType, mVoucherIds, useBalance);
    }

    @Override
    public void onClosePayPWDialogCallback() {
        super.onClosePayPWDialogCallback();
        if (isUseVirtualMoney()) return;
        RoutersUtils.open("ybmpage://myorderlist/0");
        finish();
    }

    @Override
    public boolean handleSetPassword() {
        RoutersUtils.open("ybmpage://myorderlist/0");
        finish();
        return true;
    }

    @Override
    public void updateSelectPayType(@Nullable PayTypeBankCard payTypeBankCard) {
        super.updateSelectPayType(payTypeBankCard);
        if (payTypeBankCard == null) return;
        bottomView.setData(new PaymentBottomPayItemShowView.PayItemShowBean(
               payTypeBankCard.getLogoUrl() , payTypeBankCard.getPayTypeMktTips(), payTypeBankCard.getBankShowName(), new SpannableStringBuilder(payTypeBankCard.getMktTip()!=null? payTypeBankCard.getMktTip(): "")
        ));
    }

    /**
     * 是否使用购物金
     * @return
     */
    public boolean isUseVirtualMoney() {
        if (clShoppingGold == null || cbVirtualMoneyOnOff == null){
            return false;
        }
        return clShoppingGold.getVisibility() == View.VISIBLE && cbVirtualMoneyOnOff.isChecked() && virtualGold != 0;
    }

    /**
     * 是否走购物金 充值流程
     * @return
     */
    public boolean isNeedVirtualMoneyRecharge() {
        return isUseVirtualMoney() && (getMCurrentVirtualGoldRechargeBean()!=null);
    }

    @NonNull
    @Override
    public String getTranNo() {
        return tranNo;
    }

    @Override
    public void toOrderForVirtualMoney() {
        super.toOrderForVirtualMoney();
        payment();
    }
}
