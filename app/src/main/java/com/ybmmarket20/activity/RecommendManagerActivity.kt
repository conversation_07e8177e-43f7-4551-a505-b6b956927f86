package com.ybmmarket20.activity

import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.SpUtil
import kotlinx.android.synthetic.main.activity_recommend_manager.*

/**
 * 推荐
 */
@Router("recommendmanageractivity")
class RecommendManagerActivity: BaseActivity() {
    override fun getContentViewId(): Int = R.layout.activity_recommend_manager

    override fun initData() {
        setTitle("推荐管理")
        sc_privacy.isChecked = SpUtil.readBoolean("recommendPrivacySwitch", true)
        sc_privacy.setOnCheckedChangeListener { _, isChecked ->
            SpUtil.writeBoolean("recommendPrivacySwitch", isChecked)
        }
    }
}