package com.ybmmarket20.activity;

import androidx.recyclerview.widget.SimpleItemAnimator;
import android.text.TextUtils;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.OrderDetailtAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RefundProduct;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.widget.RoundLinearLayout;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 退货订单退货商品明细列表
 */
@Router({"refunddetailproductlist", "refunddetailproductlist/:refundOrderId", "refunddetailproductlist/:refundOrderId/:isSmallPayment"})
public class RefundDetailProductListActivity extends BaseActivity {

    @Bind(R.id.rv_product)
    CommonRecyclerView rvProduct;
    private List<RefundProductListBean> rowsBeans = new ArrayList<>();
    private int pageSize = 1000;
    private int total = 0;
    private String refundOrderId;
    private boolean isSmallPayment;
    protected YBMBaseMultiItemAdapter adapter;

    @Override
    protected void initData() {
        setTitle("退款商品明细");
        refundOrderId = getIntent().getStringExtra("refundOrderId");
        isSmallPayment = TextUtils.equals(getIntent().getStringExtra("isSmallPayment"), "1");
        if (TextUtils.isEmpty(refundOrderId)) {
            ToastUtils.showShort("参数错误");
            finish();
            return;
        }
        adapter = new OrderDetailtAdapter(isSmallPayment, rowsBeans, false, false, false, true) {
            @Override
            protected void bindItem(YBMBaseHolder baseViewHolder, RefundProductListBean bean) {
                super.bindItem(baseViewHolder, bean);
                RoundLinearLayout itemRoot = baseViewHolder.getView(R.id.root);
                if (bean.getItemType() == RefundProductListBean.ITEMTYPE_CONTENT && bean.extraGift == 0 && bean.extraGiftVariety > 0) {
                    itemRoot.setCornerRadiusWithDp(7, 7, 0, 0);
                } else {
                    itemRoot.setCornerRadiusWithDp(7, 7, 7, 7);
                }

            }
        };
        rvProduct.setAdapter(adapter);
        rvProduct.setEmptyView(R.layout.empty_view_product);
        rvProduct.setEnabled(false);
        rvProduct.setLoadMoreEnable(false);
        rvProduct.setShowAutoRefresh(false);
        ((SimpleItemAnimator) rvProduct.getRecyclerView().getItemAnimator()).setSupportsChangeAnimations(false);
        getData();
    }

    private void getData() {
        showProgress();
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.REFUND_DETAIL_LIST).addParam("refundOrderId", refundOrderId).addParam("limit", pageSize + "").addParam("merchantId", merchant_id).build();
        HttpManager.getInstance().post(params, new BaseResponse<RefundProduct>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundProduct> data, RefundProduct bean) {
                dismissProgress();
                if (data != null && data.isSuccess()) {//设置数据
                    setData(bean);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
            }
        });

    }

    public void setData(RefundProduct bean) {
        if (bean == null) {
            return;
        }
        rowsBeans = new ArrayList<RefundProductListBean>();
        //添加套餐商品
        if (bean.activityPackageList != null && bean.activityPackageList.size() > 0) {
            RefundProductListBean productListBean = new RefundProductListBean();
            total += bean.activityPackageList.size();
            for (int a = 0; a < bean.activityPackageList.size(); a++) {
                productListBean = new RefundProductListBean();
                productListBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE);
                productListBean.productId = bean.activityPackageList.get(a).id;
                productListBean.productName = bean.activityPackageList.get(a).title;
                productListBean.productAmount = bean.activityPackageList.get(a).packageCount + "";
                rowsBeans.add(productListBean);
                if (bean.activityPackageList.get(a).orderRefundVOList != null && bean.activityPackageList.get(a).orderRefundVOList.size() > 0) {
                    for (RefundProductListBean listBean : bean.activityPackageList.get(a).orderRefundVOList) {
                        listBean.subtotal = StringUtil.DecimalFormat2Double(listBean.productPrice * Integer.parseInt(listBean.productAmount));
                        listBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT);
                        rowsBeans.add(listBean);
                    }
                }
            }
        }
        //添加普通商品
        if (bean.refundOrderDetailList != null) {
            if (bean.refundOrderDetailList.rows != null && bean.refundOrderDetailList.rows.size() > 0) {
                total += bean.refundOrderDetailList.rows.size();
                for (RefundProductListBean listBean : bean.refundOrderDetailList.rows) {
                    listBean.subtotal = StringUtil.DecimalFormat2Double(listBean.productPrice * Integer.parseInt(listBean.productAmount));
                    if (listBean.extraGift == 1) listBean.setItemType(RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT);
                    rowsBeans.add(listBean);
                }
            }
        }
        if (adapter instanceof OrderDetailtAdapter) {
            ((OrderDetailtAdapter) adapter).isSmallPayment = isSmallPayment;
        }
        adapter.setNewData(rowsBeans);
        setTitleRight();
    }


    private void setTitleRight() {
        setRigthText(total + "件");
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_order_product_list;
    }

}
