package com.ybmmarket20.activity;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import android.content.Intent;
import android.widget.LinearLayout;

import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.fragments.VoucherAvailableFragment;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 可用优惠券
 */
@Router({"voucheravailable", "voucheravailable/:selectVoucherIds/:skus/:xyyMoneyForVoucherCheck", "voucheravailable/:selectVoucherIds/:skus/:xyyMoneyForVoucherCheck/:isFromAgentOrder/:purchaseNo"})
public class VoucherAvailableActivity extends BaseActivity {

    @Bind(R.id.ps_tab)
    SlidingTabLayout mPsTab;
    @Bind(R.id.vp_client)
    ViewPager mVpClient;

    private ArrayList<String> mList_title;
    private String purchaseNo;//代下单采购单编号
    private boolean isFromAgentOrder;//是否代下单过来的
    private String skus;//商品id:小计,商品id:小计,
    private String selectVoucherIds;//选中优惠券列表
    private String price;//用来校验自营叠加券的自营店铺金额

    @Override
    protected void initData() {
        setTitle("选择优惠券");
        Intent intent = getIntent();
        if (intent != null) {
            selectVoucherIds = intent.getStringExtra("selectVoucherIds");
            skus = intent.getStringExtra("skus");
            price = intent.getStringExtra("xyyMoneyForVoucherCheck");
            isFromAgentOrder = "true".equals(intent.getStringExtra("isFromAgentOrder"));
            purchaseNo = intent.getStringExtra("purchaseNo");
        }
        initFragmentTitle();
        VoucherAdapter adapter = new VoucherAdapter(getSupportFragmentManager(), mList_title);
        mVpClient.setAdapter(adapter);
        mVpClient.setOffscreenPageLimit(mList_title.size() + 1);
        mPsTab.setIndicatorWidthEqualTitleHalf(true);
        mPsTab.setViewPager(mVpClient);
        LinearLayout linearLayout = (LinearLayout) mPsTab.getChildAt(0);
        linearLayout.setShowDividers(LinearLayout.SHOW_DIVIDER_MIDDLE);
        linearLayout.setDividerPadding(ConvertUtils.dp2px(11));
        linearLayout.setDividerDrawable(ContextCompat.getDrawable(this, R.drawable.layout_divider_vertical));
    }

    private void initFragmentTitle() {
        mList_title = new ArrayList<>();
        mList_title.add("可用优惠券");
        mList_title.add("不可用优惠券");
//        for (int a = 0; a < mList_title.size(); a++) {
//            mPsTab.addTab(mPsTab.newTab().setText(mList_title.get(a)));
//        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_voucher_available;
    }

    private class VoucherAdapter extends FragmentPagerAdapter {

        private List<Fragment> list_fragment = new ArrayList<>();     //fragment列表
        private List<String> list_Title;                              //tab名的列表

        public VoucherAdapter(FragmentManager fm, List<String> list_Title) {
            super(fm);
            this.list_Title = list_Title;
        }

        @Override
        public Fragment getItem(int position) {

            if (list_fragment == null || list_fragment.isEmpty() || list_fragment.size() <= position || list_fragment.get(position) == null) {
                if (list_fragment == null) {
                    list_fragment = new ArrayList<>();
                }
                if (list_fragment.size() > position && list_fragment.get(position) == null) {
                    list_fragment.remove(position);
                }
                Fragment fragment = VoucherAvailableFragment.getInstance(position, skus, selectVoucherIds, price, isFromAgentOrder, purchaseNo);
                list_fragment.add(position, fragment);
            }
            return list_fragment.get(position);
        }

        @Override
        public int getCount() {
            return list_Title.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return list_Title.get(position);
        }
    }

}
