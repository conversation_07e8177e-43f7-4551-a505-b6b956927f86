package com.ybmmarket20.activity;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;

import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.fragments.WishFragment;
import com.ybmmarket20.utils.RoutersUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 心愿单 废弃掉
 */
@Deprecated
@Router("wishactivity")
public class WishActivity extends BaseActivity {

    @Bind(R.id.vp_client)
    ViewPager vpClient;
    @Bind(R.id.ps_tab)
    SlidingTabLayout psTab;

    private ArrayList<String> mListTitle;

    @Override
    protected void initData() {
        setTitle("我的心愿单");
        initFragmentTitle();
        WishListAdapter adapter = new WishListAdapter(getSupportFragmentManager(), mListTitle);
        vpClient.setAdapter(adapter);
        vpClient.setOffscreenPageLimit(3);
        psTab.setViewPager(vpClient);
//        UiUtils.reflex(psTab,ConvertUtils.dp2px(30));
//        LinearLayout linearLayout = (LinearLayout) psTab.getChildAt(0);
//        linearLayout.setShowDividers(LinearLayout.SHOW_DIVIDER_MIDDLE);
//        linearLayout.setDividerPadding(ConvertUtils.dp2px(11));
//        linearLayout.setDividerDrawable(ContextCompat.getDrawable(this,
//                R.drawable.layout_divider_vertical));
    }

    @OnClick({R.id.btn_wish})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.btn_wish:
                hideSoftInput();
                RoutersUtils.open("ybmpage://addwishactivity");
                break;
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_wish;
    }

    private void initFragmentTitle() {
        mListTitle = new ArrayList<>();
        mListTitle.add("已完成");
        mListTitle.add("搜集中");
        mListTitle.add("未完成");
//        for (int a = 0; a < mListTitle.size(); a++) {
//            psTab.addTab(psTab.newTab().setText(mListTitle.get(a)));
//        }
    }

    private class WishListAdapter extends FragmentPagerAdapter {
        private List<Fragment> listFragment = new ArrayList<>();     //fragment列表
        private List<String> listTitle;                              //tab名的列表

        public WishListAdapter(FragmentManager fm, List<String> list_Title) {
            super(fm);
            this.listTitle = list_Title;
        }

        @Override
        public Fragment getItem(int position) {
            if (listFragment == null || listFragment.isEmpty() || listFragment.size() <= position || listFragment.get(position) == null) {
                if (listFragment == null) {
                    listFragment = new ArrayList<>();
                }
                if (listFragment.size() > position && listFragment.get(position) == null) {
                    listFragment.remove(position);
                }
                WishFragment fragment = WishFragment.getInstance(position);
                listFragment.add(position, fragment);
            }
            return listFragment.get(position);
        }

        @Override
        public int getCount() {
            return listTitle.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return listTitle.get(position);
        }
    }


}
