package com.ybmmarket20.activity;

import android.content.Intent;
import android.view.View;
import android.widget.ImageView;

import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.home.MainActivity;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 提交心愿单成功
 */
public class WishSucceedActivity extends BaseActivity {

    @Bind(R.id.wish_succeed_iv)
    ImageView wishSucceedIv;

    @Override
    protected void initData() {
        setTitle("药品心愿单");
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_wish_succeed;
    }

    @OnClick({R.id.wish_succeed_commit_btn})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.wish_succeed_commit_btn:
                //跳转首页
                Intent intent = new Intent(this, MainActivity.class);
                intent.putExtra("comment", "0");
                startActivity(intent);
                finish();
                break;
        }
    }
}
