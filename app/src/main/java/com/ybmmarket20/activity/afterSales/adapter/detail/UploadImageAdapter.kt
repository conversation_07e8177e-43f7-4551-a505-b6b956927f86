package com.ybmmarket20.activity.afterSales.adapter.detail

import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import com.luck.picture.lib.PictureNetPreviewActivity
import com.luck.picture.lib.PictureNetPreviewActivity.PICTURE_NET_PREVIEW_PATH_LIST
import com.luck.picture.lib.PictureNetPreviewActivity.PICTURE_NET_PREVIEW_PATH_POSITION
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMMultiViewAdapter
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.ImageLoader
import java.io.Serializable

/**
 * 上传凭据
 */
class UploadImageAdapter(private val multiData: List<String>) :
    YBMMultiViewAdapter<String>(R.layout.item_after_sales_image, multiData) {

    override fun bindMultiView(holder: Y<PERSON><PERSON><PERSON>Holder, bean: String) {
        val iv = holder.itemView as ImageView
        ImageLoader.loadImage(mContext, iv, bean)
        holder.itemView.setOnClickListener {
            val bundle = Bundle()
            bundle.putStringArrayList(PICTURE_NET_PREVIEW_PATH_LIST, ArrayList(multiData))
            bundle.putInt(PICTURE_NET_PREVIEW_PATH_POSITION, holder.bindingAdapterPosition)
            val intent = Intent(mContext, PictureNetPreviewActivity::class.java).apply {
                this.putStringArrayListExtra(PICTURE_NET_PREVIEW_PATH_LIST, ArrayList(multiData))
                this.putExtra(PICTURE_NET_PREVIEW_PATH_POSITION, holder.bindingAdapterPosition)
            }
            mContext.startActivity(intent)
            (mContext as BaseActivity).overridePendingTransition(R.anim.a5, 0)
        }
    }
}