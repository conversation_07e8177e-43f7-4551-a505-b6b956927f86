package com.ybmmarket20.adapter;

import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.Group;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.RangePriceBeanKt;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.GoodsUtilsKt;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品详情列表
 */

public class CommodityGridAdapter extends YBMBaseAdapter<RowsBean> {

    private List<RowsBean> dataList;

    private JgTrackBean jgTrackBean;

    public CommodityGridAdapter(List<RowsBean> items, int page, int pageNum) {
        super(R.layout.fragment_commodity_item, new ArrayList<RowsBean>());
        initData(items, page, pageNum);
    }

    public void setJgTrackBean(JgTrackBean jgTrackBean) {
        this.jgTrackBean = jgTrackBean;
    }

    public void initData(List<RowsBean> items, int page, int pageNum) {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        dataList.clear();
        //start end分别代表要显示的数组在总数据List中的开始和结束位置
        int start = page * pageNum;
        int end = start + pageNum;
        while ((start < items.size()) && (start < end)) {
            dataList.add(items.get(start));
            start++;
        }
        super.setNewData(dataList);
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {

        ImageView icon = ybmBaseHolder.getView(R.id.icon);
        TextView tv_name_recommend = ybmBaseHolder.getView(R.id.tv_name_recommend);
        TextView tv_description_recommend = ybmBaseHolder.getView(R.id.tv_description_recommend);
        TextView tv_recommend_price = ybmBaseHolder.getView(R.id.tv_recommend_price);
        TextView tv_unit_price = ybmBaseHolder.getView(R.id.tv_unit_price);
        TextView tv_recommend_mask_layer = ybmBaseHolder.getView(R.id.tv_recommend_mask_layer);

        setAct(ybmBaseHolder, rowsBean);

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl())
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .placeholder(R.drawable.jiazaitu_min).into(icon);
        tv_name_recommend.setText(rowsBean.getShowName());
        tv_description_recommend.setText(rowsBean.getSpec());
        if (RangePriceBeanKt.isStep(rowsBean.rangePriceBean) && rowsBean.rangePriceBean != null && !TextUtils.isEmpty(rowsBean.rangePriceBean.getMinSkuPrice())) {
            tv_recommend_price.setText("¥" + UiUtils.transform(rowsBean.rangePriceBean.getMinSkuPrice()) + " 起");
        } else {
            //商品区间价格
            tv_recommend_price.setText(UiUtils.showProductPrice(rowsBean));
        }
        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            tv_recommend_price.setText(String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
        }
        //是否控销  isControl=1表示控销，=2表示不是控销
        if (rowsBean.getIsControl() == 1) {

            if (rowsBean.isPurchase()) {
                //控销可购买
                if (rowsBean.getPriceType() == 1) {
                    tv_recommend_price.setText(String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
                } else {
                    tv_recommend_price.setText(UiUtils.showProductPrice(rowsBean));
                }

                //是否是OEM协议商品
                setIsOEM(ybmBaseHolder, rowsBean);
            } else {
                //控销不可购买
                tv_recommend_price.setText("暂无购买权限");
            }
        } else {

            //是否是OEM协议商品
            setIsOEM(ybmBaseHolder, rowsBean);
        }

        // 设置单价
        if (!TextUtils.isEmpty(rowsBean.getUnitPrice())) {
            tv_unit_price.setText(rowsBean.getUnitPrice());
            tv_unit_price.setVisibility(View.VISIBLE);
        } else {
            tv_unit_price.setVisibility(View.GONE);
        }

        String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
        String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
        if (rowsBean.getStatus() == 2 || rowsBean.getAvailableQty() <= 0) {
            tv_recommend_mask_layer.setText(sellOutStr);
        } else if (rowsBean.getStatus() == 4) {
            tv_recommend_mask_layer.setText(soldOutStr);
        }
        if (rowsBean.getStatus() == 2 || rowsBean.getStatus() == 4 ||
                rowsBean.getAvailableQty() <= 0) {
            tv_recommend_mask_layer.setVisibility(View.VISIBLE);
        } else {
            tv_recommend_mask_layer.setVisibility(View.INVISIBLE);
        }
        ybmBaseHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(rowsBean);
                }
            }
        });
    }

    private void setAct(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {
        Group groupSpellPrice = ybmBaseHolder.getView(R.id.groupSpellPrice);
        TextView tvPriceTitle = ybmBaseHolder.getView(R.id.tv_price_title);
        TextView tvRecommendPrice = ybmBaseHolder.getView(R.id.tv_recommend_price);
        boolean isPassed = AuditStatusSyncUtil.getInstance().isAuditFirstPassed();

        ybmBaseHolder.setGone(R.id.tv_recommend_price, isPassed);
        if (!isPassed) {
            ybmBaseHolder.setGone(R.id.tv_audit_passed_visible, true);
        } else if (rowsBean.actPt == null && rowsBean.actPgby == null) {
            tvRecommendPrice.setVisibility(View.VISIBLE);
            groupSpellPrice.setVisibility(View.GONE);
        } else {
            tvRecommendPrice.setVisibility(View.GONE);
            groupSpellPrice.setVisibility(View.VISIBLE);
        }
        SpannableStringBuilder priceSpannable = null;
        if (rowsBean.actPt != null) {
            tvPriceTitle.setText("拼团价");
            priceSpannable = GoodsUtilsKt.getGoodsSpellGroupStepPrice(rowsBean.actPt.isStepPrice(), rowsBean.actPt.getMinSkuPrice(), rowsBean.actPt.assemblePrice + "");
        } else if (rowsBean.actPgby != null) {
            tvPriceTitle.setText("包邮价");
            priceSpannable = GoodsUtilsKt.getPriceStringBuilder(rowsBean.actPgby.getAssemblePrice() + "");
        }
        tvRecommendPrice.setText(priceSpannable);
    }

    private void setIsOEM(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {
        if (rowsBean.getIsOEM()) {
            //是否签署协议
            if (rowsBean.getSignStatus() == 1) {

            } else {
                ((TextView) ybmBaseHolder.getView(R.id.tv_recommend_price)).setTextSize(12);
                ((TextView) ybmBaseHolder.getView(R.id.tv_recommend_price)).setText("价格签署协议可见");
            }
        }

        if (rowsBean.showAgree == 0) {
            ((TextView) ybmBaseHolder.getView(R.id.tv_recommend_price)).setTextSize(12);
            ((TextView) ybmBaseHolder.getView(R.id.tv_recommend_price)).setText("价格签署协议可见");
        }
    }

    public static interface OnGridviewItemClickListener {
        void onItemClick(RowsBean rows);
    }

    private OnGridviewItemClickListener mOnItemClickListener = null;

    public void setOnItemClickListener(OnGridviewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder) {
        boolean isPassed = AuditStatusSyncUtil.getInstance().isAuditFirstPassed();
        baseViewHolder.setGone(R.id.tv_audit_passed_visible, !isPassed);
        baseViewHolder.setGone(R.id.tv_recommend_price, isPassed);
    }
}
