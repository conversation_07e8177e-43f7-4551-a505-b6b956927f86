package com.ybmmarket20.adapter;


import android.text.TextUtils;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ProductInstructionBean;

import java.util.List;

public class CommodityRecyclerAdapter extends YBMBaseAdapter<ProductInstructionBean> {

    public CommodityRecyclerAdapter(int layoutResId, List<ProductInstructionBean> data) {
        super(layoutResId, data);
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, ProductInstructionBean productInstructionBean) {
        //这里使用文本判断，无奈之举，后端配合不了，产品摇摆不定和稀泥
        if (TextUtils.equals(productInstructionBean.title, "医疗器械注册证或备案凭证编号")) {
            ybmBaseHolder.setVisible(R.id.tv_title, false);
            String content = "医疗器械注册证或备案凭证编号  " + productInstructionBean.content;
            ybmBaseHolder.setText(R.id.tv_name, content);
        } else {
            ybmBaseHolder.setVisible(R.id.tv_title, true);
            ybmBaseHolder.setText(R.id.tv_title, productInstructionBean.title);
            ybmBaseHolder.setText(R.id.tv_name, productInstructionBean.content);
        }
    }
}
