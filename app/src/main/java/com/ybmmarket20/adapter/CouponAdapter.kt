@file:JvmName("JvmNameCouponAdapter")

package com.ybmmarket20.adapter

import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.CommonRecyclerView
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.COUPON_STATUS_PAST_DUE
import com.ybmmarket20.activity.COUPON_STATUS_UNUSED
import com.ybmmarket20.activity.COUPON_STATUS_USED
import com.ybmmarket20.activity.CouponMemberActivity
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.bean.CouponScopeGoods
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.report.coupon.CouponUtil
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.*

//通用券
const val COUPON_TYPE_COMMON = 1

//商品券
const val COUPON_TYPE_GOODS = 2

//新人券
const val COUPON_TYPE_NEW_CUSUMER = 5

//叠加券
const val COUPON_TYPE_OVERLAY = 6

//店铺券
const val COUPON_TYPE_SHOP = 7

//平台券
const val COUPON_TYPE_PLATFORM = 8

//专品券
const val COUPON_TYPE_SPECIFIC_GOODS = 9

/**
 * 优惠券适配器
 */
class CouponAdapter(val iCouponEntryType: ICouponEntryType, layoutId: Int, rows: List<CouponRowBean>) : YBMBaseAdapter<CouponRowBean>(layoutId, rows) {

    var mCouponTrackCallback: ICouponTrackCallback? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: CouponRowBean?) {
        ifNotNull(baseViewHolder, t) { holder, bean ->
            val tvPriceUnit = holder.getView<TextView>(R.id.tv_PriceUnit)
            val tvDiscountUnit = holder.getView<TextView>(R.id.tv_discount_unit)
            val tvCouponAmount = holder.getView<TextView>(R.id.tv_coupon_amount)
            val tvCouponFullReduce = holder.getView<TextView>(R.id.tv_coupon_full_reduce)
            val clGoodsCouponBgLeft = holder.getView<ConstraintLayout>(R.id.cl_goods_coupon_bg_left)
            //店铺名
            val tvCouponTitle = holder.getView<TextView>(R.id.tv_coupon_title)
            val tvCouponSubTitle = holder.getView<TextView>(R.id.tv_coupon_subtitle)
            val tvCouponLimt = holder.getView<TextView>(R.id.tv_coupon_limit)
            val ivDownArrow = holder.getView<ImageView>(R.id.iv_arrow_down)
            val tvUseInstruction = holder.getView<TextView>(R.id.tv_use_instruction)
            val tvCouponFullReduceMax = holder.getView<TextView>(R.id.tv_coupon_full_reduce_max)
            val clGoodsCouponBgRight = holder.getView<ConstraintLayout>(R.id.cl_goods_coupon_bg_right)
            val coupon_divider = holder.getView<View>(R.id.coupon_divider)
            val vCouponDividerBottom = holder.getView<View>(R.id.v_coupon_divider_bottom)
            val tvGodCouponTag = holder.getView<TextView>(R.id.tv_god_coupon_tag)
            val tvInvalidReason = holder.getView<TextView>(R.id.tv_invalid_reason)
            val tvCouponImmediateUse = holder.getView<TextView>(R.id.tv_coupon_immediate_use)
            //设置立即领取按钮样式
            setCouponBtnStyle(bean.voucherType, tvCouponImmediateUse)

            tvInvalidReason.visibility = View.GONE
            //券名
            tvCouponSubTitle.text = bean.voucherTitle
            //使用范围
            tvCouponLimt.text = bean.voucherInstructions
            //是否显示神券标签
            tvGodCouponTag.visibility = if (TextUtils.isEmpty(bean.couponDutyDepartLabel)) {
                View.GONE
            } else {
                tvGodCouponTag.text = bean.couponDutyDepartLabel
                View.VISIBLE
            }

            //店铺券类型新样式 原有平台的只增加店铺名称 样式保持不变
            if (bean.voucherType == COUPON_TYPE_SHOP) {
                tvCouponLimt.visibility = View.VISIBLE
                tvUseInstruction.visibility = View.GONE
                ivDownArrow.visibility = View.INVISIBLE
            } else {
                tvCouponLimt.visibility = View.GONE
                tvUseInstruction.visibility = View.VISIBLE
                ivDownArrow.visibility = View.VISIBLE
            }

            //使用门槛
            holder.setText(R.id.tv_coupon_full_reduce, bean.minMoneyToEnableDesc)

            if (!bean.maxMoneyInVoucherDesc.isNullOrEmpty()) {
                tvCouponFullReduceMax.visibility = View.VISIBLE
                tvCouponFullReduceMax.text = bean.maxMoneyInVoucherDesc
            } else {
                tvCouponFullReduceMax.visibility = View.GONE
            }


            //券有效期
            val time = (DateTimeUtil.getCouponDateTime2(bean.validDate)
                    + "-" + DateTimeUtil.getCouponDateTime2(bean.expireDate))
            holder.setText(R.id.tv_coupon_date, time)

            val isMinMoney = !TextUtils.isEmpty(bean.minMoneyToEnableDesc)
            holder.setGone(R.id.tv_coupon_full_reduce, isMinMoney)
            holder.setText(R.id.tv_coupon_instructions, bean.voucherInstructions)
            holder.setGone(R.id.view_half_circle, !bean.isUnFold)
            clGoodsCouponBgRight.background =
                ContextCompat.getDrawable(mContext, if (!bean.isUnFold) R.drawable.shape_item_goods_coupon_right else R.drawable.shape_item_goods_coupon_right2)
            //根据服务器传递数据tag判断
            when (bean.state) {
                COUPON_STATUS_UNUSED -> {
                    mCouponTrackCallback?.onCouponExposure(bean, holder.layoutPosition)
                    val normalDrawable = ContextCompat.getDrawable(
                        mContext,
                        if (!bean.isUnFold) R.drawable.shape_item_goods_coupon_left else R.drawable.shape_item_goods_coupon_left2
                    )
                    val specificGoodsDrawable = ContextCompat.getDrawable(
                        mContext,
                        R.drawable.shape_item_goods_coupon_left_specific_goods
                    )
                    clGoodsCouponBgLeft.background = if (bean.voucherType == COUPON_TYPE_SPECIFIC_GOODS) {
                        specificGoodsDrawable
                    } else {
                        normalDrawable
                    }
                    val textColor = if (bean.voucherType == COUPON_TYPE_SPECIFIC_GOODS) {
                        R.color.color_ff155d
                    } else R.color.color_ff4244
                    tvCouponAmount.setTextColor(ContextCompat.getColor(mContext, textColor))
                    tvPriceUnit.setTextColor(ContextCompat.getColor(mContext, textColor))
                    tvDiscountUnit.setTextColor(ContextCompat.getColor(mContext, textColor))
                    tvCouponFullReduce.setTextColor(ContextCompat.getColor(mContext, textColor))
                    tvCouponFullReduceMax.setTextColor(ContextCompat.getColor(mContext, textColor))

                    //未开始的券不展示去使用的按钮
                    if (System.currentTimeMillis() >= bean.validDate) {
                        holder.setGone(R.id.tv_coupon_immediate_use, true)
                    } else {
                        holder.setGone(R.id.tv_coupon_immediate_use, false)
                    }
                    holder.setGone(R.id.tv_coupon_used, false)
                    holder.setGone(R.id.iv_coupon_past_due, false)
                    if (bean.voucherType == COUPON_TYPE_GOODS || (bean.voucherType == COUPON_TYPE_OVERLAY && bean.voucherSkuImages?.size ?: 0 > 0)) {
                        //商品券 或者 叠加券(指定商品数大于0)需要展示商品
                        holder.setGone(R.id.cl_all_fold, bean.isUnFold)
                        holder.setGone(R.id.coupon_divider, bean.isUnFold)
                        holder.setGone(R.id.group_fold, true)
                    } else {
                        holder.setGone(R.id.group_fold, false)
                        holder.setGone(R.id.cl_all_fold, bean.isUnFold)

                        holder.setGone(R.id.coupon_divider, bean.isUnFold)
                    }
                    setTitleAndTag(
                        mContext, bean.voucherType, bean.shopName
                            ?: "", bean.voucherTypeDesc ?: "", tvCouponTitle
                    )
                    coupon_divider.background = ContextCompat.getDrawable(mContext, R.drawable.shape_coupon_imaginary_line)
                    setBottomDividerHeight(vCouponDividerBottom, 14)
                }

                COUPON_STATUS_USED -> {
                    setUsedAndPastDue(
                        bean.isUnFold,
                        clGoodsCouponBgLeft,
                        tvCouponAmount,
                        tvPriceUnit,
                        tvDiscountUnit,
                        tvCouponFullReduce,
                        tvCouponFullReduceMax
                    )
                    holder.setGone(R.id.tv_coupon_immediate_use, false)
                    holder.setGone(R.id.tv_coupon_used, true)
                    holder.setGone(R.id.iv_coupon_past_due, false)
                    setTitleTag(
                        mContext, tvCouponTitle, R.drawable.shape_coupon_type_tag_gray, R.color.white, bean.shopName
                            ?: "", bean.voucherTypeDesc ?: ""
                    )
                    holder.setGone(R.id.group_fold, false)
                    holder.setGone(R.id.cl_all_fold, bean.isUnFold)
                    holder.setGone(R.id.coupon_divider, bean.isUnFold)
                    coupon_divider.background = ContextCompat.getDrawable(mContext, R.drawable.shape_coupon_imaginary_line_gray)
                    setBottomDividerHeight(vCouponDividerBottom, 8)
                }

                COUPON_STATUS_PAST_DUE -> {
                    setUsedAndPastDue(
                        bean.isUnFold,
                        clGoodsCouponBgLeft,
                        tvCouponAmount,
                        tvPriceUnit,
                        tvDiscountUnit,
                        tvCouponFullReduce,
                        tvCouponFullReduceMax
                    )
                    holder.setGone(R.id.tv_coupon_immediate_use, false)
                    holder.setGone(R.id.tv_coupon_used, false)
                    val ivInvalidFlag = holder.getView<ImageView>(R.id.iv_coupon_past_due)
                    if (bean.invalidFlag == 1) {
                        //已过期
                        ivInvalidFlag.setImageResource(R.drawable.icon_coupon_past_due)
                        tvUseInstruction.visibility = View.VISIBLE
                        ivDownArrow.visibility = View.VISIBLE
                    } else {
                        //已失效
                        ivInvalidFlag.setImageResource(R.drawable.icon_coupon_invalided)
                        holder.setText(R.id.tv_invalid_reason, bean.invalidReason)
                        tvUseInstruction.visibility = View.INVISIBLE
                        ivDownArrow.visibility = View.INVISIBLE
                        tvInvalidReason.visibility = View.VISIBLE

                    }
                    holder.setGone(R.id.iv_coupon_past_due, true)
                    setTitleTag(
                        mContext, tvCouponTitle, R.drawable.shape_coupon_type_tag_gray, R.color.white, bean.shopName
                            ?: "", bean.voucherTypeDesc ?: ""
                    )
                    holder.setGone(R.id.group_fold, false)
                    holder.setGone(R.id.cl_all_fold, bean.isUnFold)
                    holder.setGone(R.id.coupon_divider, bean.isUnFold)
                    coupon_divider.background = ContextCompat.getDrawable(mContext, R.drawable.shape_coupon_imaginary_line_gray)
                    setBottomDividerHeight(vCouponDividerBottom, 8)
                }
            }
            ivDownArrow.setImageResource(if (bean.isUnFold) R.drawable.icon_coupon_arrow_up else R.drawable.icon_coupon_arrow_down)
            ivDownArrow.setOnClickListener(OnClickUserInstructionListener(bean, this))
            tvUseInstruction.setOnClickListener(OnClickUserInstructionListener(bean, this))
            holder.getView<View>(R.id.tv_coupon_immediate_use).setOnClickListener(
                OnCouponItemClickListener(
                    bean.voucherTemplateId
                        ?: "", bean,true, position = holder.layoutPosition
                )
            )
            holder.getView<View>(R.id.tv_goods_coupon_usable).setOnClickListener(
                OnCouponItemClickListener(
                    bean.voucherTemplateId
                        ?: "", bean, position = holder.layoutPosition
                )
            )
            holder.getView<View>(R.id.iv_coupon_arrow_right).setOnClickListener(
                OnCouponItemClickListener(
                    bean.voucherTemplateId
                        ?: "", bean, position = holder.layoutPosition
                )
            )

            if (bean.voucherState == 1) {
                tvPriceUnit.visibility = View.GONE
                tvDiscountUnit.visibility = View.VISIBLE
                val amount = UiUtils.transform2Int(bean.discount)
                holder.setText(R.id.tv_coupon_amount, StringUtil.setDotAfterSize(amount, 19))
                tvDiscountUnit.text = "折"
            } else {
                tvPriceUnit.visibility = View.VISIBLE
                tvDiscountUnit.visibility = View.GONE
                holder.setText(R.id.tv_coupon_amount, UiUtils.transformInt(bean.moneyInVoucher))
                tvPriceUnit.text = "¥"
            }

            if (bean.isUnFold) {
                val goodsList = bean.voucherSkuImages
                goodsList?.run {
                    if (this.isEmpty() || this.last().isLast != ITEM_TYPE_LAST) {
                        this.add(CouponScopeGoods(null, -1, ITEM_TYPE_LAST, "").apply {
                            this.itemType = ITEM_TYPE_LAST
                        })
                    }
                }
                val couponScopeGoodsAdapter = goodsList?.let {
                    CouponScopeGoodsAdapter(it, bean.voucherTemplateId ?: "")
                }
                val crvCouponScopeGoods = holder.getView<CommonRecyclerView>(R.id.crv_coupon_to_use)
                crvCouponScopeGoods.layoutManager = WrapLinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
                crvCouponScopeGoods.setAdapter(couponScopeGoodsAdapter)
            } else {
                val couponScopeGoodsAdapter = CouponScopeGoodsAdapter(
                    ArrayList<CouponScopeGoods>(), bean.voucherTemplateId
                        ?: ""
                )
                val crvCouponScopeGoods = holder.getView<CommonRecyclerView>(R.id.crv_coupon_to_use)
                crvCouponScopeGoods.layoutManager = WrapLinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
                crvCouponScopeGoods.setAdapter(couponScopeGoodsAdapter)
            }
        }
    }

    private fun setUsedAndPastDue(
        isUnFold: Boolean, clGoodsCouponBgLeft: ConstraintLayout, tvCouponAmount: TextView,
        tvPriceUnit: TextView, tvDiscountUnit: TextView, tvCouponFullReduce: TextView, tvCouponFullReduceMax: TextView
    ) {
        clGoodsCouponBgLeft.background =
            ContextCompat.getDrawable(mContext, if (!isUnFold) R.drawable.shape_item_goods_coupon_left_gray else R.drawable.shape_item_goods_coupon_left_gray2)
        tvCouponAmount.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
        tvPriceUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
        tvDiscountUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
        tvCouponFullReduce.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
        tvCouponFullReduceMax.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
    }


    inner class OnCouponItemClickListener(val coupon_id: String, val bean: CouponRowBean,val isUse:Boolean = false, val position: Int) : View.OnClickListener {

        override fun onClick(v: View?) {
            val router = if (TextUtils.isEmpty(bean.appUrl)) {
                "ybmpage://couponavailableactivity/$coupon_id"
            } else {
                bean.appUrl ?: ""
            }.let {
                CouponUtil.wrapperRouterUrlWithParam(it, iCouponEntryType.getCouponEntryType())
            }
            if (router.startsWith("ybmpage")) {
                RoutersUtils.open(router)
                mCouponTrackCallback?.onCouponClick(bean, position, "立即使用", router)
            }

            if (isUse){ //立即使用
                v?.let {
                    CouponMemberActivity.jgTrackBtnClick(it.context,"立即使用")
                }
            }
        }
    }

    private class OnClickUserInstructionListener(val bean: CouponRowBean?, val adapter: CouponAdapter) : View.OnClickListener {

        override fun onClick(v: View?) {
            bean?.let {
                it.isUnFold = !it.isUnFold
                adapter.notifyDataSetChanged()
            }
        }

    }

    /**
     * 设置底部分割线高度
     */
    private fun setBottomDividerHeight(divider: View, height: Int) {
        val lp: ConstraintLayout.LayoutParams = divider.layoutParams as ConstraintLayout.LayoutParams
        lp.height = ConvertUtils.dp2px(height.toFloat())
    }

    fun setOnTrackCallback(callback: ICouponTrackCallback) {
        mCouponTrackCallback = callback
    }

    interface ICouponTrackCallback {

        fun onCouponExposure(voucherListBean: CouponRowBean?, position: Int)

        fun onCouponClick(voucherListBean: CouponRowBean?, position: Int, clickName: String?, clickLink: String?)
    }

}