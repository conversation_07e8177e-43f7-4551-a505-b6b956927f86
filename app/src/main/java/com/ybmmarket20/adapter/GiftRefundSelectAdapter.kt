package com.ybmmarket20.adapter

import android.annotation.SuppressLint
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.GiftSelectRefundBean
import com.ybmmarket20.bean.GiftSelectStatus
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.bean.RefundProductListBean.REFUND_GIFT_SELECTED
import com.ybmmarket20.bean.RefundProductListBean.REFUND_GIFT_UNSELECTED
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

/**
 * @class   GiftRefundSelectAdapter
 * <AUTHOR>
 * @date  2024/9/13
 * @description
 */
class GiftRefundSelectAdapter :YBMBaseAdapter<RefundProductListBean>(R.layout.item_gift_select_refund, arrayListOf()) {

    companion object{
        const val GIFT_SELECT_REFUND_TYPE = 1 //退还选择赠品的样式
    }

    var canSelectNumber:Int = 0 //可选退还数量

    var selectedNumberChangeCallBack: (()->Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, refundProductListBean: RefundProductListBean) {
        whenAllNotNull(baseViewHolder, refundProductListBean) {holder, bean ->
            bindRefundGiftSelect(holder, bean)
        }

    }

    @SuppressLint("NotifyDataSetChanged")
    private fun bindRefundGiftSelect(holder: YBMBaseHolder, bean: RefundProductListBean) {
        holder.itemView.apply {
            val ivSelect = findViewById<ImageView>(R.id.iv_select)
            val ivGift = findViewById<ImageView>(R.id.iv_gift)
            val tvTitle = findViewById<TextView>(R.id.tv_title)
            val tvValidity = findViewById<TextView>(R.id.tv_validity)
            val tvMediumPackaging = findViewById<TextView>(R.id.tv_medium_packaging)
            val tvRefundNumber = findViewById<TextView>(R.id.tv_refund_number)
            val ivMinus = findViewById<ImageView>(R.id.iv_minus)
            val ivAdd = findViewById<ImageView>(R.id.iv_add)
            val tvNumber = findViewById<TextView>(R.id.tv_number)

            mContext.glideLoadWithPlaceHolder((AppNetConfig.LORD_IMAGE + bean.imageUrl),ivGift)

            tvTitle.text = "${bean.productName ?: ""}${bean.spec}"
            tvValidity.text = "效期: " + bean.nearEffect
            tvNumber.text = if(bean.curSelectedCount >= 1) bean.curSelectedCount.toString() else {
                bean.curSelectedCount = 1
                "1"
            }
            tvMediumPackaging.text = "中包装数量：${bean.mediumPackageNum}"
            tvRefundNumber.text = "可退数量：${bean.productAmount}"

            if (!bean.isGiftSelected) {
                Glide.with(mContext).load(R.drawable.icon_gift_no_select).into(ivSelect)
                ivSelect.setOnClickListener {
                    if (canSelectNumber >= getSelectGiftNumber() + bean.curSelectedCount){
                        bean.isGiftSelected = true
                        selectedNumberChangeCallBack?.invoke()
                        notifyDataSetChanged()
                    }else{
                        ToastUtils.showShort("还可勾选${canSelectNumber - getSelectGiftNumber()}件商品，请重新选择")
                    }

                }
            } else {
                Glide.with(mContext).load(R.drawable.icon_gift_select).into(ivSelect)
                ivSelect.setOnClickListener {
                    bean.isGiftSelected = false
                    selectedNumberChangeCallBack?.invoke()
                    notifyDataSetChanged()
                }
            }

            ivMinus.setOnClickListener {

                if (bean.curSelectedCount > 1) {
                    bean.curSelectedCount--
                    tvNumber.text = bean.curSelectedCount.toString()
                }else{ //不可减了
                    return@setOnClickListener
                }

                if (bean.isGiftSelected){ //选中状态
                    selectedNumberChangeCallBack?.invoke()
                }

            }

            ivAdd.setOnClickListener {
                if (bean.isGiftSelected){ //选中状态
                    if (canSelectNumber == getSelectGiftNumber()){
                        // TODO: 李江 Toast待确定
//                        ToastUtils.showShort("还可勾选0件商品，请重新选择")
                    } else if (bean.curSelectedCount.toString() == bean.productAmount){
                        // TODO: 李江 Toast待确定
//                        ToastUtils.showShort("还可勾选a-b件商品，请重新选择")
                    } else {
                        bean.curSelectedCount++
                        tvNumber.text = bean.curSelectedCount.toString()
                        selectedNumberChangeCallBack?.invoke()
                    }
                } else { // 未选中
                    if (bean.curSelectedCount.toString() == bean.productAmount){
                        // TODO: 李江 Toast待确定
//                        ToastUtils.showShort("还可勾选a-b件商品，请重新选择")
                    } else {
                        bean.curSelectedCount++
                        tvNumber.text = bean.curSelectedCount.toString()
                    }
                }
            }

        }
    }

    /**
     * 获取目前选中的赠品数
     * @return Int
     */
    fun getSelectGiftNumber():Int{
        var selectNumber = 0
        (mData as ArrayList<RefundProductListBean>).forEach {
            if (it.isGiftSelected){
                selectNumber += it.curSelectedCount
            }
        }

        return selectNumber
    }

}