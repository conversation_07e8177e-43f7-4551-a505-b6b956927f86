package com.ybmmarket20.adapter

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel


class GoodsLicenseEditAdapter(goodsList: MutableList<RefundProductListBean>, private val paymentGoodsViewModel : PaymentGoodsViewModel)
    : YBMBaseAdapter<RefundProductListBean>(R.layout.item_goods_license, goodsList) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RefundProductListBean?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean->
            val ivGoods = holder.getView<ImageView>(R.id.ivGoods)
            ImageUtil.load(mContext, "${AppNetConfig.LORD_IMAGE}${bean.imageUrl}", ivGoods)
            holder.setText(R.id.tvProductName, "${bean.productName}/${bean.spec}")

            val ivReport = holder.getView<ImageView>(R.id.ivReport)
            val tvReport = holder.getView<TextView>(R.id.tvReport)
            val isReportSelected = paymentGoodsViewModel.isSelectReport(bean.productId)
            setSelectStatus(ivReport, tvReport, isReportSelected)

            val ivLicense = holder.getView<ImageView>(R.id.ivLicense)
            val tvLicense = holder.getView<TextView>(R.id.tvLicense)
            val isLicenseSelected = paymentGoodsViewModel.isSelectLicense(bean.productId)
            setSelectStatus(ivLicense, tvLicense, isLicenseSelected)

            val llReport = holder.getView<LinearLayout>(R.id.llReport)
            val llLicense = holder.getView<LinearLayout>(R.id.llLicense)
            llReport.setOnClickListener {
                paymentGoodsViewModel.changeReportStatus(bean.productId)
                notifyItemChanged(holder.bindingAdapterPosition)
            }
            llLicense.setOnClickListener {
                paymentGoodsViewModel.changeLicenseStatus(bean.productId)
                notifyItemChanged(holder.bindingAdapterPosition)
            }
            if (bean.isThirdShop && !bean.isFbpShop) {
                llLicense.visibility = View.VISIBLE
            } else {
                llLicense.visibility = View.GONE
            }

        }
    }

    private fun setSelectStatus(ivStatus: ImageView, textStatus: TextView, status: Boolean) {
        if (status) {
            //选中
            ivStatus.setImageResource(R.drawable.icon_license_selected)
            textStatus.setTextColor(Color.parseColor("#01B377"))
        } else {
            ivStatus.setImageResource(R.drawable.icon_license_unselected)
            textStatus.setTextColor(Color.parseColor("#676773"))
        }
    }
}