package com.ybmmarket20.adapter

import android.content.Context
import android.graphics.Color
import android.util.SparseArray
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.HomeFeedRows
import com.ybmmarket20.bean.HomeFeedShopInfo
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.StandardInfo
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.flowDataPageListPageShopClickForFeed
import com.ybmmarket20.utils.analysis.flowDataPageListPageShopExposureForFeed
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.GoodListAdapterNew

// 商品
const val ITEM_TYPE_GOODS: Int = 1
// 店铺
const val ITEM_TYPE_SHOP: Int = 2
// 标品
const val ITEM_TYPE_STANDARD_GOODS = 3

class HomeFeedAdapter(val context: Context, data: MutableList<HomeFeedRows>?) :
    YBMBaseMultiItemAdapter<HomeFeedRows>(data) {

    private var goodsAdapter: GoodListAdapterNew
    private var standardGoodsListAdapter: StandardGoodsListAdapter
    var tabIndex = 0
    override var flowData: BaseFlowData? = null
        set(value) {
            field = value
            goodsAdapter.flowData = value
            standardGoodsListAdapter.flowData = value
        }
    private val traceShopData = SparseArray<SparseArray<String>>()

    init {
        addItemType(ITEM_TYPE_GOODS, R.layout.item_goods_new)
        addItemType(ITEM_TYPE_SHOP, R.layout.item_home_feed_shop)
        addItemType(ITEM_TYPE_STANDARD_GOODS, R.layout.item_standard_goods)
        goodsAdapter = GoodListAdapterNew(context, -1, null)
        goodsAdapter.flowData = flowData
        standardGoodsListAdapter = StandardGoodsListAdapter(context,-1, null)
        standardGoodsListAdapter.tabIndex = tabIndex
        standardGoodsListAdapter.flowData = flowData
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: HomeFeedRows?) {
        whenAllNotNull(baseViewHolder, t) { holder, homeFeedRows ->
            if (homeFeedRows.itemType == ITEM_TYPE_GOODS && homeFeedRows.productInfo != null) {
                bindGoodsItemView(holder, homeFeedRows.productInfo!!)
            } else if (homeFeedRows.itemType == ITEM_TYPE_SHOP && homeFeedRows.shopInfo != null) {
                bindShopItemView(holder, homeFeedRows.shopInfo!!)
            } else if (homeFeedRows.itemType == ITEM_TYPE_STANDARD_GOODS && homeFeedRows.standardInfo != null) {
                bindStandardGoodsInfo(holder, homeFeedRows.standardInfo!!)
            }
        }
    }

    /**
     * 商品
     */
    private fun bindGoodsItemView(holder: YBMBaseHolder, rowsBean: RowsBean) {
        goodsAdapter.bindItemView(holder, rowsBean)
    }

    /**
     * 店铺
     */
    private fun bindShopItemView(holder: YBMBaseHolder, shopInfo: HomeFeedShopInfo) {
        if (traceShopData[holder.bindingAdapterPosition] == null) {
            traceShopData.put(holder.bindingAdapterPosition, SparseArray())
            flowDataPageListPageShopExposureForFeed(
                shopInfo.newAppLink,
                "${holder.bindingAdapterPosition + 1}",
                shopInfo.showName,
                shopInfo.shopCode
            )
        }
        holder.getView<TextView>(R.id.tv_title_tag).apply {
            text = shopInfo.shopPatternCodeName
            background = if (shopInfo.shopPatternCode == "ybm") {
                ContextCompat.getDrawable(context, R.drawable.shape_home_shop_feed_title_tag_green)
            } else {
                ContextCompat.getDrawable(context, R.drawable.shape_home_shop_feed_title_tag_red)
            }
            setTextColor(
                if (shopInfo.shopPatternCode == "ybm") {
                    Color.parseColor("#00b377")
                } else {
                    Color.parseColor("#FC6B0B")
                }
            )
        }
        holder.setText(R.id.tv_title, shopInfo.showName)
        holder.setVisible(
            R.id.ll_tag,
            shopInfo.activityList != null || shopInfo.activityList!!.isNotEmpty()
        )
        val tagContainer = holder.getView<LinearLayout>(R.id.ll_tag)
        tagContainer.removeAllViews()
        shopInfo.activityList?.forEach {
            val tagView = View.inflate(context, R.layout.item_home_feed_shop_tag, null)
            tagView.findViewById<TextView>(R.id.tv_home_shop_tag).text = it.activityTypeDesc
            tagView.findViewById<TextView>(R.id.tv_home_shop_tag_des).text = it.activityContent
            tagContainer.addView(tagView)
        }
        val goodsAdapter = HomeFeedShopGoodsAdapter(
            context,
            shopInfo.shopGoods,
            shopInfo.shopCode,
            traceShopData[holder.bindingAdapterPosition]
        )
        holder.getView<RecyclerView>(R.id.rv_home_shop_feed).apply {
            //不参与嵌套滑动，防止滑动冲突
            isNestedScrollingEnabled = false
            layoutManager = WrapLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = goodsAdapter
        }
        holder.getView<ConstraintLayout>(R.id.cl_home_shop_feed_top).setOnClickListener {
            RoutersUtils.open(shopInfo.newAppLink)
            flowDataPageListPageShopClickForFeed(
                shopInfo.newAppLink,
                "${holder.bindingAdapterPosition + 1}",
                shopInfo.showName,
                shopInfo.shopCode
            )

        }
    }

    /**
     * 标品
     */
    private fun bindStandardGoodsInfo(holder: YBMBaseHolder, standardInfo: StandardInfo) {
        standardGoodsListAdapter.bindItemView(holder, standardInfo)
    }

}