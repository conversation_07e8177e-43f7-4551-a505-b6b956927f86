package com.ybmmarket20.adapter;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.business.order.OrderBuyAgainManager;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.PromotionTagView;
import com.ybmmarket20.view.TagView;
import com.ybmmarketkotlin.utils.TextViewKt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * 订单详情商品明细列表
 */

public class OrderDetailtAdapter extends YBMBaseMultiItemAdapter<RefundProductListBean> {
    private boolean onclick = true;//点击到商品详情
    private boolean choice = false;//选择商品
    private boolean showTag = true;//显示标签
    private boolean isDetail = false;//金额为0隐藏
    private boolean isPayActivity = false;//待确认订单过来的
    private int mTotal;
    private Handler mHandler;
    private static final int GIFT_ID = 20;
    private boolean isShowFindSameGoodsBtn = false;
    private String mOrderNo;

    private int isVirtualSupplier;//是否是虚拟供应商
    public boolean isSmallPayment;

    public OrderDetailtAdapter(List<RefundProductListBean> data) {
        this(data, true, false, true, false);
    }

    public OrderDetailtAdapter(List<RefundProductListBean> data, boolean click, boolean isDetail) {
        this(data, click, false, true, isDetail);
    }

    public OrderDetailtAdapter(List<RefundProductListBean> data, boolean click, boolean choice, boolean showTag, boolean isDetail, boolean isPayActivity, Handler handler) {
        this(data, click, choice, showTag, isDetail);
        this.isPayActivity = isPayActivity;
        this.mHandler = handler;
    }

    public OrderDetailtAdapter(List<RefundProductListBean> data, boolean click, boolean choice, boolean showTag, boolean isDetail, boolean isPayActivity) {
        this(data, click, choice, showTag, isDetail);
        this.isPayActivity = isPayActivity;
    }

    public OrderDetailtAdapter(boolean isSmallPayment, List<RefundProductListBean> data, boolean click, boolean choice, boolean showTag, boolean isDetail) {
        this(data, click, choice, showTag, isDetail);
        this.isSmallPayment = isSmallPayment;
    }

    public OrderDetailtAdapter(List<RefundProductListBean> data, boolean click, boolean choice, boolean showTag, boolean isDetail) {
        super(data);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE, R.layout.detail_product_package_title);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE, R.layout.detail_product_package_subtitle);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT, R.layout.detail_product_package_item);
        addItemType(RefundProductListBean.ITEMTYPE_CONTENT, R.layout.detail_product_package_item);
        addItemType(RefundProductListBean.ITEMTYPE_GIFT_CONTENT, R.layout.detail_product_gift_item);
        addItemType(RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT, R.layout.item_refund_detail_goods_gift);
        this.onclick = click;
        this.choice = choice;
        this.showTag = showTag;
        this.isDetail = isDetail;
        initData();
    }

    public void setVirtualSupplier(int virtualSupplier) {
        isVirtualSupplier = virtualSupplier;
    }

    @Override
    public void setNewData(List data) {
        super.setNewData(data);
        initData();
    }

    /**
     * 设置是否显示找相似按钮
     * @param isShowFindSameGoodsBtn
     */
    public void setShowFindSameGoodsBtn(boolean isShowFindSameGoodsBtn) {
        this.isShowFindSameGoodsBtn = isShowFindSameGoodsBtn;
    }

    // 初始化isSelected的数据
    private void initData() {
        mTotal = 0;
        if (mData != null && mData.size() > 0) {
            for (int i = 0; i < mData.size(); i++) {
                RefundProductListBean listBean = (RefundProductListBean) mData.get(i);
                if (listBean.type == 5) {
                    mTotal++;
                }
            }
        }

    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, RefundProductListBean bean) {
        switch (bean.getItemType()) {
            case RefundProductListBean.ITEMTYPE_PACKAGE_TITLE:
                bindPackgeTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE:
                bindPackgeSubTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_GIFT_CONTENT:
                bindGiftSubTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT:
            case RefundProductListBean.ITEMTYPE_CONTENT:
                bindItem(baseViewHolder, bean);
                break;

            case RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT:
                bindGoodsGift(baseViewHolder, bean);
                break;
        }
    }

    /**
     * 赠品
     * @param holder
     * @param bean
     */
    private void bindGoodsGift(YBMBaseHolder holder, final RefundProductListBean bean) {
        ImageView iv = holder.getView(R.id.iv_cart_goods_gift);
        TextView title = holder.getView(R.id.tv_goods_gift_title);
        TextView count = holder.getView(R.id.tv_goods_gift_count);
        TextView refundCount = holder.getView(R.id.tv_goods_gift_refund_count);
        TextView tvSpec = holder.getView(R.id.tv_goods_gift_spec);
        ImageUtil.load(mContext, bean.imageUrl, iv);
        title.setText(bean.productName);
        refundCount.setText("￥" + UiUtils.transform(bean.subtotal));
        count.setText("X"+bean.productAmount);
        tvSpec.setText("规格：" + bean.spec);
        holder.itemView.setOnClickListener(v -> RoutersUtils.open("ybmpage://productdetail/${bean.skuid}"));
    }

    private void bindGiftSubTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {

        baseViewHolder.setGone(R.id.tv_btn_ok, isPayActivity);
        String Str = "物料心愿单礼包";
        String StrNumber = isPayActivity ? Str : Str + "(" + mTotal + ")";
        baseViewHolder.setText(R.id.tv_number, StrNumber);

        baseViewHolder.setOnClickListener(R.id.tv_btn_ok, new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                setGift(baseViewHolder, bean);
            }
        });

        CheckBox cb = baseViewHolder.getView(R.id.cb_choice);
        cb.setVisibility(isPayActivity ? View.VISIBLE : View.GONE);
        //选中状态
        cb.setChecked(bean.selectStatus == 1);
//        if (cb.isChecked()) {
//            cb.setEnabled(false);
//        } else {
//            cb.setEnabled(true);
//        }
        //是否可以点击
        cb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                for (RefundProductListBean refundProductListBean : ((List<RefundProductListBean>) mData)) {
                    refundProductListBean.selectStatus = 0;
                }

                if (cb.isChecked()) {
                    bean.selectStatus = 1;
                } else {
                    bean.selectStatus = 0;
                }
//                if (mHandler != null) {
//                    mHandler.sendMessage(mHandler.obtainMessage(GIFT_ID, cb.isChecked() ? bean.id + "" : "-1"));
//                }
                notifyDataSetChanged();
                LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_GIFT_PAYMENT_2).putExtra("giftId", cb.isChecked() ? bean.id + "" : "-1"));

            }
        });

    }

    private void setGift(YBMBaseHolder baseViewHolder, RefundProductListBean bean) {

        setAlertDialogEx(new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {

                String merchantId = SpUtil.getMerchantid();
                RequestParams params = new RequestParams();
                params.put("merchantId", merchantId);
                params.put("giftId", bean.id + "");
                HttpManager.getInstance().post(AppNetConfig.CANCEL_BIG_GIFT_PACKAGE, params, new BaseResponse<EmptyBean>() {

                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                        if (obj != null && obj.isSuccess()) {

                            int position = baseViewHolder.getAdapterPosition();
                            if (mData.size() > position) {
                                deleteAccount(bean.id);
                            }

                        }
                    }
                });
            }
        });

    }

    private void deleteAccount(int id) {

        Iterator<RefundProductListBean> it = mData.iterator();
        while (it.hasNext()) {
            RefundProductListBean x = it.next();
            if (x.id == id) {
                it.remove();
            }
        }
        OrderDetailtAdapter.super.setNewData(mData);
        LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_GIFT_PAYMENT).putExtra("giftId", id + ""));
    }

    public void setAlertDialogEx(AlertDialogEx.OnClickListener onClickListener) {
        AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
        dialogEx.setMessage("确认不需要后\r\n您将无法获取该礼包").setCancelButton("取消", null).setConfirmButton("确认", onClickListener).show();
    }

    protected void bindPackgeTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
        if (!choice) {
            baseViewHolder.getView(R.id.cb_choice).setVisibility(View.GONE);
        }
        TextView tvName = baseViewHolder.getView(R.id.tv_name);
        tvName.setText(bean.productName);
        baseViewHolder.setText(R.id.tv_num, "X" + (isSmallPayment?bean.productQuantity:bean.productAmount)).setText(R.id.tv_name, bean.productName);
    }

    protected void bindPackgeSubTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
        baseViewHolder.setText(R.id.tv_price, "单价:¥" + UiUtils.transform(bean.productPrice)).setText(R.id.tv_total, "小计:¥" + UiUtils.transform(bean.subtotal));
    }

    protected void bindItem(final YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
        if (!choice) {
            baseViewHolder.getView(R.id.cb_choice).setVisibility(View.GONE);

        } else {
            if (bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT) {
                baseViewHolder.getView(R.id.cb_choice).setVisibility(View.INVISIBLE);
            } else {
                baseViewHolder.getView(R.id.cb_choice).setVisibility(View.VISIBLE);
            }
        }

        //是否显示药采节
        //baseViewHolder.setVisible(R.id.tv_procurement_festival, bean.gift);

        //显示不同样式
        baseViewHolder.setBackgroundColor(R.id.root, UiUtils.getColor(bean.type == 5 ? R.color.color_fafafa : R.color.white))
                .setTextColor(R.id.tv_name, UiUtils.getColor(bean.type == 5 ? R.color.text_676773 : R.color.text_000000));

        baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min);
        baseViewHolder.setText(R.id.tv_name, bean.productName);

        baseViewHolder.setText(R.id.tv_price, "¥" + UiUtils.transform(bean.productPrice));
        baseViewHolder.setText(R.id.tv_guige, bean.spec);

        TagView tagView = baseViewHolder.getView(R.id.tg);
        if (showTag) {
            if (!TextUtils.isEmpty(bean.blackProductText) && (bean.tagList == null || bean.tagList.isEmpty())) {
                LabelIconBean bean1 = new LabelIconBean();
                bean1.name = bean.blackProductText;
                bean1.uiType = 2;
                List<LabelIconBean> tagList = new ArrayList<>();
                tagList.add(bean1);
                tagView.bindData(tagList);
            } else {
                tagView.bindData(bean.tagList);
            }
        }
        TextView tv_shop_name = baseViewHolder.getView(R.id.tv_name);

        // 设置促销活动标签
        PromotionTagView mPtv = baseViewHolder.getView(R.id.view_ptv);
        mPtv.setShowData(bean.productActivityTag);

        setGoodsName(tv_shop_name, bean);

        // 包邮 tag
        {
            TextView tvName = baseViewHolder.getView(R.id.tv_name);
            if (!TextUtils.isEmpty(bean.tagTitle)) {
                TextViewKt.textWithSuffixTag(tvName, SpannableStringBuilder.valueOf(bean.productName), bean.tagTitle);
            }
        }

        baseViewHolder.setGone(R.id.ll_confirm_order_product_detail_price, isDetail ? bean.showDetail : isDetail);
        baseViewHolder.setGone(R.id.iv_detail, isDetail);
        baseViewHolder.setImageResource(R.id.iv_detail, bean.showDetail ? R.drawable.icon_up_arrow : R.drawable.icon_down_arrow);

        baseViewHolder.setText(R.id.tv_num, "x" + (isSmallPayment?bean.productQuantity:bean.productAmount));
        baseViewHolder.setText(R.id.tv_total, bean.getTotalPriceStringBuilder());

        if (onclick) {
            baseViewHolder.setOnClickListener(R.id.ll_root, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemClick(bean, v);
                }
            });
        } else if (!choice) {//不点击也不选择点击就展开
            baseViewHolder.setOnClickListener(R.id.ll_root, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bean.showDetail = !bean.showDetail;
                    notifyItemChanged(baseViewHolder.getAdapterPosition());
                }
            });
        }
        if (!choice) {
            baseViewHolder.setOnClickListener(R.id.ll_detail, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bean.showDetail = !bean.showDetail;
                    notifyItemChanged(baseViewHolder.getAdapterPosition());
                }
            });
            baseViewHolder.setOnClickListener(R.id.ll_price_detail, new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bean.showDetail = !bean.showDetail;
                    notifyItemChanged(baseViewHolder.getAdapterPosition());
                }
            });
        }
        //控制显示
        if (false) {
            baseViewHolder.setGone(R.id.tv_rel_price, bean.realPayAmount > 0);//实付金额
            baseViewHolder.setGone(R.id.tv_order_balance, bean.useBalanceAmount > 0);//余额抵扣
            baseViewHolder.setGone(R.id.tv_order_coupon, bean.discountAmount > 0);//优惠金额
            baseViewHolder.setGone(R.id.tv_rebate, bean.balanceAmount > 0);//返点

            baseViewHolder.setGone(R.id.tv_rel_price_value, bean.realPayAmount > 0);//实付金额
            baseViewHolder.setGone(R.id.tv_order_balance_value, bean.useBalanceAmount > 0);//余额抵扣
            baseViewHolder.setGone(R.id.tv_order_coupon_value, bean.discountAmount > 0);//优惠金额
            baseViewHolder.setGone(R.id.tv_rebate_value, bean.balanceAmount > 0);//返点

        }

        try {
            //返点金额
            baseViewHolder.setText(R.id.tv_rel_price_value, "¥" + StringUtil.DecimalFormat2Double(bean.realPayAmount));//实付金额
            baseViewHolder.setText(R.id.tv_order_balance_value, "¥" + StringUtil.DecimalFormat2Double(bean.useBalanceAmount));//余额抵扣
            baseViewHolder.setText(R.id.tv_order_coupon_value, "¥" + StringUtil.DecimalFormat2Double(bean.discountAmount));//优惠金额
            baseViewHolder.setText(R.id.tv_rebate_value, "¥" + StringUtil.DecimalFormat2Double(bean.balanceAmount));//返点

            //实付价
            baseViewHolder.setText(R.id.tv_rel_price_value2, "¥" + StringUtil.DecimalFormat2Double(bean.productPrice));//原单价
            baseViewHolder.setText(R.id.tv_order_coupon_value2, "¥" + bean.getDiscountAmount());//优惠
            baseViewHolder.setText(R.id.tv_rebate_value2, "¥" + bean.getBalanceAmount());//余额抵扣
//            double sub = MathUtils.sub(bean.productPrice, Double.valueOf(bean.getDiscountAmount()));
//            double result = MathUtils.sub(sub, Double.valueOf(bean.getBalanceAmount()));
            baseViewHolder.setText(R.id.tv_rk_value, "¥" + bean.getRkPrice());//实付价
            //成本价
            baseViewHolder.setText(R.id.tv_rel_price_value_tow, "¥" + StringUtil.DecimalFormat2Double(bean.productPrice));//原单价
            baseViewHolder.setText(R.id.tv_order_coupon_value_tow, "¥" + bean.getDiscountAmount());//优惠
            baseViewHolder.setText(R.id.tv_rebate_value_tow, "¥" + bean.getBalance2Amount());//返利
            baseViewHolder.setText(R.id.tv_rk_value_tow, "¥" + bean.getFormatCostPrice());//成本价
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        if (bean.extraGift != 1 && (bean.packageId==null || bean.packageId.isEmpty()) && !isDetail){ //非赠品和套餐 非明细页
            baseViewHolder.setVisible(R.id.tv_find_same_goods, false);
            baseViewHolder.setVisible(R.id.tv_bug_again, true);
            baseViewHolder.getView(R.id.tv_bug_again).setOnClickListener(v -> {
                OrderBuyAgainManager.Companion.buyAgain((TextView)v,mOrderNo,bean.productId,isSmallPayment?bean.productQuantity:bean.productAmount);
            });
        }else{
            baseViewHolder.setVisible(R.id.tv_bug_again, false);
            //设置找相似
            try {
                baseViewHolder.setVisible(R.id.tv_find_same_goods, isShowFindSameGoodsBtn);
                baseViewHolder.getView(R.id.tv_find_same_goods).setOnClickListener(v -> {
                    RoutersUtils.open("ybmpage://findsamegoods/" + bean.productId);
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("order_no", mOrderNo);
                    trackParams.put("sku_id", bean.productId);
                    XyyIoUtil.track("page_OrderDetails_Similar", trackParams);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!TextUtils.isEmpty(bean.priceDes)) {
            baseViewHolder.setVisible(R.id.tvZengPinTip, true);
            baseViewHolder.getView(R.id.tvZengPinTip).setOnClickListener(v -> {
                AlertDialogEx alert = new AlertDialogEx(mContext);
                alert.setTitle("价格说明")
                        .setMessage(bean.priceDes)
                        .setCancelButton("我知道了", (dialog, button) -> dialog.dismiss());
                alert.show();
            });
        } else {
            baseViewHolder.setVisible(R.id.tvZengPinTip, false);
        }

        // 设置整单包邮样式
        if (bean.tagWholeOrderList != null && !bean.tagWholeOrderList.isEmpty()) {
            baseViewHolder.setVisible(R.id.cl_new_tag, true);
            for (LabelIconBean labelIconBean : bean.tagWholeOrderList) {
                if (labelIconBean.name.equals("整单包邮")) {
                    TextView tv_new_tag_1 = baseViewHolder.getView(R.id.tv_new_tag_1);
                    tv_new_tag_1.setText(labelIconBean.name);
                    TextView tv_new_tag_2 = baseViewHolder.getView(R.id.tv_new_tag_2);
                    tv_new_tag_2.setText(labelIconBean.description);
                }
            }
        }else{
            baseViewHolder.setVisible(R.id.cl_new_tag, false);
        }

    }

    public void setOrderNo(String orderNo) {
        mOrderNo = orderNo;
    }


    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, RefundProductListBean rowsBean) {

        List<Drawable> list = new ArrayList<>();

        String goodsName = rowsBean.productName;

//        if (rowsBean.gift) {//显示药彩节
//            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_procurement_festival));
//        }

        try {

            if (rowsBean.productActivityTag != null && !TextUtils.isEmpty(rowsBean.productActivityTag.tagUrl)) {
                String url = rowsBean.productActivityTag.tagUrl;
                if (!rowsBean.productActivityTag.tagUrl.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + rowsBean.productActivityTag.tagUrl;
                }

                ImageHelper.with(mContext).load(url)
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<GlideDrawable>() {
                    @Override
                    public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                        list.add(resource);
                        setShowActivityTag(tv_shop_name, goodsName, list);
                    }
                });

            } else {
                setShowActivityTag(tv_shop_name, goodsName, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(TextView textView, String showName, List<Drawable> list) {
        SpannableStringBuilder shopName = getShopNameIcon(showName, list);
        if (!TextUtils.isEmpty(shopName)) textView.setText(shopName);
    }

    private SpannableStringBuilder getShopNameIcon(String shopName, List<Drawable> icons) {
        if (icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题

                drawable.setBounds(0, 0, ConvertUtils.dp2px(35), ConvertUtils.dp2px(13));

                MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                //占个位置
                spannableString.insert(0, "-");
                spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            return spannableString;
        }
        return null;
    }

    protected void itemClick(RefundProductListBean bean, View v) {
        if(bean.isRandom==1){
            // 随心拼商品不能跳转
            return;
        }
        if (bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE || bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_TITLE) {
            return;
        }
        try {
            RoutersUtils.open("ybmpage://productdetail/" + bean.productId + "?isMainProductVirtualSupplier=" + isVirtualSupplier);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
