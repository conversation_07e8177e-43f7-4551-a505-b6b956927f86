package com.ybmmarket20.adapter;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;

import androidx.core.content.ContextCompat;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.widget.RoundLinearLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.PromotionTagView;
import com.ybmmarket20.view.TagView;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 首页三列商品布局
 */
public class ProductGrid3AdapterCms extends GoodsListAdapter {
    private boolean whiteBg = false;
    private int maxTagCount;
    private int type = 0;
    private int marginLeft = 10;
    private int marginRight = 10;

    private int paddingLeft = 0;
    private int paddingRight = 0;

    public ProductGrid3AdapterCms(List<RowsBean> items) {
        super(R.layout.home_product_item_white, new ArrayList<RowsBean>());
        mData = items;
        this.maxTagCount = 2;
    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {


        if (rowsBean == null) {
            baseViewHolder.itemView.setVisibility(View.INVISIBLE);
            return;
        } else {
            baseViewHolder.itemView.setVisibility(View.VISIBLE);
        }
        ViewGroup.LayoutParams layoutParams = baseViewHolder.getConvertView().getLayoutParams();
        layoutParams.width = (UiUtils.getScreenWidth() - dp2px(marginLeft) - dp2px(marginRight) - dp2px(paddingLeft) - dp2px(paddingRight)) / 3;
        baseViewHolder.getConvertView().setLayoutParams(layoutParams);
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {

                    // 2005商品点击埋点
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("id", rowsBean.getId());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    if (!TextUtils.isEmpty(rowsBean.zhugeEventName)) {
                        // 这里的埋点有，首页商品，首页推荐商品, 首页常购清单,诊所专区clinic
                        XyyIoUtil.track(rowsBean.zhugeEventName, jsonObject, rowsBean);
                    }

                    currPosition = baseViewHolder.getAdapterPosition();
                    mOnItemClickListener.onItemClick(rowsBean);
                }
            }
        });

        RoundLinearLayout convertView = baseViewHolder.getView(R.id.rll_contain);
        if (whiteBg) {
            convertView.setBackgroundColor(Color.parseColor("#ffffff"));
        } else {
            convertView.setBackgroundColor(Color.parseColor("#00000000"));
        }


        ImageView iv_product = baseViewHolder.getView(R.id.iv_product);
        ImageView iv_tag_left = baseViewHolder.getView(R.id.iv_tag_left);
        ImageView home_time_bg = baseViewHolder.getView(R.id.home_time_bg);

        TextView tvName = baseViewHolder.getView(R.id.tv_name);

        if (rowsBean.getMarkerUrl().startsWith("http")) {
            ImageHelper.with(mContext).load(rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent)
                    .error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_tag_left);
        } else {
            if (TextUtils.isEmpty(rowsBean.getMarkerUrl())) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv_tag_left);
            } else {
                ImageHelper.with(mContext).load(AppNetConfig.LORD_TAG + rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_tag_left);
            }
        }

        //活动价
        boolean isShowUrl = rowsBean.isReducePrice() && rowsBean.isMarkerUrl();
        baseViewHolder.setText(R.id.tv_activity_price, String.valueOf("药采节价:" + UiUtils.transform(rowsBean.getReducePrice())));
        baseViewHolder.setGone(R.id.tv_activity_price, isShowUrl);

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl()).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_product);
        //商品区间价格-热销
        baseViewHolder.setText(R.id.tv_price, UiUtils.showProductPrice(rowsBean));
        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            baseViewHolder.setText(R.id.tv_price, String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
        }
        String num = "×" + rowsBean.getProductNumber();
        if (type > 0) {
            baseViewHolder.setText(R.id.tv_num, num);
        }
        //是否控销  isControl=1表示控销，=2表示不是控销
        if (rowsBean.getIsControl() == 1) {

            if (rowsBean.isPurchase()) {
                //控销可购买
                if (rowsBean.getPriceType() == 1) {
                    baseViewHolder.setText(R.id.tv_price, String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
                } else {
                    baseViewHolder.setText(R.id.tv_price, UiUtils.showProductPrice(rowsBean));
                }
                //是否是OEM协议商品
                setIsOEM(baseViewHolder, rowsBean);
            } else {
                //控销不可购买
                baseViewHolder.setText(R.id.tv_price, "暂无购买权限");
            }
        } else {
            //是否是OEM协议商品
            setIsOEM(baseViewHolder, rowsBean);
        }
        baseViewHolder.setText(R.id.tv_name, rowsBean.getShowName()).setText(R.id.tv_spec, rowsBean.getSpec());

        if (rowsBean.getStatus() == 2 || rowsBean.getStatus() == 4 || rowsBean.getAvailableQty() <= 0) {
            home_time_bg.setVisibility(View.VISIBLE);
            baseViewHolder.setTextColor(R.id.tv_price, UiUtils.getColor(R.color.record_red));
        } else {
            home_time_bg.setVisibility(View.INVISIBLE);
            baseViewHolder.setTextColor(R.id.tv_price, UiUtils.getColor(R.color.record_red));
        }

//        //是否显示独家标示
//        baseViewHolder.setVisible(R.id.tv_exclusive, rowsBean.getAgent() == 1);
//        //是否显示医保标识
//        baseViewHolder.setVisible(R.id.tv_health_insurance, rowsBean.getIsUsableMedicalStr() == 1);
//        //是否显示药采节
//        baseViewHolder.setVisible(R.id.tv_procurement_festival, rowsBean.isGift());

        // 设置促销活动标签
        PromotionTagView mPtv = baseViewHolder.getView(R.id.view_ptv);
        mPtv.setShowData(rowsBean.getActivityTag());

        setGoodsName(tvName, rowsBean);

        TagView rlIconType = baseViewHolder.getView(R.id.rl_icon_type);
        //rlIconType.bindData(rowsBean.getTagList(), maxTagCount, isGone(baseViewHolder.getAdapterPosition()));
        rlIconType.bindData(rowsBean.getTagList(), maxTagCount, false);
        handleAuditPassedVisible(baseViewHolder);
    }

    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, RowsBean rowsBean) {

        List<Drawable> list = new ArrayList<>();

        String goodsName = rowsBean.getShowName();

        if (rowsBean.getAgent() == 1) {//显示独家
//            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_dujia));
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_dujia);
            //drawable.setBounds(0, 0, ConvertUtils.dp2px(28), ConvertUtils.dp2px(15));
            list.add(drawable);
        }

        if (rowsBean.isGift()) {//显示药彩节
            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_procurement_festival));
        }

        try {

            if (rowsBean.getActivityTag() != null && !TextUtils.isEmpty(rowsBean.getActivityTag().tagUrl)) {
                String url = rowsBean.getActivityTag().tagUrl;
                if (!rowsBean.getActivityTag().tagUrl.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + rowsBean.getActivityTag().tagUrl;
                }

                ImageHelper.with(mContext).load(url)
                        .asBitmap()
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
                        list.add(ImageUtil.getDrawableForHeight(mContext, resource, tv_shop_name.getMeasuredHeight()));
                        setShowActivityTag(tv_shop_name, goodsName, list);
                    }
                });

            } else {
                setShowActivityTag(tv_shop_name, goodsName, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(TextView textView, String showName, List<Drawable> list) {
        SpannableStringBuilder shopName = getShopNameIcon(textView, showName, list);
        if (!TextUtils.isEmpty(shopName)) {
            textView.setText(shopName);
        }
    }

    private SpannableStringBuilder getShopNameIcon(TextView tvName, String shopName, List<Drawable> icons) {
        if (shopName != null && icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题
                if (drawable != null) {

                    float tvHeight = tvName.getTextSize();
                    float drawableWith = (drawable.getIntrinsicWidth() / (float) drawable.getIntrinsicHeight()) * tvHeight;
                    drawable.setBounds(0, 0, (int) (drawableWith + 0.5), (int) (tvHeight + 0.5));

                    MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                    //占个位置
                    spannableString.insert(0, "-");
                    spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
            return spannableString;
        }
        return null;
    }

    private void setIsOEM(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {
        if (rowsBean.getIsOEM()) {
            //是否签署协议
            if (rowsBean.getSignStatus() == 1) {

            } else {
                ((TextView) baseViewHolder.getView(R.id.tv_price))
                        .setTextSize(12);
                baseViewHolder.setText(R.id.tv_price, "价格签署协议可见");
            }
        }

        if (rowsBean.showAgree == 0) {
            ((TextView) baseViewHolder.getView(R.id.tv_price)).setTextSize(12);
            baseViewHolder.setText(R.id.tv_price, "价格签署协议可见");
        }
    }


    private boolean isGone(int postion) {// 三个都是空才会隐藏标签，其它都显示标签位置
        if (!isEmpty(postion)) {
            return false;
        }
        int next = -1;
        int pre = -1;
        switch (postion % 3) {
            case 0:// +1 + 2
                next = postion + 2;
                pre = postion + 1;
                break;
            case 1: // -1 +1
                next = postion + 1;
                pre = postion - 1;
                break;
            case 2:// -1 - 2
                next = postion - 2;
                pre = postion - 1;
                break;
        }
        return isEmpty(next) && isEmpty(pre);
    }

    private boolean isEmpty(int postion) {
        if (getData() == null || getData().isEmpty()) {
            return true;
        }
        if (postion < 0 || postion >= getData().size()) {
            return true;
        }
        RowsBean rowsBean = (RowsBean) getData().get(postion);
        if (rowsBean == null || rowsBean.getTagList() == null || rowsBean.getTagList().isEmpty()) {
            return true;
        }
        return false;
    }

    // 设置集合推荐模块的左右外边距值
    public void setMargin(int marginLeft, int marginRight) {
        this.marginLeft = marginLeft;
        this.marginRight = marginRight;
    }

    public void setPadding(int paddingLeft, int paddingRight) {
        this.paddingLeft = paddingLeft;
        this.paddingRight = paddingRight;
    }

    public void setWhiteBg(boolean isWhiteBg) {
        whiteBg = isWhiteBg;
    }

    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder) {
        baseViewHolder.setGone(R.id.tv_price, AuditStatusSyncUtil.getInstance().isAuditFirstPassed());
        baseViewHolder.setGone(R.id.tv_audit_passed_visible, !AuditStatusSyncUtil.getInstance().isAuditFirstPassed());
    }
}

