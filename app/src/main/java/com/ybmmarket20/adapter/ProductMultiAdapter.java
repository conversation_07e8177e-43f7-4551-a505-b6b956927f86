package com.ybmmarket20.adapter;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.ProductEditLayout;
import com.ybmmarket20.view.PromotionTagView;
import com.ybmmarket20.view.TagView;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ProductMultiAdapter extends YBMBaseMultiItemAdapter<RowsBean> {

    protected int currPosition = -1;
    private int maxTagCount;
    protected int pageFrom = ProductEditLayout.FROMPAGE_HOME;

    public ProductMultiAdapter(List<RowsBean> data) {
        super(data);
        addItemType(RowsBean.content_11, R.layout.product_multi_content_01);
        addItemType(RowsBean.content_31, R.layout.product_multi_content_02);
        this.maxTagCount = 3;
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {

        switch (rowsBean.getItemType()) {
            case RowsBean.content_11:
                bindProductMultiContent01(ybmBaseHolder, rowsBean);
                break;
            case RowsBean.content_31:
                bindProductMultiContent02(ybmBaseHolder, rowsBean);
                break;
        }
    }

    /*
     * 商品类型1
     * */
    private void bindProductMultiContent01(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {
//        // 控销价，毛利
//        TextView tvRetailPrice = baseViewHolder.getView(R.id.tv_retail_price);
//        /*RelativeLayout ly_shop_price = baseViewHolder.getView(R.id.shop_price_layout);
//        TextView tv_shop_price_kxj_number = baseViewHolder.getView(R.id.shop_price_kxj_number_tv);
//        TextView tv_shop_price_ml_number = baseViewHolder.getView(R.id.shop_price_ml_number_tv);
//        TextView shop_price_kxj_title_tv = baseViewHolder.getView(R.id.shop_price_kxj_title_tv);*/
//
//        //如果建议零售价存在或者是控销并且不可购买，控销不显示
//        if (TextUtils.isEmpty(rowsBean.getSuggestPrice()) && TextUtils.isEmpty(rowsBean.getUniformPrice())) {
//            tvRetailPrice.setVisibility(View.INVISIBLE);
//        } else {
//            tvRetailPrice.setVisibility(View.VISIBLE);
//            //建议零售价
//            if (!TextUtils.isEmpty(rowsBean.getSuggestPrice())) {
//                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
//                        mContext.getResources().getString(R.string.product_list_lsj_title),
//                        StringUtil.getUniformPrice2Double(rowsBean.getSuggestPrice()),
//                        rowsBean.getGrossMargin()));
//            }
//            //控销价
//            if (!TextUtils.isEmpty(rowsBean.getUniformPrice())) {
//                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
//                        mContext.getResources().getString(R.string.product_list_kxj_title),
//                        StringUtil.getUniformPrice2Double(rowsBean.getUniformPrice()),
//                        rowsBean.getGrossMargin()));
//            }
//        }

        //tagList
        TagView rlIconType = baseViewHolder.getView(R.id.rl_icon_type);
        rlIconType.bindData(rowsBean.getTagList(), maxTagCount, isGone(baseViewHolder.getAdapterPosition()));
        setProductItemData(baseViewHolder, rowsBean, "/" + rowsBean.getMediumPackageTitle(), rowsBean.getSpec());
    }

    /*
     * 商品类型2
     * */
    private void bindProductMultiContent02(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {

        //tagList
        TagView rlIconType = baseViewHolder.getView(R.id.rl_icon_type);
        rlIconType.bindData(rowsBean.getTagList(), maxTagCount, false);
        setProductItemData(baseViewHolder, rowsBean, "/" + rowsBean.getMediumPackageTitle(), rowsBean.getSpec());

    }

    private void setProductItemData(YBMBaseHolder baseViewHolder, RowsBean rowsBean, String mediumPackageTitle, String s) {
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                // 2006 商品点击埋点
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("id", rowsBean.getId());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (!TextUtils.isEmpty(rowsBean.zhugeEventName)) {
                    // 这里的埋点有，首页商品，首页推荐商品, 首页常购清单,诊所专区clinic
                    XyyIoUtil.track(rowsBean.zhugeEventName, jsonObject, rowsBean);
                }

                if (mOnItemClickListener != null) {
                    currPosition = baseViewHolder.getAdapterPosition();
                    mOnItemClickListener.onItemClick(rowsBean);
                }
            }
        });
        ImageView iv_product = baseViewHolder.getView(R.id.iv_product);
        ImageView iv_tag_left = baseViewHolder.getView(R.id.iv_tag_left);
        TextView home_time_bg = baseViewHolder.getView(R.id.shop_no_limit_tv01);
        TextView tv_shop_pack_size = baseViewHolder.getView(R.id.shop_price_tv);

        ProductEditLayout editLayout = baseViewHolder.getView(R.id.el_edit);

        TextView tvName = baseViewHolder.getView(R.id.tv_name);

        if (rowsBean.getMarkerUrl() != null && rowsBean.getMarkerUrl().startsWith("http")) {
            ImageHelper.with(mContext).load(rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_tag_left);
        } else {
            if (TextUtils.isEmpty(rowsBean.getMarkerUrl())) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv_tag_left);
            } else {
                ImageHelper.with(mContext).load(AppNetConfig.LORD_TAG + rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_tag_left);
            }
        }

        //活动价
        boolean isShowUrl = rowsBean.isReducePrice() && rowsBean.isMarkerUrl();
        baseViewHolder.setText(R.id.tv_activity_price, String.valueOf("药采节价:" + UiUtils.transform(rowsBean.getReducePrice())));
        baseViewHolder.setGone(R.id.tv_activity_price, isShowUrl);

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getProductImg()).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(iv_product);
        //商品区间价格-热销
        baseViewHolder.setText(R.id.tv_price, UiUtils.showProductPrice(rowsBean));
        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            baseViewHolder.setText(R.id.tv_price, String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
        }

        //中包装数量,不可拆零才显示,4.2.0以后所有的商品都显示
        if (null != tv_shop_pack_size) {
            tv_shop_pack_size.setText(mediumPackageTitle);
            tv_shop_pack_size.setVisibility(!TextUtils.isEmpty(rowsBean.getMediumPackageTitle()) ? View.VISIBLE : View.GONE);
        }


        boolean isBuy = true;
        //是否控销  isControl=1表示控销，=2表示不是控销
        if (rowsBean.getIsControl() == 1) {

            if (rowsBean.isPurchase()) {
                //控销可购买
                if (rowsBean.getPriceType() == 1) {
                    baseViewHolder.setText(R.id.tv_price, String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
                } else {
                    baseViewHolder.setText(R.id.tv_price, UiUtils.showProductPrice(rowsBean));
                }
                //是否是OEM协议商品
                setIsOEM(baseViewHolder, rowsBean);
            } else {
                //控销不可购买
                isBuy = false;
                baseViewHolder.setText(R.id.tv_price, "暂无购买权限");
                View view = baseViewHolder.getView(R.id.shop_price_layout);
                if (view != null) view.setVisibility(View.GONE);
            }
        } else {
            //是否是OEM协议商品
            setIsOEM(baseViewHolder, rowsBean);
        }
        baseViewHolder.setText(R.id.tv_name, rowsBean.getShowName()).setText(R.id.tv_spec, s);

        if (rowsBean.isSoldOut() || rowsBean.getAvailableQty() <= 0) {////2是已售罄//4是已下架
            String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
            String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
            home_time_bg.setVisibility(View.VISIBLE);
            home_time_bg.setText(rowsBean.getStatus() == 2 ? sellOutStr : soldOutStr);
        } else {
            home_time_bg.setVisibility(View.INVISIBLE);
        }

//        //是否显示独家标示
//        baseViewHolder.setVisible(R.id.tv_exclusive, rowsBean.getAgent() == 1);
//        //是否显示医保标识
//        baseViewHolder.setVisible(R.id.tv_health_insurance, rowsBean.getIsUsableMedicalStr() == 1);
//        //是否显示药采节
//        baseViewHolder.setVisible(R.id.tv_procurement_festival, rowsBean.isGift());

        // 设置促销活动标签
        PromotionTagView mPtv = baseViewHolder.getView(R.id.view_ptv);
        mPtv.setShowData(rowsBean.getActivityTag());

        setGoodsName(tvName, rowsBean);

        //加减按钮
        if (editLayout != null) {
            editLayout.bindData(rowsBean.getId(), rowsBean.getStatus(), isBuy, pageFrom, (ImageView) baseViewHolder.getView(R.id.icon), true, rowsBean.getStepNum(), rowsBean.getIsSplit() == 1);
        }
    }

    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, RowsBean rowsBean) {

        List<Drawable> list = new ArrayList<>();

        String goodsName = rowsBean.getShowName();

        if (rowsBean.getAgent() == 1) {//显示独家
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_dujia);
            drawable.setBounds(0, 0, ConvertUtils.dp2px(28), ConvertUtils.dp2px(15));
            list.add(drawable);
        }

        if (rowsBean.isGift()) {//显示药彩节
            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_procurement_festival));
        }

        try {

            if (rowsBean.getActivityTag() != null && !TextUtils.isEmpty(rowsBean.getActivityTag().tagUrl)) {
                String url = rowsBean.getActivityTag().tagUrl;
                if (!rowsBean.getActivityTag().tagUrl.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + rowsBean.getActivityTag().tagUrl;
                }

                ImageHelper.with(mContext).load(url)
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<GlideDrawable>() {
                    @Override
                    public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                        list.add(resource);
                        setShowActivityTag(tv_shop_name, goodsName, list);
                    }
                });

            } else {
                setShowActivityTag(tv_shop_name, goodsName, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(TextView textView, String showName, List<Drawable> list) {
        SpannableStringBuilder shopName = getShopNameIcon(showName, list);
        if (!TextUtils.isEmpty(shopName)) textView.setText(shopName);
    }

    private SpannableStringBuilder getShopNameIcon(String shopName, List<Drawable> icons) {
        if (icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题

                if (drawable != null) {
                    Rect dRect = drawable.getBounds();
                    int height = ConvertUtils.dp2px(13);
                    float width = (int) (((float) dRect.width() / (float) dRect.height()) * height);
                    if (dRect.height() != 0)
                        drawable.setBounds(0, 0, (int) width, ConvertUtils.dp2px(13));
                    else drawable.setBounds(0, 0, ConvertUtils.dp2px(35), ConvertUtils.dp2px(13));
                    MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                    //占个位置
                    spannableString.insert(0, "-");
                    spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);

                }
            }
            return spannableString;
        }
        return null;
    }

    @Override
    public void onAttachedToRecyclerView(final RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
        if (manager instanceof GridLayoutManager) {
            final GridLayoutManager gridManager = ((GridLayoutManager) manager);
            gridManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    int type = getItemViewType(position);
                    // RowsBean.content_11
                    // RowsBean.content_31
                    if (type == RowsBean.content_11) {
                        return 3;
                    } else {
                        return 1;
                    }
                }

            });
        }
    }

    /**
     * 设置控销价或者零售价
     *
     * @param tv_shop_price_kxj_number
     * @param tv_shop_price_ml_number
     * @param uniformPrice2Double
     */
    private void setSuggestOrGrossMargin(String grossMarginStr, TextView tv_shop_price_kxj_number, TextView tv_shop_price_ml_number, String uniformPrice2Double) {
        try {
            //建议零售价或者控销价
            tv_shop_price_kxj_number.setText(uniformPrice2Double);
            //毛利率
            tv_shop_price_ml_number.setText(grossMarginStr);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    private void setIsOEM(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {
        if (rowsBean.getIsOEM()) {
            // 是否签署协议
            if (rowsBean.getSignStatus() == 1) {

            } else {
                SpannableString spannableString = new SpannableString("价格签署协议可见 ");
                spannableString.setSpan(new AbsoluteSizeSpan(12, true), 0, spannableString.length() - 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                baseViewHolder.setText(R.id.tv_price, spannableString);
            }
        }
        //是否符合协议标准展示价格,1:符合0:不符合
        if (rowsBean.showAgree == 0) {
            SpannableString spannableString = new SpannableString("价格签署协议可见 ");
            spannableString.setSpan(new AbsoluteSizeSpan(12, true), 0, spannableString.length() - 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            baseViewHolder.setText(R.id.tv_price, spannableString);
        }
    }

    private boolean isGone(int postion) {// 三个都是空才会隐藏标签，其它都显示标签位置
        if (!isEmpty(postion)) {
            return false;
        }
        int next = -1;
        int pre = -1;
        switch (postion % 3) {
            case 0:// +1 + 2
                next = postion + 2;
                pre = postion + 1;
                break;
            case 1: // -1 +1
                next = postion + 1;
                pre = postion - 1;
                break;
            case 2:// -1 - 2
                next = postion - 2;
                pre = postion - 1;
                break;
        }
        return isEmpty(next) && isEmpty(pre);
    }

    private boolean isEmpty(int postion) {
        if (getData() == null || getData().isEmpty()) {
            return true;
        }
        if (postion < 0 || postion >= getData().size()) {
            return true;
        }
        RowsBean rowsBean = (RowsBean) getData().get(postion);
        if (rowsBean == null || rowsBean.getTagList() == null || rowsBean.getTagList().isEmpty()) {
            return true;
        }
        return false;
    }

    protected boolean isFixedViewType(int type) {
        return type == EMPTY_VIEW || type == HEADER_VIEW || type == FOOTER_VIEW || type == LOADING_VIEW;
    }

    private SpanSizeLookup mSpanSizeLookup;

    public interface SpanSizeLookup {
        int getSpanSize(GridLayoutManager gridLayoutManager, int position);
    }

    public void setSpanSizeLookup(SpanSizeLookup spanSizeLookup) {
        this.mSpanSizeLookup = spanSizeLookup;
    }


    protected OnListViewItemClickListener mOnItemClickListener = null;

    public interface OnListViewItemClickListener {
        void onItemClick(RowsBean rows);
    }

    public void setOnItemClickListener(OnListViewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    public int getCurrPosition() {
        return currPosition;
    }
}
