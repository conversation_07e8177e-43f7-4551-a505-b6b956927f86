package com.ybmmarket20.adapter;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.tbruyelle.rxpermissions2.Permission;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.VideoPicPreviewEntity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.YBMBaseFragment;
import com.ybmmarket20.fragments.AddImage3Fragment;
import com.ybmmarket20.fragments.AddImageFragment;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.view.ShowBigBitmapPopPublish;
import com.ybmmarket20.view.ShowBottomAddImageDialog;
import com.ybmmarket20.view.ShowBottomSheetDialog;

import java.io.File;
import java.util.List;

public class PublishPics3Adapter extends BaseAdapter {

    private AddImage3Fragment fragment;
    List<VideoPicPreviewEntity> localPathList;
    private int allowedSize;
    private boolean allow_photo;
    private boolean allow_gallery;
    private boolean allow_video;
    private TextView tv_hint;
    private boolean firstItemAdd = true;//第一个按钮是否为增加按钮
    private PublishPics3Adapter.PublishImageViewOnClickListener onClickListener;
    private boolean isCanClick; //添加按钮是否能点击
    protected String PHOTO_DIR;

    public PublishPics3Adapter(AddImage3Fragment fragment, List<VideoPicPreviewEntity> localPathList, int size, boolean allow_video, boolean allow_gallery,
                               boolean allow_photo, boolean firstItemAdd, TextView tv_hint) {
        this.fragment = fragment;
        this.allowedSize = size;
        this.allow_gallery = allow_gallery;
        this.allow_photo = allow_photo;
        this.allow_video = allow_video;
        this.firstItemAdd = firstItemAdd;
        this.tv_hint = tv_hint;
        this.localPathList = localPathList;
        int size1 = allowedSize - localPathList.size();
        onClickListener = new PublishPics3Adapter.PublishImageViewOnClickListener(this, size1, allow_video, allow_gallery, allow_photo, false);
    }

    @Override
    public int getCount() {
        int size = 0;
        if (!firstItemAdd) {
            size = localPathList.size();
        } else if (localPathList.size() + 1 < allowedSize) {
            size = localPathList.size() + 1;
        } else {
            size = allowedSize;
        }
        if ((!firstItemAdd && size == 0) || size == 1) {
            tv_hint.setVisibility(View.VISIBLE);
        } else {
            tv_hint.setVisibility(View.GONE);
        }
        if (localPathList.size() == 1 && allowedSize == 1) {
            tv_hint.setVisibility(View.GONE);
        }
        return size;
    }

    @Override
    public Object getItem(int pos) {
        return pos;
    }

    @Override
    public long getItemId(int pos) {
        return pos;
    }

    PublishPics3Adapter.ViewHolder holder;

    @Override
    public View getView(int pos, View convertView, ViewGroup arg2) {
        if (convertView == null) {
            convertView = LayoutInflater.from(fragment.getNotNullActivity()).inflate(R.layout.item_add_image3, null);
            holder = new PublishPics3Adapter.ViewHolder();
            convertView.setTag(holder);
        } else {
            holder = (PublishPics3Adapter.ViewHolder) convertView.getTag();
        }
        convertView.setId(pos);
        holder.iv_preview = (ImageView) convertView.findViewById(R.id.iv_preview);
        holder.iv_preview_icon = (ImageView) convertView.findViewById(R.id.iv_preview_icon);
        holder.iv_delete = (ImageView) convertView.findViewById(R.id.iv_delete);
        holder.iv_preview_icon.setVisibility(View.GONE);

        if (localPathList.size() == pos && firstItemAdd) {//添加按钮
            holder.iv_delete.setVisibility(View.GONE);
            int size = allowedSize - localPathList.size();
            holder.iv_preview_icon.setVisibility(View.GONE);
            if (isCanClick) {
                ImageHelper.with(BaseYBMApp.getApp().getCurrActivity()).load(R.drawable.icon_add_image_apply_for_convoy).asBitmap().skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).dontAnimate().dontTransform().into(holder.iv_preview);
                convertView.setOnClickListener(new PublishPics3Adapter.PublishImageViewOnClickListener(this, size, allow_video, allow_gallery, allow_photo, false));
            } else {
                convertView.setOnClickListener(null);
                holder.iv_preview.setImageResource(R.drawable.add_image_false);
                ImageHelper.with(BaseYBMApp.getApp().getCurrActivity()).load(R.drawable.add_image_false).asBitmap().skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).dontAnimate().dontTransform().into(holder.iv_preview);
            }

            if (localPathList.size() == allowedSize) {
                convertView.setVisibility(View.GONE);
            } else {
                convertView.setVisibility(View.VISIBLE);
            }
        } else {//文件列表
            holder.iv_delete.setVisibility(View.VISIBLE);
            convertView.setOnClickListener(onClickListener);
            if (localPathList.get(pos).getUrl_type() == VideoPicPreviewEntity.URL_NETWORK_TYPE) {//网络图片、或者视频
                ImageHelper.with(BaseYBMApp.getApp().getCurrActivity()).load(localPathList.get(pos).getPre_url()).asBitmap().diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(holder.iv_preview);
            } else {
                ImageHelper.with(BaseYBMApp.getApp().getCurrActivity()).load(new File(localPathList.get(pos).getPre_url())).asBitmap().skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).dontAnimate().dontTransform().into(holder.iv_preview);
            }
            if (localPathList.get(pos).getFile_type() == VideoPicPreviewEntity.FILE_PIC_TYPE) {
                holder.iv_preview_icon.setVisibility(View.GONE);
            } else if (localPathList.get(pos).getFile_type() == VideoPicPreviewEntity.FILE_VIDEO_TYPE) {
                holder.iv_preview_icon.setVisibility(View.VISIBLE);
            }
        }
        holder.iv_delete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDeleteDialog(new AlertDialogEx.OnClickListener() {
                    @Override
                    public void onClick(AlertDialogEx dialog, int button) {
                        ((BaseActivity) fragment.getNotNullActivity()).hideSoftInput();
                        if (pos < localPathList.size()) {
                            int urlType = localPathList.get(pos).getUrl_type();
                            int fileType = localPathList.get(pos).getFile_type();
                            String v_url = localPathList.get(pos).getV_url();
                            String p_url = localPathList.get(pos).getPre_url();
                            if (fileType == VideoPicPreviewEntity.FILE_VIDEO_TYPE) {
                                //点击视频
                            } else {
                                if (localPathList != null && localPathList.size() > pos) {
                                    localPathList.remove(pos);
                                    PublishPics3Adapter.this.notifyDataSetChanged();
                                }
                            }
                        }
                    }
                });
            }
        });
        return convertView;
    }

    private void showDeleteDialog(AlertDialogEx.OnClickListener listener) {

        AlertDialogEx dialogEx = new AlertDialogEx(fragment.getNotNullActivity());
        dialogEx.setMessage("确认删除当前图片吗？").setCancelButton("取消", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("确定", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    class ViewHolder {
        ImageView iv_preview, iv_preview_icon, iv_delete;//iv_publish_item  iv_preview
    }

    public void refreshGv(int pos) {
        localPathList.remove(pos);
        notifyDataSetChanged();
    }

    public void setIsCanClick(boolean isCanClick) {
        this.isCanClick = isCanClick;
    }


    public class PublishImageViewOnClickListener implements View.OnClickListener {
        //      protected final AlertDialogEx choiceDialog;
        private int size;
        private boolean detele;
        ShowBottomAddImageDialog mDialogLayout;

        public void setDetele(boolean detele) {
            this.detele = detele;
        }

        public PublishImageViewOnClickListener(PublishPics3Adapter publishPicsAdapter, int size, boolean allow_video, boolean allow_gallery, boolean allow_photo, boolean detele) {
            this.size = size;
            this.detele = detele;
            mDialogLayout = new ShowBottomAddImageDialog(fragment.getNotNullActivity());
            mDialogLayout.setOnCancelClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mDialogLayout.dismiss();
                }
            });
            mDialogLayout.setOnPhotoGalleryClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    // 调用图库
                    FileUtil.startPicturesForResult(fragment);
                    mDialogLayout.dismiss();

                }
            });
            mDialogLayout.setOnTakingPicturesClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    //调用系统相机程序
                    FileUtil.startCameraForResult(fragment, getPhotoFile().getAbsolutePath());
                    fragment.setPicture(getPhotoFile());
                    mDialogLayout.dismiss();
                }
            });
        }

        @Override
        public void onClick(View v) {
            int pos = v.getId();
            ((BaseActivity) fragment.getNotNullActivity()).hideSoftInput();
            if (pos < localPathList.size()) {
                int urlType = localPathList.get(pos).getUrl_type();
                int fileType = localPathList.get(pos).getFile_type();
                String v_url = localPathList.get(pos).getV_url();
                String p_url = localPathList.get(pos).getPre_url();
                if (fileType == VideoPicPreviewEntity.FILE_VIDEO_TYPE) {
                    //点击视频
                } else {
                    ShowBigBitmapPopPublish bigBitmapPop;
                    if (detele) {
                        bigBitmapPop = new ShowBigBitmapPopPublish(localPathList.get(pos).getPre_url(), pos, new ShowBigBitmapPopPublish.RefreshGvListener() {
                            @Override
                            public void refreshGv(int pos) {
                                if (localPathList != null && localPathList.size() > pos) {
                                    localPathList.remove(pos);
                                    PublishPics3Adapter.this.notifyDataSetChanged();
                                }
                            }
                        });
                    } else {
                        bigBitmapPop = new ShowBigBitmapPopPublish(localPathList.get(pos).getPre_url(), pos, null);
                    }
                    bigBitmapPop.show(v);
                }
            } else {
//                choiceDialog.show();
                mDialogLayout.show();
            }

        }
    }

    //生成相机来的图片
    public File getPhotoFile() {

        File file = new File(fragment.getNotNullActivity().getExternalCacheDir().getAbsolutePath(), "xyy.jpg");
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        LogUtils.d(file.getAbsolutePath());
        return file;
    }


}