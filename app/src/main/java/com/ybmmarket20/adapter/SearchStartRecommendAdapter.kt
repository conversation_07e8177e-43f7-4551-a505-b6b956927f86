package com.ybmmarket20.adapter

import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchStartRecommendRows
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import org.json.JSONObject

/**
 * 搜索启动页品类adapter
 */
class SearchStartRecommendAdapter(
    data: List<SearchStartRecommendRows>,
    val flowData: FlowData? = null
): YBMBaseAdapter<SearchStartRecommendRows>(R.layout.item_search_start_recommend, data){

    private var rvWidth = 0

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SearchStartRecommendRows?) {
        whenAllNotNull(baseViewHolder, t) { holder, rows ->
            val convertView = holder.itemView
            if (rvWidth == 0) rvWidth = (ScreenUtils.getScreenWidth(mContext) * 0.6).toInt()
            val lp = convertView.layoutParams
            lp.width = rvWidth
            holder.setText(R.id.tv_search_start_item_title, rows.name)
            holder.setText(R.id.tv_search_start_recommend_buy_count, rows.linkText)
            val adapter = getGoodsListAdapter(rows)
            val rv = holder.getView<RecyclerView>(R.id.rv_search_start_recommend_goods)
            rv.layoutManager = WrapLinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            rv.adapter = adapter
            holder.getView<TextView>(R.id.tv_search_start_recommend_buy_count).setOnClickListener {
                RoutersUtils.open(rows.link)
                XyyIoUtil.track("action_SearchStartup_chartEntrance_Click", JSONObject().also {
                    it.put("name", rows.linkText)
                    it.put("action", rows.link)
                })
            }
        }
    }

    /**
     * 获取adapter并缓存
     */
    fun getGoodsListAdapter(searchStartRecommendRows: SearchStartRecommendRows): SearchStartRecommendGoodsAdapter? {
        var goodsListAdapter = searchStartRecommendRows.goodsListAdapter
        if (goodsListAdapter == null) {
            goodsListAdapter = searchStartRecommendRows.skuList?.let {
                SearchStartRecommendGoodsAdapter(it, searchStartRecommendRows.type, flowData, searchStartRecommendRows.link, searchStartRecommendRows.name)
            }
        }
        return goodsListAdapter
    }
}