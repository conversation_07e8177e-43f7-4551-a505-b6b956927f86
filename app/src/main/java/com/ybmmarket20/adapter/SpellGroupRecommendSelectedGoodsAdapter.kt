package com.ybmmarket20.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import kotlin.math.absoluteValue

class SpellGroupRecommendSelectedGoodsAdapter(
    data: List<RowsBean>,
    val spellGroupRecommendGoodsLiveData: LiveData<SpellGroupRecommendGoodsBean>?,
    val mainGoodsBean: SpellGroupGoodsItem?,
    val addCartCallback: AddCartCallback
) :
    YBMBaseAdapter<RowsBean>(R.layout.item_spell_group_recommend_select_goods, data) {

    //埋点参数
    private var flowData: BaseFlowData? = null
    private val traceProductData = SparseArray<String>()
    var jgTrackBean: JgTrackBean? =null

    private fun clearTrackData() {
        traceProductData.clear()
    }

    fun setFlowData(flowData: BaseFlowData?) {
        this.flowData = flowData
        clearTrackData()
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            ImageUtil.load(
                mContext,
                AppNetConfig.LORD_IMAGE + bean.imageUrl,
                holder.getView(R.id.iv_goods)
            )
            ImageUtil.loadNoPlace(
                mContext,
                AppNetConfig.LORD_TAG + bean.markerUrl,
                holder.getView(R.id.iv_goods_tag)
            )
            val showName = holder.getView<TextView>(R.id.tv_goods_title)
            showName.TextWithPrefixTag(bean.tags?.titleTags, bean.productName)
            showName.setLineSpacing(0f, 1.1f)
            holder.setText(R.id.tv_price, getPriceSpannableBuilder(bean))

            //加购
            val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
            pel.jgTrackBean = jgTrackBean
            pel.bindData(
                bean.productId,
                bean.status,
                true,
                true,
                1,
                bean.isSplit == 1,
                "${spellGroupRecommendGoodsLiveData?.value?.goodsIdMapping?.get(bean.productId) ?: 0}"
            )
            pel.setOnAddCartListener(object : ProductEditLayoutSuiXinPin.AddCartListener {
                override fun onPreAddCart(params: RequestParams?): RequestParams =
                    RequestParams()

                override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
//                        mAddChargeLocalCallback?.invoke(pel)
                    val spellGroupGoodsItem = SpellGroupGoodsItem(
                        bean.imageUrl,
                        bean.markerUrl,
                        bean.showName,
                        bean.actSuiXinPin?.suiXinPinPrice ?: "0",
                        bean.productUnit,
                        "${bean.fob}",
                        0,
                        bean.productId,
                        bean.stepNum,
                        bean.isSplit,
                        nearEffect = bean.nearEffect
                    )

                    val goodsAmount =
                        ((params?.amount?.toIntOrNull() ?: 0) - (params?.preAmount ?: 0))
                    addCartCallback.addCart(
                        spellGroupGoodsItem,
                        goodsAmount > 0, goodsAmount.absoluteValue
                    )

                    XyyIoUtil.track(
                        "page_ListPage_Purchase", hashMapOf(
                            "commodityId" to (mainGoodsBean?.skuId ?: ""),
                            "commodityName" to (bean.showName ?: ""),
                            "sptype" to (flowData?.spType ?: ""),
                            "spid" to (flowData?.spId ?: ""),
                            "sid" to (flowData?.sId ?: ""),
                            "direct" to "5",
                            "source" to bean.sourceType,
                            "index" to "${holder.bindingAdapterPosition}"
                        )
                    )
                }
            })
            //厂商
            holder.setText(R.id.tv_manufactor, bean.manufacturer)
            //效期
            holder.setText(R.id.tv_effect, "有效期：${bean.nearEffect}")

            //售罄
            val isSettleOut = bean.status == 2 || bean.status == 4 || bean.availableQty <= 0
            holder.getView<TextView>(R.id.tv_settle_out).visibility =
                if (isSettleOut) View.VISIBLE else View.GONE
            holder.getView<LinearLayout>(R.id.ll_subscribe).visibility =
                if (isSettleOut) View.VISIBLE else View.GONE
            pel.visibility = if (!isSettleOut) View.VISIBLE else View.GONE
            //到货通知
            val tvGoodsSubscribe = holder.getView<TextView>(R.id.tv_goods_subscribe)
            val ivGoodsSubscribe = holder.getView<ImageView>(R.id.iv_goods_subscribe)
            if (bean.favoriteStatus == 2) {
                ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_arrival)
                tvGoodsSubscribe.text = "到货通知"
            } else {
                ivGoodsSubscribe.setBackgroundResource(R.drawable.icon_goods_subscribe)
                tvGoodsSubscribe.text = " 已订阅"
            }
            ivGoodsSubscribe.setOnClickListener {
                addCartCallback.checkCollect(
                    bean,
                    holder.adapterPosition,
                    this
                )
            }

            // 商品列表曝光埋点
            if (flowData != null && traceProductData[holder.bindingAdapterPosition] == null) {
                XyyIoUtil.track(
                    "page_ListPage_Exposure", hashMapOf(
                        "commodityId" to (mainGoodsBean?.skuId ?: ""),
                        "commodityName" to (mainGoodsBean?.goodsTitle ?: ""),
                        "sptype" to (flowData?.spType ?: ""),
                        "spid" to (flowData?.spId ?: ""),
                        "sid" to (flowData?.sId ?: ""),
                        "direct" to "5",
                        "source" to bean.sourceType,
                        "index" to "${holder.bindingAdapterPosition}"
                    )
                )
                traceProductData.put(holder.bindingAdapterPosition, bean.productId)
            }
        }
    }

    fun getPriceWithFormat(src: String?, littleSize: Int, mainSize: Int): SpannableStringBuilder {
        val builder = SpannableStringBuilder(src)
        if (src == null) return builder
        if (!src.contains(".")) builder.append(".00")
        val index = builder.toString().indexOf(".")
        val symbolSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(littleSize.toFloat()))
        val littleSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(littleSize.toFloat()))
        val mainSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(mainSize.toFloat()))
        builder.setSpan(symbolSpan, 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(mainSpan, 1, index, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(littleSpan, index, builder.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        return builder
    }


    /**
     * 设置价格样式
     */
    private fun getPriceSpannableBuilder(bean: RowsBean): SpannableStringBuilder {
        //随心拼价格
        val suiXinPinPrice = bean.actSuiXinPin?.suiXinPinPrice ?: "0"


        val priceBuilder = getPriceWithFormat("¥${suiXinPinPrice}", 14, 20)
        val unitBuilder = SpannableStringBuilder("/${bean.productUnit}  ")
        unitBuilder.setSpan(
            AbsoluteSizeSpan(12, true),
            0,
            unitBuilder.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        priceBuilder.setSpan(
            StyleSpan(Typeface.BOLD),
            0,
            priceBuilder.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        priceBuilder.append(unitBuilder)

        //原价
        val fob = "¥${bean.fob}"

        if (!UiUtils.transform(bean.fob).equals(UiUtils.transform(suiXinPinPrice))) {
            val originalPriceBuilder = SpannableStringBuilder(fob)
            originalPriceBuilder.setSpan(
                AbsoluteSizeSpan(11, true),
                0,
                originalPriceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            originalPriceBuilder.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        mContext,
                        R.color.color_676773
                    )
                ), 0, originalPriceBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            originalPriceBuilder.setSpan(
                StrikethroughSpan(),
                0,
                originalPriceBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            if (suiXinPinPrice != fob) {
                priceBuilder.append(originalPriceBuilder)
            }
        }
        return priceBuilder
    }
}


interface AddCartCallback {
    fun addCart(goodsItem: SpellGroupGoodsItem, isAdd: Boolean, goodsAmount: Int)
    fun checkCollect(rowsBean: RowsBean, potion: Int, adapter: RecyclerView.Adapter<*>)
}