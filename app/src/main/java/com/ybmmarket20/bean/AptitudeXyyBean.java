package com.ybmmarket20.bean;

import com.ybmmarket20.view.ShopQualificationItem;

import java.util.List;

public class AptitudeXyyBean {

        private List<RowsBean> rows;

        public List<RowsBean> getRows() {
            return rows;
        }

        public boolean isSelf;

        public String shopCode;

        public Corporation corporation;

        public ShopNotice shopNotice;

        public List<ShopQualificationItem> corporationItemList;
        public List<ShopQualificationItem> shopNoticeItemList;

        public void setRows(List<RowsBean> rows) {
            this.rows = rows;
        }

        public static class RowsBean {
            /**
             * id : 87
             * links : ["http://ees.test.ybm100.com/electronicsignatures/file/download?no=f4e49611ef8448568fc73c7221f12f02&timestamp=1581681423856"]
             * name : 食品经营许可证
             */

            private String id;
            private String name;
            private List<String> links;
            public String qualificationName; //资质名称
            public String qualificationCode; //备案号

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public List<String> getLinks() {
                return links;
            }

            public void setLinks(List<String> links) {
                this.links = links;
            }
        }

        public static class Corporation {
            public String name;
            public String phone;
            public String businessScope;
        }

        public static class ShopNotice {
            public String expressType;
            public String expressRemarks;
            public String orderHandleTime;
            public String deliveryHandleTime;
            public String content;
        }
}
