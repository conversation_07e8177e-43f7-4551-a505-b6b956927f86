package com.ybmmarket20.bean;

import java.util.List;

public abstract class BaseSearchResultBean extends BaseFeedBean<RowsBean> {

    public int count;
    // 1 为默认，什么都没有触发
    // 2、3 为触发人工关键词映射
    // 4 为触发包含停止次
    public String keyWordHitType;

    public String keyWord;//原始词

    public String keyWordHitWord;//	命中词，即更改后的词

    // 319 等活动筛选条件
    public String activityScreenTagId;
    public String selectActivityScreenTagTitle;     // 选中活动筛选标题
    public String unSelectActivityScreenTagTitle;   // 未选中活动筛选标题

    public int type;

    public List<String> wordList;

    public String spType;
    public String spId;
    public String expId;
}
