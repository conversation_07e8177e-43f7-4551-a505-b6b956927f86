package com.ybmmarket20.bean;

import android.text.TextUtils;

public class PayConfigCard {
    public String bankLogo;
    public String bankShowName;
    public String cardId;
    public int isValidBankMobile;
    //很奇葩的字段，小心处理, 可用状态用"yes",其他状态用不可选，如果不返回默认是yes
    public String canUse = "yes";

    public int isSelected;


    /**
     * 是否可用
     * @return
     */
    public boolean isCanUse() {
        return TextUtils.equals(canUse, "yes");
    }

}
