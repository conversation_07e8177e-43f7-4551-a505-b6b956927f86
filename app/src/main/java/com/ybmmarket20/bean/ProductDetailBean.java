package com.ybmmarket20.bean;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.xyyreport.paramsInfo.GroupGoodsPlaceInfo;
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo;
import com.ybmmarket20.xyyreport.paramsInfo.OPRowsBeanInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品详情-实体类
 */
public class ProductDetailBean implements IRowsBeanInfo {

    public int id;
    public String showName;
    public String shopCode;
    public int categoryId;
    public String categoryFirstId;
    public String manufacturer;
    public String spec;//规格
    public int status;//商品状态  1-销售中，2-已售罄，3-特惠中(未用到)，4-下架，5-秒杀(未用到)，6-待上架(新增商品属于待上架)，7-已录入，8-待审核，9-审核未通过
    public double retailPrice;  // 对比价(原价)
    public String pricePrefix;   // 价格前缀； 如直降价，特价等
    public double fob;          // 当前价格
    public int availableQty;//库存
    public String cnProductCode; //医疗代码
    public int isEphedrine;//麻黄碱 1是 0非
    public String imageUrl;
    public String approvalNumber;//批准文号
    public String validity;//有效期
    public String description;
    public String descriptions;//title-查看更多的描述
    public String descriptionUrl;//title-查看更多的描述的url
    public List<ProductDetailImageBean> skuInstructionImageList;//商品说明书图片集合
    public PromotionDetailBean promotionDetail;
    public String markerUrl;//标签图片
    public List<PromiseListBean> promiseList;
    public List<PriceRangeListBean> skuPriceRangeList;//价格区间
    public String promotionTag;//限购
    public int isControl;//是否控销
    public boolean isPurchase;//是否可以购买
    public String drugClassificationImage;  //药品分类图标图片对应的url
    public String mediumPackage;//中包装
    public String suggestPrice;//建议零售价
    public String suggestPriceStr;//建议零售价 ka用展示
    public String pieceLoading;//件包装
    public String shelfLife;//保质期
    public String grossMargin;//毛利率
    public String uniformPrice;//控销价格
    public List<ImagesVideosListBean> imagesVideosList;//视频
    public int mediumPackageNum;//中包装数量 默认为1
    public int isSplit;//是否可拆零 0:不可拆零；1:可拆零 默认1
    public String isSplitTitle;//中包装是否可拆零提示文案
    public String medicalInsuranceCode; // 医保编码
    @Deprecated
    public String nearEffect;//近效期：nearEffect nearEffect
    @Deprecated
    public String farEffect;//远效期：farEffect
    public String effectStr;        //近远效期
    public String manufactureDate;      //生产日期
    public String seckillEndTimeStr;//秒杀倒计时时间
    public int priceType;//价格类型（1：fob;2:价格区间）
    public List<LabelIconBean> cxTagList;//促销标签
    public List<LabelIconBean> firstTagList;//医保等标签
    public List<LabelIconBean> tagList;//通用促销标签
    public int agent;//是否独家：0否，1是
    public boolean isShowVoucher;//是否显示领券数据组 true：是
    public String[] voucherTagList;//优惠券数组
    public String companyName;//第三方厂家名称
    public String orgId;//第三方厂家id
    public int isThirdCompany;//是否显示第三方厂家
    public int businessType;//收藏类型 1: 有货提醒收藏；2：降价收藏；
    public int favoriteFlag;//是否可以订阅 1.可订阅；0.不可订阅
    public int favoriteStatus;//收藏 1收藏 2未收藏
    public int signStatus;//协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
    public boolean isOEM;//true是OEM协议商品，为空或者false为非OEM协议商品(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断), true/false
    public int agreementEffective;//协议状态(0-未生效,1-生效中)
    public double reducePrice;//活动价
    public boolean gift;//
    public int isUsableMedicalStr;//是否医保：0否，1是
    public int listStatus;//是否加入清单  1:已加入清单 2:未加入清单
    public double prescriptionPrice;//医保价
    public int nearEffectiveFlag;//近效期标识(1:临期，2:近效)
    public ActivityTagBean activityTag;//活动标签
    public int oemType;//商品类型(1:普通商品，2:oem商品)
    public String producer;//产地
    //限时加补拼团品
    public LimitFullDiscountActInfo limitFullDiscountActInfo;


    //分享新增，透传即可
    //活动类型
    public  Long promoType;
    //活动ID
    public Long promoId;

    // 优先购需求新增
    public int leastPurchaseNum; // 最小起购数量，默认为 0
    public long distanceEndTime; // 距离结束时间（优先购）
    public int merchantStatus;   // 用户类型 1.智鹿(优先购)用户 2.其他用户

    public int showAgree; //是否符合协议标准展示价格,1:符合0:不符合

    // 是否控销协议商品（0不是控销协议商品，1是控销协议商品）
    public int isControlAgreement;

    public int isGive;//是否为赠送品 1，是  0，不是
    public int productType;//1普通商品，2秒杀商品，3X拼团商品，4赠品活动商品, 5批购包邮商品）

    public int licenseStatus;

    public String productUnit;//商品单位

    public int activityType;//提示语优化

    public String freightTips;//运费提示
    public String tracingCode;//追溯码

    public String sellingProposition1;//商品卖点信息1推荐语
    public String sellingProposition2;//商品卖点信息2推荐语
    public String sellingProposition3;//商品卖点信息3推荐语

    public String barcode;

    public int highGrossGroupBuying; //0:不是髙毛拼团，1:是髙毛拼团

    public ActPtBean actPtBean;

    public String controlTitle; //控销价格展示文案
    public String controlNotes; //控销注释
    public String controlPurchaseButton; //控销购买按钮
    public int controlType; //资质类型为1 5是控销商品
    public boolean isVirtualSupplier; //是否是虚拟供应商
    @SerializedName("pid")
    public String pId;//原商品id
    public String priceDesc;

    public String buyTip; //购买提示文案

    public Double jgPrice; //极光埋点的价格

    //极光埋点时，手动设置的跟着商品走  不是后台给的
    public JgTrackBean mJgTrackBean;

    @SerializedName("levelPriceDTO")
    public RangePriceBean rangePriceBean;//平销品阶梯价

    public TagsWrapperBean tags;
    //商品营销类型
    public String productActivityType;

    // 拼团商品可加车
    public boolean canAddToCart; // 加入购物车
    public ArrayList<NextDayServiceTagListBean> nextDayServiceTagList;

    public String qtListData;
    public String actPurchaseTip;
    //V12.0.06新增 中药重点属性、扩展属性。后台做了数据拼接，前端直接展示无需处理
    public List<ProductDetailKeyAttr> keyAttributeList; // 中药重点属性+扩展属性+生产日期
    public List<ProductDetailKeyAttr> skuExtAttrDTOS;   // 扩展属性


    public boolean isControlUnShowPrice() {
        return !TextUtils.isEmpty(controlTitle);
    }

    public String getProductId() {
        return id +"";
    }

    public boolean isActivityType() {
        return activityType == 99;
    }

    public boolean isNearEffectiveFlag() {
        return nearEffectiveFlag == 1 || nearEffectiveFlag == 2;
    }


    public String getProductUnit() {
        return productUnit;
    }

    public void setProductUnit(String productUnit) {
        this.productUnit = productUnit;
    }

    public boolean isShowGive() {
        return isGive == 1 || productType == 4;
    }

    public boolean isOemType() {
        return oemType == 2;
    }

    public boolean isReducePrice() {
        return reducePrice > 0;
    }

    public boolean isMarkerUrl() {
        return !TextUtils.isEmpty(markerUrl);
    }

    public boolean isFavoriteStatus() {
        return favoriteStatus == 1;
    }

    public boolean isListStatus() {
        return listStatus == 1;
    }

    public String[] getVoucherTagList() {
        return voucherTagList;
    }

    public List<LabelIconBean> getCxTagList() {
        return cxTagList;
    }

    public List<ImagesVideosListBean> getImagesVideosList() {
        return imagesVideosList;
    }

    public PromotionDetailBean getPromotionDetail() {
        return promotionDetail;
    }

    public List<PromiseListBean> getPromiseList() {
        return promiseList;
    }

    public List<ProductDetailImageBean> getSkuInstructionImageList() {
        return skuInstructionImageList;
    }

    public List<LabelIconBean> getFirstTagList() {
        return firstTagList;
    }

    public List<PriceRangeListBean> getSkuPriceRangeList() {
        return skuPriceRangeList;
    }

    /**
     * 获取建议零售价
     *
     * @return
     */
    public double getSuggestPrice() {
        double suggestPriceDouble = 0;
        if (!TextUtils.isEmpty(suggestPrice)) {
            try {
                suggestPriceDouble = Double.parseDouble(suggestPrice);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        return suggestPriceDouble;
    }

    /**
     * 获取控销价
     *
     * @return
     */
    public double getUniformPrice() {
        double uniformPriceDouble = 0;
        if (!TextUtils.isEmpty(uniformPrice)) {
            try {
                uniformPriceDouble = Double.parseDouble(uniformPrice);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        return uniformPriceDouble;
    }

    /**
     * 是否是控销品
     * @return
     */
    public boolean isControlGoods() {
        return !TextUtils.isEmpty(controlTitle) && controlType == 5;
    }

    public boolean isCanAddToCart() {
        return canAddToCart;
    }

    public void setCanAddToCart(boolean canAddToCart) {
        this.canAddToCart = canAddToCart;
    }

    @Override
    public long getSpmProductId() {
        return id;
    }

    @Nullable
    @Override
    public String getSpmProductName() {
        return showName;
    }

    @Nullable
    @Override
    public String getSpmQtSkuData() {
        return null;
    }

    @Nullable
    @Override
    public String getShopCode() {
        return shopCode;
    }

    @Override
    public boolean onOpSingleGoods() {
        return false;
    }

    @Nullable
    @Override
    public String getQtListData() {
        return null;
    }

    @Override
    public void setQtListData(@Nullable String qtListData) {
        //空实现
    }

    @Override
    public boolean onOpGoods() {
        return false;
    }

    @Nullable
    @Override
    public OPRowsBeanInfo getOPRowsBeanInfo() {
        return null;
    }

    @Nullable
    @Override
    public GroupGoodsPlaceInfo getGroupGoodsPlaceInfo() {
        return null;
    }

    @Override
    public boolean isNormalGoods() {
        return false;
    }

    @Override
    public boolean isSingleGroupPurchase() {
        return false;
    }

    @Override
    public boolean isMultipleGroupPurchase() {
        return false;
    }

    @Override
    public boolean isGroupPurchase() {
        return false;
    }

    @Override
    public int getCombinationSelectedStatus() {
        return 0;
    }

    @Nullable
    @Override
    public String getScmId() {
        return "";
    }

    @Override
    public boolean isMainFrequently() {
        return false;
    }

    @Nullable
    @Override
    public String getListExpId() {
        return "";
    }

    @Override
    public boolean isCart() {
        return false;
    }
}
