package com.ybmmarket20.bean

import com.ybmmarket20.bean.loadmore.IPage
import com.ybmmarket20.bean.loadmore.LoadMoreCommonData

/**
 * <AUTHOR>
 * @date 2020-05-09
 * @description
 */
class RefreshCrmRecommendBean<T>(
        val pageList: MutableList<T>,
        val licenseStatus: Int,
        val spId: String?,
        val sid: String?,
        val spType: String?
) : LoadMoreCommonData(), IPage<T> {
    override fun getCurPage(): Int = pageNo

    override fun getTotalPages(): Int = totalPage

    override fun getPageRowSize(): Int = pageSize

    override fun getRowsList(): MutableList<T> = pageList
}