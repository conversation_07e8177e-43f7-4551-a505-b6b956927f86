package com.ybmmarket20.bean

import com.google.gson.annotations.SerializedName

data class ShopBasicInfoBean(
    var addr: String? = null, //店铺联系人地址
    var brief: String? = null, //商家简介
    var corporat: String? = null,//店铺联系人
    var email: String? = null,//邮箱
    var orgLogo: String? = null,//商家logo
    var orgName: String? = null,//商家店铺名称
    var phone: String? = null,//联系人手机号
    var saleSkuNum: String? = null, //商品总销量
    var upSkuNum: String? = null,//已上架商品数
    var webUrl: String? = null,//店铺首页地址
    var freightInfo: String? = null,//店铺运费信息
    var shareLink: String? = null,//分享链接
    var shareDesc: String? = null,//分享文案

    var salesVolumeDesc: String? = null,//发货xxx件
    var salesVolume: String? = null, //发货xxx件
    var shelvesDesc: String? = null,//上架xxx种
    var shelves: String? = null,//分享文案


    var shopPatternCode: String? = null, // 店铺种类
    var shopPropTags: List<TagBean>?,
    var shopServiceQualityTags: List<TagBean>?,
    var shopCode: String?,

    var deliveryAddress: String? // 发货地址

    /*INDUSTRY("industry", "工业"),

    POP("pop", "第三方"),

    YBM("ybm", "药帮忙"),

    YKQ("ykq", "宜块钱"),

    PERSONAL("personal", "自然人"),

    VIRTUAL("virtual", "虚拟供应商"),*/



) {
    @SerializedName("relateOrgs")
    val shopTabList: List<ShopTab>? = null
}
