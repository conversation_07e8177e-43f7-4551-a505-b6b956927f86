package com.ybmmarket20.bean

import com.google.gson.annotations.SerializedName

class SuggestListBean {
    var suggestList: MutableList<String>? = null
    var csuInfo: RowsBean? = null
    var suggestShopList: MutableList<SuggestShopBean>? = null
}

class SuggestListBeanV2 {
    var suggestList: MutableList<SuggestListItem>? = null
    var csuInfo: RowsBean? = null
    var suggestShopList: MutableList<SuggestShopBean>? = null
}

data class SuggestListItem(
    val suggestion: String?,
    val leftLabels: MutableList<SuggestListItemLabel>,
    val rightLabels: MutableList<SuggestListItemLabel>,
    var searchKeyword: String?
)

data class SuggestListItemSelected(
    val suggestion: String?,
    val leftLabel: SuggestListItemLabel?,
    val rightLabel: SuggestListItemLabel?
)

data class SuggestListItemLabel(
    val name: String?,
    val searchConditions: MutableList<SuggestSearchConditions>?
)

data class SuggestSearchConditions(
    val searchField: String,
    val searchValue: String
)

data class SuggestShopBean(
        var shopName: String,
        var skipUrl: String?,
        var shopCode: String,
        @SerializedName("shopPropTags") var sugShopTags: MutableList<TagBean>?,//sug店铺标签
        var shopServiceQualityTags: MutableList<TagBean>?, //店铺服务质量标签
        var searchKeyword: String?
        //sug店铺描述
//    var sugShopDes: String?
) {
    fun getSugShopDes(): String? {
        return shopServiceQualityTags?.joinToString(separator = " | ") {
            it.text
        }
    }
}