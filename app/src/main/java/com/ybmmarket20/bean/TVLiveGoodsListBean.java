package com.ybmmarket20.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class TVLiveGoodsListBean {

    /**
     * pageNo : 1
     * totalPage : 1
     * pageSize : 10
     * totalCount : 3
     * products : [{"agreeSigneStatus":0,"availableQty":3140,"barcode":"Y010400191","branchCode":"XS420000","code":"6930129000114","commonName":"新亚 林可霉素利多卡因凝胶(绿药膏)","farEffect":"2021-07-23","fob":0,"id":1,"imageUrl":"6930129000114.jpg","isControl":1,"isOEM":"false","isSplit":1,"limitedQty":100,"manufacturer":"上海新亚药业闵行有限公司","mediumPackage":"50盒","mediumPackageNum":50,"mediumPackageTitle":"中包装:50盒","nearEffect":"2020-11-18","nearEffectiveFlag":1,"productUnit":"盒","purchaseType":0,"showAgree":1,"showName":"新亚 林可霉素利多卡因凝胶(绿药膏)","signStatus":0,"spec":"15G*1支","status":1},{"agreeSigneStatus":0,"availableQty":9999999,"barcode":"Y010100050","branchCode":"XS420000","code":"6900233882357","commonName":"卡博平 阿卡波糖片","farEffect":"2021-09-30","fob":44.97,"id":1127,"imageUrl":"6900233882357.jpg","isControl":0,"isOEM":"false","isSplit":1,"limitedQty":100,"manufacturer":"杭州中美华东制药有限公司","mediumPackage":"10盒","mediumPackageNum":10,"mediumPackageTitle":"中包装:10盒","nearEffect":"2021-09-30","nearEffectiveFlag":0,"productUnit":"盒","purchaseType":0,"showAgree":1,"showName":"卡博平 阿卡波糖片","signStatus":0,"spec":"50mg*15片*2板","status":1},{"agreeSigneStatus":0,"availableQty":9999992,"barcode":"ALW01100003","branchCode":"XS420000","code":"6909221669956","commonName":"仲景 六味地黄丸【最低零售价15.00元】","farEffect":"2022-11-30","fob":11,"id":10193,"imageUrl":"6909221669956.JPG?random=0.606671561509857","isControl":0,"isOEM":"false","isSplit":1,"limitedQty":500,"manufacturer":"仲景宛西制药股份有限公司(原河南省宛西制药股份有限公司)","mediumPackage":"5盒","mediumPackageNum":5,"mediumPackageTitle":"中包装:5盒","nearEffect":"2022-11-30","nearEffectiveFlag":0,"productUnit":"盒","purchaseType":0,"showAgree":1,"showName":"仲景 六味地黄丸【最低零售价15.00元】","signStatus":0,"spec":"120丸（浓缩丸）","status":1}]
     */

    public int pageNo;
    public int totalPage;
    public int pageSize;
    public int totalCount;
    public List<ProductsBean> products;
    public int licenseStatus;

    public static class ProductsBean {
        /**
         * agreeSigneStatus : 0
         * availableQty : 3140
         * barcode : Y010400191
         * branchCode : XS420000
         * code : 6930129000114
         * commonName : 新亚 林可霉素利多卡因凝胶(绿药膏)
         * farEffect : 2021-07-23
         * fob : 0.0
         * id : 1
         * imageUrl : 6930129000114.jpg
         * isControl : 1
         * isOEM : false
         * isSplit : 1
         * limitedQty : 100
         * manufacturer : 上海新亚药业闵行有限公司
         * mediumPackage : 50盒
         * mediumPackageNum : 50
         * mediumPackageTitle : 中包装:50盒
         * nearEffect : 2020-11-18
         * nearEffectiveFlag : 1
         * productUnit : 盒
         * purchaseType : 0
         * showAgree : 1
         * showName : 新亚 林可霉素利多卡因凝胶(绿药膏)
         * signStatus : 0
         * spec : 15G*1支
         * status : 1
         */
        public String validity;// 有效期至（如2018-08-08）
        public int agreeSigneStatus;
        public int availableQty;
        public String barcode;
        public String branchCode;
        public String code;
        public String commonName;
        public String farEffect;
        public double fob;
        public String id;
        public String imageUrl;
        public int isControl;       //是否控销
        public boolean isPurchase;  //是否可以购买
        public boolean isOEM; // //true是OEM协议商品，为空或者false为非OEM协议商品(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断), true/false
        public int signStatus;  //协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
        public int isSplit;
        public int limitedQty;
        public String manufacturer;
        public String mediumPackage;
        public int mediumPackageNum;
        public String mediumPackageTitle;
        public String nearEffect;
        public int nearEffectiveFlag;
        public String productUnit;
        public int purchaseType;
        public int showAgree;
        public String showName;
        public String spec;
        public int status;
        public int showtime;//展示时长，单位秒
        public boolean isHaveVoucherTemplate;  // 商品是否需要领取优惠券 isHaveVoucherTemplate
        public String voucherTemplateIds;      // 商品关联的优惠券模板id(多个优惠券模板用英文逗号分隔)
        public String voucherReduceAmount;     // 券后减金额
    }
}
