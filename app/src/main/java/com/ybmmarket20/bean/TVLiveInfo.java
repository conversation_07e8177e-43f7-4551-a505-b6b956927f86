package com.ybmmarket20.bean;

import java.util.List;

public class TVLiveInfo {

    /**
     * liveInfo : {"audienceCount":0,"audiences":[],"custom":"{}","liveStatus":1,"mixedPlayURL":"http://prod-pull.ybm100.com/live/ectest111.flv","pushers":[{"userAvatar":"","userID":"ectest111","userName":"ectest111"}],"roomCreator":"ectest111","roomID":"room_ectest111","roomInfo":"{\"title\":\"第一次\"}"}
     */

    private LiveInfoBean liveInfo;

    public LiveInfoBean getLiveInfo() {
        return liveInfo;
    }

    public void setLiveInfo(LiveInfoBean liveInfo) {
        this.liveInfo = liveInfo;
    }

    public static class LiveInfoBean {
        /**
         * audienceCount : 0
         * audiences : []
         * custom : {}
         * liveStatus : 1
         * mixedPlayURL : http://prod-pull.ybm100.com/live/ectest111.flv
         * pushers : [{"userAvatar":"","userID":"ectest111","userName":"ectest111"}]
         * roomCreator : ectest111
         * roomID : room_ectest111
         * roomInfo : {"title":"第一次"}
         */

        private int audienceCount;
        private String custom;
        private int liveStatus;//1.直播中2.点播回访
        private String mixedPlayURL;
        private String roomCreator;
        private String roomID;
        private String roomInfo;
        private List<?> audiences;
        private List<PushersBean> pushers;
        private String roomName;

        public String getRoomName() {
            return roomName;
        }

        public void setRoomName(String roomName) {
            this.roomName = roomName;
        }

        public int getAudienceCount() {
            return audienceCount;
        }

        public void setAudienceCount(int audienceCount) {
            this.audienceCount = audienceCount;
        }

        public String getCustom() {
            return custom;
        }

        public void setCustom(String custom) {
            this.custom = custom;
        }

        public int getLiveStatus() {
            return liveStatus;
        }

        public void setLiveStatus(int liveStatus) {
            this.liveStatus = liveStatus;
        }

        public String getMixedPlayURL() {
            return mixedPlayURL;
        }

        public void setMixedPlayURL(String mixedPlayURL) {
            this.mixedPlayURL = mixedPlayURL;
        }

        public String getRoomCreator() {
            return roomCreator;
        }

        public void setRoomCreator(String roomCreator) {
            this.roomCreator = roomCreator;
        }

        public String getRoomID() {
            return roomID;
        }

        public void setRoomID(String roomID) {
            this.roomID = roomID;
        }

        public String getRoomInfo() {
            return roomInfo;
        }

        public void setRoomInfo(String roomInfo) {
            this.roomInfo = roomInfo;
        }

        public List<?> getAudiences() {
            return audiences;
        }

        public void setAudiences(List<?> audiences) {
            this.audiences = audiences;
        }

        public List<PushersBean> getPushers() {
            return pushers;
        }

        public void setPushers(List<PushersBean> pushers) {
            this.pushers = pushers;
        }

        public static class PushersBean {
            /**
             * userAvatar :
             * userID : ectest111
             * userName : ectest111
             */

            private String userAvatar;
            private String userID;
            private String userName;

            public String getUserAvatar() {
                return userAvatar;
            }

            public void setUserAvatar(String userAvatar) {
                this.userAvatar = userAvatar;
            }

            public String getUserID() {
                return userID;
            }

            public void setUserID(String userID) {
                this.userID = userID;
            }

            public String getUserName() {
                return userName;
            }

            public void setUserName(String userName) {
                this.userName = userName;
            }
        }
    }
}
