package com.ybmmarket20.bean

import java.io.Serializable

/*
 * Created by sean on 2019-07-26
 */

data class TradingSnapshotListBean(
    val detailList: List<Detail>?,
    val num: Int,
    val orderNo: String,
    val title: String? = "",
    val contentOne: String? = "",
    val contentTwo: String? = "",
    val showStatement: String? = "",
    val packageList: List<Packages>?
) : Serializable

data class Packages(
    val packageCount: Int,
    val packageId: Int,
    val packageDetailList: List<Detail>,
    val subtotalPrice: Double,
    val totalPrice: Double
) : Serializable

data class Detail(
    val packageCount: Int,
    val packageId: Int,
    val preferentialAmount: Double,
    val preferentialCount: Int,
    val balanceFlag: Int,
    val balancePercent: Double,
    val branchCode: String,
    val id: Int,
    val imageUrl: String,
    val orderNo: String,
    val productActivityTag: ProductActivityTag,
    val productAmount: Int,
    val productName: String,
    val productPrice: Double,
    val skuId: Int,
    val spec: String,
    val subTotal: Double,
    val tagJsonStr: String,
    val tagList: List<Tag>,
    var channelCode: String? = null,//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    var isThirdCompany: Int = 0,//是否是自营（0：是；1：否）
    val nearEffect: String? = "",//近效期
    val farEffect: String? = ""//远效期

) : Serializable

data class ProductActivityTag(
    val skuTagNotes: List<Any>,
    val tagNoteBackGroupUrl: String,
    val tagUrl: String
) : Serializable

data class Tag(
    val name: String,
    val uiType: Int,
    val value: Int
) : Serializable