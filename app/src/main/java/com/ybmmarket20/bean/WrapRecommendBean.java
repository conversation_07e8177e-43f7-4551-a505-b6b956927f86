package com.ybmmarket20.bean;


import com.ybm.app.bean.AbstractMutiItemEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @file WrapRecommendBean.java
 * @brief
 * @date 2019/4/7
 * Copyright (c) 2019, 北京小药药
 * All rights reserved.
 */
public class WrapRecommendBean extends AbstractMutiItemEntity {


    public static final int CONTENT_NORMAL = 0; //内容样式1-正常条目布局
    public static final int CONTENT_COMBO = 1;  //内容样式2-品牌布局

    @Override
    public int getItemType() {
        if(itemType <= 0 ){
            itemType =  CONTENT_NORMAL;
        }
        return super.getItemType();
    }
    public boolean brand;

    // 品牌取的值
    public BrandItem brandBean;
    public RowsBean rowsBean;

    public BrandItem getBrandBean() {
        return brandBean;
    }

    public void setBrandBean(BrandItem brandBean) {
        this.brandBean = brandBean;
    }

    public RowsBean getRowsBean() {
        return rowsBean;
    }

    public void setRowsBean(RowsBean rowsBean) {
        this.rowsBean = rowsBean;
    }

    public boolean isBrand() {
        return brand;
    }

    public void setBrand(boolean brand) {
        this.brand = brand;
    }
}
