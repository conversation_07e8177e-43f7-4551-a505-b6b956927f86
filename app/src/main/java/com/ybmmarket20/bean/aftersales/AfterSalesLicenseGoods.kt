package com.ybmmarket20.bean.aftersales

import com.ybmmarket20.bean.CompanyLicenseBean


data class AfterSalesLicense(
    var licenseGoods: MutableList<AfterSalesLicenseGoods> = mutableListOf(),
    var licenseCompany: CompanyLicenseBean,
    var afterSalesTips: AfterSalesTips,
    var afterSalesUploadImage: AfterSalesUploadImage,
    var searchKeyWord: String
)

data class AfterSalesLicenseGoods(
    val skuId: String?,
    val goodsName: String?,
    val spec: String?,
    val goodsImageUrl: String?,
    var checkReportState: Boolean,
    var licenseState: Boolean
)

data class AfterSalesLicenseGoodsTemp(
    val imageUrl: String?,
    val varietyNum: Int,
    val productName: String?,
    val imaproductPricegeUrl: String?,
    val skuId: String?,
    val spec: String?,
) {
    fun mappingData(): AfterSalesLicenseGoods {
        return AfterSalesLicenseGoods(
            skuId,
            productName,
            spec,
            imageUrl,
            checkReportState = false,
            licenseState = false
        )
    }
}