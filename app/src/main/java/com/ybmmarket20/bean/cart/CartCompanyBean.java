package com.ybmmarket20.bean.cart;

import java.util.List;

/*
 * 公司维度
 * */
public class CartCompanyBean {

    private String companyCode;//公司编码
    private String companyName;//公司名称
    private String shopJumpUrl;//跳转店铺路由
    private int companyType;//公司类型
    private List<CartShopList> shop;//店铺对象
    private int valid;//0=失效商品，1=正常商品
    private String orgId;           //自营或非自营勾选的orgId
    private String mainShopCode;    //自营或非自营勾选的shopCode
    private int selectStatus;// 非自营状态:0-未选中，1-选中
    private int isThirdCompany;
    private String freightTips;//已包邮|还差xx元包邮
    private int freightTipsShowStatus;//是否展示运费提示语1：展示; 0：不展示
    private String freightUrlText; //去凑单连接文字："去凑单"
    private String freightJumpUrl; //为去凑单跳转url
    public String activityId;      //满返活动id
    public String activityType;    //满返活动类型
    private int freightIconShowStatus;//是否展示运费！icon 1展示 0不展示

    public String payAmount;            //合计，number类型
    public int productVarietyNum;    //已选商品品种数，number类型
    public int productTotalNum;      //已选商品的总数量 ，number类型

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getShopJumpUrl() {
        return shopJumpUrl;
    }

    public void setShopJumpUrl(String shopJumpUrl) {
        this.shopJumpUrl = shopJumpUrl;
    }

    public int getCompanyType() {
        return companyType;
    }

    public void setCompanyType(int companyType) {
        this.companyType = companyType;
    }

    public List<CartShopList> getShop() {
        return shop;
    }

    public void setShop(List<CartShopList> shop) {
        this.shop = shop;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getMainShopCode() {
        return mainShopCode;
    }

    public void setMainShopCode(String mainShopCode) {
        this.mainShopCode = mainShopCode;
    }

    public int getSelectStatus() {
        return selectStatus;
    }

    public void setSelectStatus(int selectStatus) {
        this.selectStatus = selectStatus;
    }

    public int getIsThirdCompany() {
        return isThirdCompany;
    }

    public void setIsThirdCompany(int isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public boolean isThirdCompany() {
        return isThirdCompany == 0;
    }

    public boolean isSelfCompany() {
        return isThirdCompany == 0;
    }

    public String getFreightTips() {
        return freightTips;
    }

    public void setFreightTips(String freightTips) {
        this.freightTips = freightTips;
    }

    public int getFreightTipsShowStatus() {
        return freightTipsShowStatus;
    }

    public void setFreightTipsShowStatus(int freightTipsShowStatus) {
        this.freightTipsShowStatus = freightTipsShowStatus;
    }

    public String getFreightUrlText() {
        return freightUrlText;
    }

    public void setFreightUrlText(String freightUrlText) {
        this.freightUrlText = freightUrlText;
    }

    public String getFreightJumpUrl() {
        return freightJumpUrl;
    }

    public void setFreightJumpUrl(String freightJumpUrl) {
        this.freightJumpUrl = freightJumpUrl;
    }

    public int getFreightIconShowStatus() {
        return freightIconShowStatus;
    }

    public void setFreightIconShowStatus(int freightIconShowStatus) {
        this.freightIconShowStatus = freightIconShowStatus;
    }
}
