package com.ybmmarket20.bean.cms;

import androidx.annotation.Nullable;

import java.util.List;
import java.util.PriorityQueue;

public class ModuleContent<T> {

    public String image;//图片url
    public String color;//颜色
    public String bgRes;//背景色
    public String action;//点击事件
    public String page_url;//
    public String floorSpaceHeight;//楼层间隔高度
    public String floorSpacingBgRes;//楼层间隔背景色
    public String goodsimage;//
    public String swiperDotRes;//轮播图点的颜色
    public String rotationpointcolor;//banner轮播图点的颜色
    public String goodsBgRes;


    // activeKey  0 首页 1 活动专区 2 爆款推荐 3 常购清单
    public int activeKey;

    // 集合推荐 一页显示商品个数
    // 快捷入口 显示行数
    public String count;


    // 横向商品展示
    public String titleImage;//头部图片
    public String recTitle;
    public String recSubTitle;
    public String rectitleColor;
    public String recSubTitleColor;

    // 为搜索框用的
    public String color1;                   // 搜索栏渐变色、精彩活动渐变色的 左部颜色
    public String color2;                   //  搜索栏渐变色、精彩活动渐变色的 右部颜色
    public String default_icon_color;       // 默认扫描、信息图标颜色
    public String active_icon_color;        // 上拉后，两图标进行变色
    public String search_text;              //  大家常常搜的

    // 下面四张背景图只要有一个不是图片，取轮播图第一张色值设为背景，如果没有轮播，则设置默认值
    public String top_bgRes;        //  头部背景图
    public String hotWord_bgRes;    //  热词背景图
    public String meddle_bgRes;     //  中间背景图
    public String bottom_bgRes;     //  底部区域背景图
    public String refresh_bgRes;    //  下拉刷新区域的背景色

    // 轮播图和快捷入口用的
    public Style style;
    // public String rest_bgRes;


    // 药头条左侧图标
    public String icon;
    public String inside_bgRes;

    // hometab 用的
    public TabStyle tabs;

    // 更多活动用
    public String title;
    public String textColor;

    // 精彩活动用
    public String title1;
    public String title2;

    // 横向商品列表指示器选中色值
    // public String color;

    // 竖向商品列表用
    public String code;

    // 底部导航栏用
    // public String color;  //  文字默认颜色
    public String hoverColor;//  选中文字颜色
    public String bgColor;   //  底部背景颜色

    public List<T> list;        //  模块条目数据
    public List<HotSearch> listHostSearch;  //  搜索模块中的热词
    public SeckillLayoutBean data;//秒掉api

    public class HotSearch {
        private String keyword;
        private String androidUrl;

        public String getAndroidUrl(){
            if (androidUrl == null) {
                return "";
            }
            return androidUrl;
        }

        public String getKeyword() {
            if (keyword == null) {
                return "";
            }
            return keyword;
        }
    }

    public class TabStyle {
        String spread_color;
        String color;
        String default_font;    // 默认字体颜色
        String active_font;     // 选中字体颜色
        String tab_bg;          // 选项卡背景色
        String active_line;     // 选中tab条颜色


        public String getDefault_font() {
            return default_font;
        }

        public String getActive_font() {
            return active_font;
        }

        public String getTab_bg() {
            return tab_bg;
        }

        public String getActive_line() {
            return active_line;
        }
    }

    public class Style {
        // 默认透明度
        public String opacity1;
        // 激活透明度
        public String opacity2;
        // 默认指示点颜色
        public String color1;
        // 显示位置指示点颜色
        public String color2;
        // 指示器图标
        public String shape;
        // 轮播间隔，单位ms
        public String time;
        // 左边右边还是中间
        public String type;

//              "opacity2": 100,
//            "color1": "#A526AE",
//            "color2": "#18DC50",
//            "opacity1": 100,
//            "shape": "circle",
//            "time": 3000,
//            "type": "center"


        // 获取默认透明度
        public int getOpacity1() {
            int op1 = 100;
            try {
                op1 = Integer.parseInt(opacity1);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
            return op1;
        }

        // 获取激活透明度
        public int getOpacity2() {
            int op2 = 100;
            try {
                op2 = Integer.parseInt(opacity2);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
            return op2;
        }
    }
}
