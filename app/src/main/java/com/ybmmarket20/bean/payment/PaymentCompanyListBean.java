package com.ybmmarket20.bean.payment;


import android.text.TextUtils;

import com.ybmmarket20.bean.PaymentSuiXinPinSkusBean;
import com.ybmmarket20.bean.cart.CartBean;

import java.util.List;

public class PaymentCompanyListBean {

    private List<PaymentCompanyBean> companys;
    private boolean useBalance;//是否使用余额
    private String selectVoucherIds;//选中优惠券列表
    private String skus;//商品id:小计,商品id:小计,
    private String xyyMoneyForVoucherCheck;//用来校验自营叠加券的自营店铺金额
    private double somethingSubTotal;
    public PaymentSuiXinPins suiXinPinSkus;

    private int bizSource;

    public double getSomethingSubTotal() {
        return somethingSubTotal;
    }

    public void setSomethingSubTotal(double somethingSubTotal) {
        this.somethingSubTotal = somethingSubTotal;
    }
    private List<CartBean.NeedToBePerfectedActBean> needToBePerfectedActList;


    public List<PaymentCompanyBean> getCompanys() {
        return companys;
    }

    public void setCompanys(List<PaymentCompanyBean> companys) {
        this.companys = companys;
    }

    public boolean isUseBalance() {
        return useBalance;
    }

    public void setUseBalance(boolean useBalance) {
        this.useBalance = useBalance;
    }

    public String getSelectVoucherIds() {
        return TextUtils.isEmpty(selectVoucherIds)?"":selectVoucherIds;
    }

    public void setSelectVoucherIds(String selectVoucherIds) {
        this.selectVoucherIds = selectVoucherIds;
    }

    public String getSkus() {
        return skus;
    }

    public void setSkus(String skus) {
        this.skus = skus;
    }

    public String getXyyMoneyForVoucherCheck() {
        return xyyMoneyForVoucherCheck;
    }

    public void setXyyMoneyForVoucherCheck(String xyyMoneyForVoucherCheck) {
        this.xyyMoneyForVoucherCheck = xyyMoneyForVoucherCheck;
    }

    public List<CartBean.NeedToBePerfectedActBean> getNeedToBePerfectedActList() {
        return needToBePerfectedActList;
    }

    public void setNeedToBePerfectedActList(List<CartBean.NeedToBePerfectedActBean> needToBePerfectedActList) {
        this.needToBePerfectedActList = needToBePerfectedActList;
    }

    public int getBizSource() {
        return bizSource;
    }

    public void setBizSource(int bizSource) {
        this.bizSource = bizSource;
    }
}
