package com.ybmmarket20.bean.payment;

import com.ybmmarket20.bean.ExpandableItem;
import com.ybmmarket20.bean.NextDayDeliveryDtoBean;
import com.ybmmarket20.bean.NextDayServiceTagListBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.bean.VoucherListBean;

import java.util.ArrayList;
import java.util.List;


public class PaymentItemBean extends ExpandableItem {

    private static final long serialVersionUID = 8140878097781003835L;

    private int isThirdCompany;//是否是自营（0：是；1：否）
    // companyCode
    private String companyCode;
    // 公司名称
    private String companyName;
    // 公司类型，1：大自营，2：POP
    private int companyType;
    // 店铺名称
    private String shopName;
    // 店铺类型
    private String shopCode;
    // 店铺类型
    private int shopType;

    //店铺业务模式编码
    private String shopPatternCode;

    // 默认展示的商品数据
    private List<PaymentImageBean> defaultShowProducts;

    // 优惠券展示文案
    private String voucherTip;
    // 优惠券集合
    private List<VoucherListBean> vouchers;

    // 商品种类
    private int productVarietyNum;
    // 商品件数
    private int productCount;
    // 商品总金额
    private double totalAmount;
    // 活动优惠总额
    private double promoTotalAmt;
    private double discountAmount;
    // 优惠券优惠总额
    private double voucherTotalAmt;
    // 运费总额
    private double freightTotalAmt;
    // 余额抵扣总额
    private double availBalanceAmt;
    // 实付金额
    private double payAmount;
    //赠品总金额
    private double giftTotalAmount;

    private String selectVoucherIds;//ybm自营和店铺统一使用这个字段来遍历已选择的最优优惠券id，下面的两个字段可以废弃了
    private List<VoucherListBean> selectDjVoucherList;//选择的叠加优惠券拼接字符串
    private List<VoucherListBean> selectNormalVoucherList;//选择的一般优惠券拼接字符串

    /*------------自定义--------------*/
    private boolean isLast = false;//是否最后一个

    private boolean isShopCount = false;//是否只有一个item

    private double allPayAmount;//总的合计

    private List<RefundProductListBean> detailList;
    public List<RefundProductListBean> detailListOrigin;

    private String remarks;

    private int type;//(1, "满减"),(2, "满折"),(3, "满赠"),(4, "满减赠"),9-不参与活动，10-套餐，5:大礼包 **/
    private int id;//大礼包id
    private boolean isSelectStatus;//默认选中的大礼包

    public String freeFreightDiffTips;//已包邮  实付金额满300元包邮，还需凑260.00元
    public int freightTipsShowStatus; //控制是否展示运费提示语，0: 不展示；1：展示
    public int freightIconShowStatus;//是否展示运费！icon 1展示 0不展示
    public String freightUrlText;//去凑单
    public boolean isFbpShop;//是否是fbp店铺

    public String licenseStr;//随货同行资质

    private PaymentShoppingGroupBean group;
    public NextDayDeliveryDtoBean nextDayDeliveryDto;

    public PaymentShoppingGroupBean getGroup() {
        return group;
    }

    public void setGroup(PaymentShoppingGroupBean group) {
        this.group = group;
    }

    public boolean IsNotThirdCompany() {
        return isThirdCompany == 0;
    }

    public int getIsThirdCompany() {
        return isThirdCompany;
    }

    public boolean isThirdCompany() {
        return isThirdCompany == 1;
    }

    public void setIsThirdCompany(int isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public int getCompanyType() {
        return companyType;
    }

    public void setCompanyType(int companyType) {
        this.companyType = companyType;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public int getShopType() {
        return shopType;
    }

    public void setShopType(int shopType) {
        this.shopType = shopType;
    }

    public List<PaymentImageBean> getDefaultShowProducts() {
        return defaultShowProducts;
    }

    public void setDefaultShowProducts(List<PaymentImageBean> defaultShowProducts) {
        this.defaultShowProducts = defaultShowProducts;
    }

    public String getVoucherTip() {
        return voucherTip;
    }

    public void setVoucherTip(String voucherTip) {
        this.voucherTip = voucherTip;
    }

    public List<VoucherListBean> getVouchers() {
        return vouchers;
    }

    public void setVouchers(List<VoucherListBean> vouchers) {
        this.vouchers = vouchers;
    }

    public int getProductVarietyNum() {
        return productVarietyNum;
    }

    public void setProductVarietyNum(int productVarietyNum) {
        this.productVarietyNum = productVarietyNum;
    }

    public int getProductCount() {
        return productCount;
    }

    public void setProductCount(int productCount) {
        this.productCount = productCount;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getPromoTotalAmt() {
        return promoTotalAmt;
    }

    public void setPromoTotalAmt(double promoTotalAmt) {
        this.promoTotalAmt = promoTotalAmt;
    }

    public double getVoucherTotalAmt() {
        return voucherTotalAmt;
    }

    public void setVoucherTotalAmt(double voucherTotalAmt) {
        this.voucherTotalAmt = voucherTotalAmt;
    }

    public double getFreightTotalAmt() {
        return freightTotalAmt;
    }

    public void setFreightTotalAmt(double freightTotalAmt) {
        this.freightTotalAmt = freightTotalAmt;
    }

    public double getAvailBalanceAmt() {
        return availBalanceAmt;
    }

    public void setAvailBalanceAmt(double availBalanceAmt) {
        this.availBalanceAmt = availBalanceAmt;
    }

    public double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(double payAmount) {
        this.payAmount = payAmount;
    }

    public boolean isLast() {
        return isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }

    public boolean isShopCount() {
        return isShopCount;
    }

    public void setShopCount(boolean shopCount) {
        isShopCount = shopCount;
    }

    public void setDetailListOrigin() {
        detailListOrigin = new ArrayList<>();
        if (detailList == null) return;
        detailListOrigin.addAll(getDetailList());
    }


    public List<RefundProductListBean> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<RefundProductListBean> detailList) {
        this.detailList = detailList;
    }

    public String getShopPatternCode() {
        return shopPatternCode;
    }

    public void setShopPatternCode(String shopPatternCode) {
        this.shopPatternCode = shopPatternCode;
    }

    public double getGiftTotalAmount() {
        return giftTotalAmount;
    }

    public void setGiftTotalAmount(double giftTotalAmount) {
        this.giftTotalAmount = giftTotalAmount;
    }

    public double getAllPayAmount() {
        return allPayAmount;
    }

    public void setAllPayAmount(double allPayAmount) {
        this.allPayAmount = allPayAmount;
    }

    public List<VoucherListBean> getSelectDjVoucherList() {
        return selectDjVoucherList;
    }

    public void setSelectDjVoucherList(List<VoucherListBean> selectDjVoucherList) {
        this.selectDjVoucherList = selectDjVoucherList;
    }

    public List<VoucherListBean> getSelectNormalVoucherList() {
        return selectNormalVoucherList;
    }

    public void setSelectNormalVoucherList(List<VoucherListBean> selectNormalVoucherList) {
        this.selectNormalVoucherList = selectNormalVoucherList;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSelectVoucherIds() {
        return selectVoucherIds;
    }

    public void setSelectVoucherIds(String selectVoucherIds) {
        this.selectVoucherIds = selectVoucherIds;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isSelectStatus() {
        return isSelectStatus;
    }

    public void setSelectStatus(boolean selectStatus) {
        isSelectStatus = selectStatus;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }
}
