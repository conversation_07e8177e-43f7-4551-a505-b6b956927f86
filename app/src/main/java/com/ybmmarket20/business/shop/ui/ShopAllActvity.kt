package com.ybmmarket20.business.shop.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.SparseArray
import android.view.View
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.activity.BaseProductActivity
import com.ybmmarket20.bean.ShopTab
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.ShopHomeViewModel
import kotlinx.android.synthetic.main.activity_shop_all.common_search_view
import kotlinx.android.synthetic.main.activity_shop_all.tabDivider
import kotlinx.android.synthetic.main.activity_shop_all.tabLayout

/**
 *  shopactivity    pop店铺路由
 *  searchresult    自营店铺路由
 */
const val VIRTUAL = "virtual"
//进入页面默认打开-首页
const val ENTRY_SHOP_ACTION_HOME = "0"
//进入页面默认打开-商品
const val ENTRY_SHOP_ACTION_PRODUCT = "1"

@Router("shopactivity", "searchresult")
class ShopAllActvity : BaseProductActivity() {

    var shopPatternCode: String? = null

    private var mShopCode: String? = null
    private var mOrgId: String? = null
    private var ordIdShopCode: String? = null
    private var mEntrance = ""
    //缓存Fragment
    private val mShopFragment = SparseArray<Fragment?>()

    private var currFragment: Fragment? = null
    private var mTabList: List<ShopTab>? = null
    private val mViewModel: ShopHomeViewModel by viewModels()
    //是否是控销店铺
    private var isFromControlShop: Boolean = false
    private var source:String? = "0" //1表示来自购物车  后端下发路由跳过来
    private var isFilterUnableAddCart:String? = "0" //1表示来自购物车  后端下发路由跳过来

    companion object{

        fun jgBtnClickTrack(mContext: Context, module:String, btnName:String){
            val mParams = java.util.HashMap<String, String>()
            mParams[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_REFERRER] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_REFERRER_TITLE] = JGTrackManager.TrackShopMain.TITLE
            mParams[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShopMain.PAGE_ID
            mParams[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShopMain.TITLE
            mParams[JGTrackManager.FIELD.FIELD_MODULE] = module
            mParams[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName

            JGTrackManager.eventTrack(
                    mContext,
                    JGTrackManager.TrackMineSupplier.EVENT_BTN_CLICK,
                    mParams)
        }
    }

    override fun getContentViewId(): Int = R.layout.activity_shop_all

    override fun cartClick() {
        super.cartClick()
        jgBtnClickTrack(this,"顶部导航","购物车")
    }

    override fun getRawAction(): String {
        val stringBuilder = StringBuilder()
        val orgId = if (isFromControlShop) {
            val selectedPosition = tabLayout.currentTab
            mTabList?.get(selectedPosition)?.orgId?: ""
        } else mOrgId
        orgId?.let {
            stringBuilder.append("ybmpage://shopactivity?orgId=${it}")
        }
        mShopCode?.let {
            stringBuilder.append("ybmpage://searchresult?isThirdCompany=0&shopCode=${it}")
        }
        return stringBuilder.toString()
    }

    private fun isFromShopCart() = source == "1"

    @SuppressLint("RestrictedApi", "NewApi")
    override fun initData() {
        super.initData()
        mOrgId = intent?.getStringExtra("orgId")
        mShopCode = intent?.getStringExtra("shopCode")
        isFromControlShop = intent?.getStringExtra("isControlShop") == "1"
        source = intent?.getStringExtra("source")
        isFilterUnableAddCart = intent?.getStringExtra("isFilterUnableAddCart")
        if (isFromControlShop) {
            //是否来自控销商城
            mViewModel.getShopTabs()
        } else {
            initFragment(mOrgId, mShopCode)
        }
        common_search_view?.apply {

            entry = "search_shop_home"

            tvTitleEdit.isFocusableInTouchMode = false
            tvTitleEdit.isFocusable = false
            tvTitleEdit.hint = "搜索此商家商品"
            tvTitleEdit.setOnClickListener {
                var ordIdShopCode: String? = ""
                var orgId: String? = ""
                if (isFromControlShop) {
                    val selectedPosition = tabLayout.currentTab
                    ordIdShopCode = mTabList?.get(selectedPosition)?.shopCode?: ""
                    orgId = mTabList?.get(selectedPosition)?.orgId?: ""
                } else {
                    ordIdShopCode = <EMAIL>
                    orgId = mOrgId
                }
                val shopCode = mShopCode
                shopCode?.let { RoutersUtils.open("ybmpage://shop_search_result?${IntentCanst.JG_ENTRANCE}=${mEntrance}&shopCode=${shopCode}") }
                mOrgId?.let { RoutersUtils.open("ybmpage://shop_search_result?${IntentCanst.JG_ENTRANCE}=${mEntrance}&orgId=${orgId}&orgId_shopCode=${ordIdShopCode}") }
                jgBtnClickTrack(this@ShopAllActvity,"顶部功能","搜索框")
            }
            ivScan.setOnClickListener(this)
            ivVoice.setOnClickListener(this)

            clickCallback = { content: String ->
                val orgId = if (isFromControlShop) {
                    val selectedPosition = tabLayout.currentTab
                    mTabList?.get(selectedPosition)?.orgId?: ""
                } else mOrgId
                val shopCode = mShopCode
                shopCode?.let { openNextPageUrl = "ybmpage://shop_search_result?${IntentCanst.JG_ENTRANCE}=${mEntrance}&shopCode=${shopCode}&&searchKey=" }
                orgId?.let { openNextPageUrl = "ybmpage://shop_search_result?${IntentCanst.JG_ENTRANCE}=${mEntrance}&orgId=${orgId}&&searchKey=" }
                jgBtnClickTrack(this@ShopAllActvity,"顶部功能",content)
            }
        }
        tabLayout.setOnTabSelectListener(object: OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                tabLayout.setCurrentTab(position)
                switchFragment(position)
            }

            override fun onTabReselect(position: Int) {}

        })
        mViewModel.shopTabsLiveData.observe(this) {
            if (it.isSuccess) {
                if (getTabLayoutEntityList(it.data).size > 1) {
                    tabLayout.visibility = View.VISIBLE
                    tabDivider.visibility = View.VISIBLE
                    tabLayout.setTabData(getTabLayoutEntityList(it.data))
                } else {
                    tabLayout.visibility = View.GONE
                    tabDivider.visibility = View.GONE
                }
                mTabList = it.data
                switchFragment(0)
            }
        }
    }

    /**
     * 初始化第一个Fragment
     */
    private fun initFragment(orgId: String?, shopCode: String?): Fragment {
        val ft = supportFragmentManager.beginTransaction()
        val fragment = ShopAllFragment()
        fragment.arguments = Bundle().apply {
            putString("orgId", orgId)
            putString("shopCode", shopCode)
            putString("source", source)
            putString("isFilterUnableAddCart", isFilterUnableAddCart)
        }
        fragment.setOnTabListCallback(::handleTabList)
        fragment.setOnPopShopCodeCallback { ordIdShopCode = it }
        ft.add(R.id.fl_container, fragment)
        currFragment = fragment
        mShopFragment.put(0, fragment)
        ft.commitAllowingStateLoss()
        return fragment
    }


    /**
     * 切换Fragment
     */
    @SuppressLint("CommitTransaction")
    private fun switchFragment(position: Int) {
        val ft = supportFragmentManager.beginTransaction()
        val isContainsPosition = mShopFragment.get(position) != null
        mOrgId = mTabList?.get(position)?.orgId?: ""
        val newFragment = if (isContainsPosition) {
            mShopFragment.get(position)
        } else {
            val fragment = ShopAllFragment()
            mShopFragment.put(position, fragment)
            fragment.arguments = Bundle().apply {
                val orgId = mTabList?.get(position)?.orgId?: ""
                putString("orgId", orgId)
                putString("shopCode", null)
                putString("source", source)
                putString("isFilterUnableAddCart", isFilterUnableAddCart)
            }
            fragment
        }
        if (newFragment == null) return
        if (currFragment != null && currFragment != newFragment) {
            if (!newFragment.isAdded) { // 判断是否被add过
                // 隐藏当前的fragment，将 下一个fragment 添加进去
                ft.hide(currFragment!!).add(R.id.fl_container, newFragment).commit()
            } else {
                // 隐藏当前的fragment，显示下一个fragment
                ft.hide(currFragment!!).show(newFragment).commit()
            }
            currFragment = newFragment
        } else if(currFragment == null){
            currFragment = newFragment
            if(!newFragment.isAdded){
                ft.add(R.id.fl_container, newFragment).commit()
            } else {
                ft.show(newFragment).commit()
            }
        }
    }

    /**
     * 处理tab
     */
    private fun handleTabList(tabList: List<ShopTab>?, shopCode: String?, orgId: String?) {
        if (orgId == this.mOrgId && !isFromControlShop && !tabList.isNullOrEmpty()) {
            mTabList = tabList
            isFromControlShop = true
            mShopFragment.put(0, currFragment)
            if (tabList.size > 1) {
                tabLayout.visibility = View.VISIBLE
                tabDivider.visibility = View.VISIBLE
            } else {
                tabLayout.visibility = View.GONE
                tabDivider.visibility = View.GONE
            }
            tabLayout.setTabData(getTabLayoutEntityList(tabList))
        }
    }

    private fun getTabLayoutEntityList(tabList: List<ShopTab>?): java.util.ArrayList<CustomTabEntity> {
        return java.util.ArrayList(tabList?.map { tab ->
            val title = tab.orgName?: ""
            object: CustomTabEntity {
                override fun getTabTitle(): String = title

                override fun getTabSelectedIcon(): Int = 0

                override fun getTabUnselectedIcon(): Int = 0

            }
        }?: emptyList())
    }

    override fun onEnableAnalysis(): Boolean {
        return false
    }
}