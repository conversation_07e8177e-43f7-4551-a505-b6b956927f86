package com.ybmmarket20.common;

import android.os.Bundle;
import androidx.annotation.Nullable;

import org.greenrobot.eventbus.EventBus;

/**
 * 懒加载Fragment
 * <AUTHOR>
 * 2019-10-13
 */
public abstract class LazyFragment extends BaseFragment {

	private boolean isViewCreated;
	private boolean isDataLoaded;
	private boolean mIsVisibleToUser;

	public abstract void loadData();

	@Override
	protected void initData(String content) {
		if(!EventBus.getDefault().isRegistered(this)) {
			EventBus.getDefault().register(this);
		}
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
		if(EventBus.getDefault().isRegistered(this)) {
			EventBus.getDefault().unregister(this);
		}
	}


	@Override
	public void setUserVisibleHint(boolean isVisibleToUser) {
		super.setUserVisibleHint(isVisibleToUser);
		this.mIsVisibleToUser = isVisibleToUser;
		tryLoadData();
	}

	@Override
	public void onActivityCreated(@Nullable Bundle savedInstanceState) {
		super.onActivityCreated(savedInstanceState);
		isViewCreated = true;
		tryLoadData();
	}

	private void tryLoadData() {
		if(isLazyLoad()&&isViewCreated&& mIsVisibleToUser && !isDataLoaded) {
			preLoadData();
			loadData();
			isDataLoaded = true;
		}
	}

	protected void preLoadData(){}

	public boolean isLazyLoad() {
		return true;
	}

	public String getTitle() {
		return "";
	}
}
