package com.ybmmarket20.db;

import static com.ybmmarketkotlin.utils.ChCrypto.aesDecrypt;
import static com.ybmmarketkotlin.utils.ChCrypto.aesEncrypt;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.apkfuns.logutils.LogUtils;
import com.ybmmarketkotlin.utils.ChCrypto;

/**
 * Created by ybm on 2017/7/13.
 */

public class DBHelper extends SQLiteOpenHelper {

    private static final String DB_NAME = "ybm_market.db";
    //    private static final int DB_VERSION = 1;
    private static final int DB_VERSION = 3;


    private DBHelper(Context context) {
        super(context, DB_NAME, null, DB_VERSION);
    }

    public synchronized static SQLiteDatabase getDb(Context context) {
        DBHelper helper = new DBHelper(context);
        SQLiteDatabase db;
        try {
            db = helper.getWritableDatabase();
        } catch (SQLiteException e) {
            e.printStackTrace();
            db = null;
        }
        return db;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        AccountTable.createTable(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {


        switch (oldVersion){
            case 1: updateDbFrom1To2(db);
            case 2: updateDbFrom2To3(db);
            // case2: 如果有多个版本迭代，逐步升级
            // case3:
            // case4:
        }
    }

    private void updateDbFrom1To2(SQLiteDatabase db) {
        try {
            LogUtils.e("dbhelper update frome 1 to 2");
            Cursor c = db.rawQuery("SELECT * FROM tb_account", null);
            c.moveToFirst();
            while (!c.isAfterLast()) {
                String userName = c.getString(c.getColumnIndex(AccountTable.COLUMN_USER_NAME));
                String pwd = c.getString(c.getColumnIndex(AccountTable.COLUMN_PASSWORD));
                String merchantId = c.getString(c.getColumnIndex(AccountTable.COLUMN_MERCHANT_ID));
                String changeUserName = "update [tb_account] set " + AccountTable.COLUMN_USER_NAME + " = '" + ChCrypto.INSTANCE.aesEncrypt(userName) + "' where " + AccountTable.COLUMN_MERCHANT_ID + "=  " + merchantId;
                String changePwd = "update [tb_account] set " + AccountTable.COLUMN_PASSWORD + " = '" + ChCrypto.INSTANCE.aesEncrypt(pwd) + "' where " + AccountTable.COLUMN_MERCHANT_ID + "=  " + merchantId;
                db.execSQL(changeUserName);
                db.execSQL(changePwd);
                c.moveToNext();
            }
            updateDbFrom2To3(db);
        } catch (Exception e) {

        }
    }

    private void updateDbFrom2To3(SQLiteDatabase db) {
        LogUtils.e("dbHelper update from 2 to 3");
        Cursor c = null;
        try {
            //重命名表
            String RENAME_TABLE = "alter table tb_account rename to _temp_tb_account";
            //创建新表
            String CREATE_TABLE = AccountTable.getCreateTableSqlStr();
            //删除重命名的表
            String DROP_TABLE = "drop table _temp_tb_account";
            Log.e("update_RENAME_TABLE = ", RENAME_TABLE);
            Log.e("update_CREATE_TABLE = ", CREATE_TABLE);
            Log.e("update_DROP_TABLE = ", DROP_TABLE);
            db.execSQL(RENAME_TABLE);
            db.execSQL(CREATE_TABLE);
            c = db.rawQuery("SELECT * FROM _temp_tb_account", null);
            c.moveToFirst();
            while (!c.isAfterLast()) {
                ContentValues cv = new ContentValues();
                String merchantId = c.getString(c.getColumnIndex(AccountTable.COLUMN_MERCHANT_ID));
                String userName = c.getString(c.getColumnIndex(AccountTable.COLUMN_USER_NAME));
                String password = c.getString(c.getColumnIndex(AccountTable.COLUMN_PASSWORD));
                String shopPhone = c.getString(c.getColumnIndex(AccountTable.COLUMN_SHOP_PHONE));
                String address = c.getString(c.getColumnIndex(AccountTable.COLUMN_ADDRESS));
                String addTime = c.getString(c.getColumnIndex(AccountTable.COLUMN_ADD_TIME));
                String shopName = c.getString(c.getColumnIndex(AccountTable.COLUMN_SHOP_NAME));
                cv.put(AccountTable.COLUMN_MERCHANT_ID, merchantId);
                cv.put(AccountTable.COLUMN_USER_NAME, userName);
                cv.put(AccountTable.COLUMN_PASSWORD, aesEncrypt(password));
                cv.put(AccountTable.COLUMN_SHOP_PHONE, shopPhone);
                cv.put(AccountTable.COLUMN_ADDRESS, address);
                cv.put(AccountTable.COLUMN_ADD_TIME, addTime);
                cv.put(AccountTable.COLUMN_SHOP_NAME, shopName);
                db.insert("tb_account", null, cv);
                c.moveToNext();
            }
            db.execSQL(DROP_TABLE);
        } catch (Exception e) {
            Log.e("updateDbFrom2To3Error", e.getMessage());
            e.printStackTrace();
        } finally {
            if (c != null) {
                c.close();
            }
        }
    }
}
