package com.ybmmarket20.home;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.reflect.TypeToken;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.PaymentActivity;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.adapter.ShopCheckAdapter;
import com.ybmmarket20.adapter.ShopCheckForRecommendAdapter;
import com.ybmmarket20.bean.AdBagListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartActivityBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.RefreshWrapperPagerBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.bean.SettleBean;
import com.ybmmarket20.bean.cart.CartBean;
import com.ybmmarket20.bean.cart.CartCompanyBean;
import com.ybmmarket20.bean.cart.CartDiscountDataBean;
import com.ybmmarket20.bean.cart.CartItemBean;
import com.ybmmarket20.bean.cart.CartShopList;
import com.ybmmarket20.bean.cart.CartShoppingGroupFrontBean;
import com.ybmmarket20.bean.cart.CartSortedNewBean;
import com.ybmmarket20.bean.cart.FreightInfoBean;
import com.ybmmarket20.common.AdDialog2;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.BaseBottomPopWindow;
import com.ybmmarket20.view.CartViewLayout;
import com.ybmmarket20.view.FreightTipDialog;
import com.ybmmarket20.view.MyFastScrollView;
import com.ybmmarket20.view.ProductEditLayout;
import com.ybmmarket20.view.ShowBigBitmapPopPublishForLongPic;
import com.ybmmarket20.view.ShowBottomCartCouponDialog;
import com.ybmmarket20.view.ShowCertainPopWindow;
import com.ybmmarket20.view.ShowFreightPopWindow;
import com.ybmmarket20.view.ShowPackageTipPopWindow;
import com.ybmmarketkotlin.utils.AptitudeTipsUtils;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import static com.ybmmarket20.bean.VoucherListBean.QUERY_COUPON_TYPE_PLATFORM;
import static com.ybmmarketkotlin.activity.FreightAddOnItemActivityKt.ACTIVITY_TYPE_FROM_CART;

/**
 * 购物车
 */
@Deprecated
public class CartFragment extends BaseFragment {

    @Bind(R.id.ll_title)
    LinearLayout llTitle;
    @Bind(R.id.title_left)
    RelativeLayout titleLeft;
    @Bind(R.id.tv_left)
    TextView tvLeft;
    @Bind(R.id.title_tv)
    TextView titleTv;
    @Bind(R.id.title_right)
    TextView titleRight;
    @Bind(R.id.tv_platform_coupon)
    TextView tvPlatformCoupon;//右上角平台优惠券
    @Bind(R.id.cart_list_lv)
    CommonRecyclerView cartListLv;
    @Bind(R.id.cart_ll2_tv1)
    TextView cartLl2Tv1;
    @Bind(R.id.cart_ll2_tv2)
    TextView cartLl2Tv2;
    @Bind(R.id.cart_ll2_tv4)
    TextView cartLl2Tv4;
    @Bind(R.id.cart_ll2_tv5)
    TextView cartLl2Tv5;
    @Bind(R.id.cart_rl2)
    RelativeLayout cartRl2;
    @Bind(R.id.shop_check)
    CheckBox shopCheck;
    @Bind(R.id.cart_bt)
    Button cartBt;
    @Bind(R.id.iv_cart_action)
    CartViewLayout ivCartAction;
    @Bind(R.id.iv_cart_notice)
    CartViewLayout ivCartNotice;
    @Bind(R.id.cart_tv_edit)
    TextView cartTvEdit;
    @Bind(R.id.cart_tv_select)
    TextView mCartTvSelect;
    @Bind(R.id.iv_fastscroll)
    MyFastScrollView mFastscroll;
    @Bind(R.id.iv_ad_suspension)
    ImageView adSuspension;
    @Bind(R.id.bottomBar)
    LinearLayout mBottomBar;
    @Bind(R.id.cart_head_bg)
    LinearLayout cartHeadBg;
    @Bind(R.id.cart_head_tv_proprietary)
    TextView cartHeadTvProprietary;
    @Bind(R.id.cart_head_check)
    CheckBox cartHeadCheck;
    @Bind(R.id.cart_head_ll)
    LinearLayout cartHeadLl;
    @Bind(R.id.ll_coupon_wrapper)
    LinearLayout llCouponWrapper;
    //包邮凑单粘性bar
    @Bind(R.id.rl_freight_add_on_item_head)
    RelativeLayout rlFreightAddOnItemHead;
    @Bind(R.id.tv_freight_add_on_item_tips)
    TextView tvFreightAddOnItemTips;
    @Bind(R.id.cart_new_tv_title_url)
    TextView tvFreightToAddOnItem;
    //超重提示
    @Bind(R.id.ll_freight_over_weight_tips)
    LinearLayout llFreightOverWeightTips;
    @Bind(R.id.tv_over_weight_tips)
    TextView tvOverWeightTips;
    @Bind(R.id.rtv_over_weight_look)
    RoundTextView rtvOverWeightLook;
    @Bind(R.id.cart_discount_detail)
    TextView tvCartDiscountShowDetail;

    @Bind(R.id.fl_cart_discount)
    ConstraintLayout flCartDiscount;
    @Bind(R.id.layout_aptitude_tip)
    ConstraintLayout layoutAptitudeTip;
    @Bind(R.id.tv_aptitude_tip)
    TextView tvAptitudeTip;

    public int number = 0;    //记录对话框中的数量
    public double price = 0;    //商品总价
    private String merchantid;    //注意：不要初始化时候赋值，用到的时候去取
    private boolean hidden, isFirst;
    private String name, action;
    private double differenceAmount;
    private int mCurrentPosition = 0;
    private boolean showHead = true;
    private boolean isShow = false;

    private static final String emptyStr = "请选中要删除的商品";
    public static String CLOSE_AN_ACCOUNT = "去结算";
    public static String CLOSE_AN_ACCOUNT_COUPON = "领券结算";
    public static String DELETE = "删除";
    public static String EDIT = "编辑";
    public static String ACCOMPLISH = "完成";

    private static final int WHAT_ALLPRICE_SELECTED = 10;
    private static final int WHAT_ALLBTN_SELECTED = 11;
    private static final int WHAT_CHECKNUB_SELECTED = 12;
    private static final int WHAT_NEWDATA_SELECTED = 14;
    private static final int WHAT_FAVORABLEMONEY_SELECTED = 15;
    private static final int WHAT_COUPON_REDUCE_MONEY = 16;

    private ShopCheckForRecommendAdapter adapter;
    private HandlerGoodsDao goodsDao;
    private static List<Integer> isCheckSelected;
    ShowFreightPopWindow popWindowPackage;
    private Intent mIntent;
    private List<CartItemBean> groupList = new ArrayList<>();
    private CartBean cartBean;

    //猜你喜欢
    private GoodsListAdapter goodsListAdapter;
    private int hasPlatFormVouchers;//是否包含平台券0不包含1包含
    private int hasVouchersNotReceived;//是否领券结算0没有需要领的券1:包含需要领的券

    @Override
    protected void initData(String content) {

        initReceiver();
        isFirst = true;

        initPage();
        initListener();

        if (!TextUtils.isEmpty(name)) {
            if (titleTv != null) {
                titleTv.postDelayed(() -> {
                    if (titleTv != null) {
                        setBackTitle(name, action);
                    }
                }, 300);
            }
        }
        goodsDao = HandlerGoodsDao.getInstance();
        //猜你喜欢监听
        goodsListAdapter = new GoodsListAdapter(R.layout.item_goods, getNotNullActivity(), ProductEditLayout.FROMPAGE_CART_RECOMMEND);
        goodsListAdapter.setOnListItemClickListener(rows -> openUrl("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + rows.getId()));

        adapter = new ShopCheckForRecommendAdapter(groupList, handler, goodsListAdapter);
        JgTrackBean jgTrackBean =  new JgTrackBean();
        jgTrackBean.setEntrance("购物车");
        adapter.jgTrackBean = jgTrackBean;
        adapter.setOnSelectListener(new BaseBottomPopWindow.OnSelectListener() {

            @Override
            public void getValue(SearchFilterBean show) {

            }

            @Override
            public void OnDismiss() {
                getMyCart();
            }
        });
        View emptyView = getNotNullActivity().getLayoutInflater().inflate(R.layout.empty_view_cart, (ViewGroup) cartListLv.getParent(), false);

        cartListLv.setRefreshEnable(true);
        adapter.setEmptyView(emptyView);

        cartListLv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getMyCart();//用户下拉刷新，调用更新
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));
            }

            @Override
            public void onLoadMore() {//这里加载更多走的是猜你喜欢
                /*if (isKaUser) {
                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
                        @Override
                        public void run() {
                            adapter.loadMoreEnd();
                        }
                    }, 50);
                    return;
                }*/
                if (!isKaUser) {
                    page++;
                    if (page == 2) {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                //解决当页面处于猜你喜欢标题和粘性标题之间，重新加载购物车接口，
                                // 清空groupList数据可能会引起先走加载更多第二页数据，再加载第一页数据的情况
                                getRecommendData(page);
                            }
                        }, 500);
                    } else {
                        getRecommendData(page);
                    }
                }

            }
        });
        adapter.setOnItemClickListener(rows -> {
            if (rows != null) {
                Intent intent = new Intent(getNotNullActivity(), ProductDetailActivity.class);
                intent.putExtra(IntentCanst.PRODUCTID, rows.getSkuId() + "");
                startActivity(intent);
            }
        });
        adapter.setOnDelListener(new ShopCheckAdapter.onSwipeListener() {
            @Override
            public void onDel(int pos) {
                //且如果想让侧滑菜单同时关闭，需要同时调用 ((CstSwipeDelMenu) holder.itemView).quickClose();
                singleDelete(pos, true);
            }

            @Override
            public void onCollect(int pos) {
                setCollect(pos);
            }

            @Override
            public void onAllDel(int pos) {
                if (cartListLv == null || pos == -1 || pos >= groupList.size()) {
                    return;
                }
                CartItemBean cartGroupBean = groupList.get(pos);
                if (cartGroupBean != null && cartGroupBean.getValid() == 0) {

                    CartItemBean subBean = groupList.get(pos + 1);
                    if (subBean != null) {
                        deleteLoseEfficacy(pos + 1);
                    }
                }
            }

            @Override
            public void onRemoveProduct(int pos) {
                singleDelete(pos, false);
            }
        });

        adapter.setAddCartCompletedListener(isCompleted -> {
            cartBt.setEnabled(isCompleted);
            return null;
        });

        cartListLv.setAdapter(adapter);

    }

    /*
     * 初始化页面
     * */
    private void initPage() {
        titleRight.setText("编辑");
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, llTitle);
        cartListLv.setRefreshEnable(true);
        cartRl2.setVisibility(View.VISIBLE);
        cartTvEdit.setVisibility(View.GONE);
        isCheckSelected = new ArrayList<>();
        if (adapter != null) {
            adapter.setCheckBoxVisibility(View.VISIBLE);
        }
        AptitudeTipsUtils.Companion.initAptitudeOverdueTip(getNotNullActivity(), layoutAptitudeTip, tvAptitudeTip, XyyIoUtil.PAGE_SHOPPINGCART);
    }


    /*
     * 初始化监听
     * */
    private void initListener() {
        shopCheck.setOnClickListener(new onClickListener(shopCheck));
        UiUtils.expandViewTouchDelegate(shopCheck, ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100));
        cartListLv.setOnScrollListener(new CommonRecyclerView.OnScrollListener() {
            WrapLinearLayoutManager linearLayoutManager = (WrapLinearLayoutManager) cartListLv.getLayoutManager();
            int mSuspensionHeight = UiUtils.dp2px(40);

            @Override
            public void onScrollChanged(int x, int y) {

            }

            @Override
            public void onScrollRollingDistance(int y, int dy) {
                if (mFastscroll != null) {
                    mFastscroll.showFastScroll(cartListLv, y, dy, null);
                }

                if (groupList != null && groupList.size() > 0) {
                    mSuspensionHeight = cartHeadBg.getHeight();

                    int firstVisPos = linearLayoutManager.findFirstVisibleItemPosition();

                    CartItemBean firstVisibleItem = groupList.get(firstVisPos);
                    CartItemBean nextItem;
                    View nextView;
                    if (groupList.size() > firstVisPos + 1) {
                        nextItem = groupList.get(firstVisPos + 1);
                        nextView = linearLayoutManager.findViewByPosition(firstVisPos + 1);
                    } else {
                        return;
                    }

                    if (y > 0) {
                        if (!isShow) {
                            cartHeadBg.setAlpha(1);
                            isShow = true;
                        }
                    } else {
                        if (isShow) {
                            cartHeadBg.setAlpha(0);
                            isShow = false;
                        }
                    }

                    if (dy > 0) {

                        if (nextItem.getItemType() == CartItemBean.header_proprietary_head || nextItem.getItemType() == CartItemBean.header_recommend) {

                            if (nextView != null && nextView.getTop() <= mSuspensionHeight) {
                                //被顶掉的效果
                                cartHeadBg.setY(-(mSuspensionHeight - nextView.getTop()));
                            } else {
                                cartHeadBg.setY(0);
                            }
                        }

                        //判断是否需要更新悬浮条
                        if (mCurrentPosition != firstVisPos && (firstVisibleItem.getItemType() == CartItemBean.header_proprietary_head || firstVisibleItem.getItemType() == CartItemBean.header_recommend)) {
                            mCurrentPosition = firstVisPos;
                            //更新悬浮条
                            updateSuspensionBar();
                            cartHeadBg.setY(0);
                        }
                    } else {

                        if (nextItem.getItemType() == CartItemBean.header_proprietary_head) {
                            mCurrentPosition = firstVisibleItem.getItemType() == CartItemBean.header_proprietary_head ? firstVisPos : firstVisibleItem.getParentPostPos();
                            updateSuspensionBar();

                            if (nextView != null && nextView.getTop() <= mSuspensionHeight) {
                                //被顶掉的效果
                                cartHeadBg.setY(-(mSuspensionHeight - nextView.getTop()));
                            } else {
                                cartHeadBg.setY(0);
                            }
                        } else if (nextItem.getItemType() == CartItemBean.header_recommend) {
                            mCurrentPosition = firstVisibleItem.getItemType() == CartItemBean.header_recommend ? firstVisPos : firstVisibleItem.getParentPostPos();
                            updateSuspensionBar();

                            if (nextView != null && nextView.getTop() <= mSuspensionHeight) {
                                //被顶掉的效果
                                cartHeadBg.setY(-(mSuspensionHeight - nextView.getTop()));
                            } else {
                                cartHeadBg.setY(0);
                            }
                        }
                    }
                }
            }

            @Override
            public void onScrollState(int i) {

            }

        });
    }

    /*
     * head
     * */
    private void updateSuspensionBar() {
        if (cartListLv == null) {
            return;
        }
        if (groupList != null && groupList.size() > 0) {
            if (groupList.size() <= mCurrentPosition) {
                mCurrentPosition = groupList.size() - 1;
            }
            CartItemBean cartItemBean = groupList.get(mCurrentPosition);
            cartHeadTvProprietary.setText(cartItemBean.getCompanyName());
            //处理凑单包邮
            rlFreightAddOnItemHead.setVisibility(cartItemBean.getFreightTipsShowStatus() == 1 ? View.VISIBLE : View.GONE);//1是展示 0不展示
            tvFreightAddOnItemTips.setText(cartItemBean.getFreightTips());//包邮提示语
            tvFreightToAddOnItem.setText(cartItemBean.getFreightUrlText());//去凑单 展示
            tvFreightToAddOnItem.setVisibility(!TextUtils.isEmpty(cartItemBean.getFreightUrlText()) ? View.VISIBLE : View.GONE);
            tvFreightToAddOnItem.setOnClickListener(v -> {//去凑单 点击去凑单包邮
//                RoutersUtils.open("ybmpage://freightaddonitem/" + ACTIVITY_TYPE_FROM_CART);
                RoutersUtils.open(cartItemBean.getFreightJumpUrl());
            });
            tvFreightAddOnItemTips.setOnClickListener(v -> {//包邮感叹号点击
                new FreightTipDialog(getNotNullActivity()).showTip(cartItemBean.getMainShopCode());
            });
            tvFreightAddOnItemTips.setEnabled(cartItemBean.getFreightIconShowStatus() == 1);//包邮感叹号是否可点击
            //包邮感叹号是否显示
            tvFreightAddOnItemTips.setCompoundDrawablesWithIntrinsicBounds(null, null, cartItemBean.getFreightIconShowStatus() == 1 ? getResources().getDrawable(R.drawable.icon_cart_freight_add_on_item_normal) : null, null);

            //第三方药店名称展示
            if (cartItemBean.getIsThirdCompany() == 1) {
                cartHeadTvProprietary.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_payment_pop), null, getResources().getDrawable(R.drawable.right_new), null);
                // todo 增加一个pop店铺优惠券的展示
                llCouponWrapper.setVisibility(View.GONE);
            } else {
                llCouponWrapper.setVisibility(View.GONE);
                cartHeadTvProprietary.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_autotrophy_new), null, getResources().getDrawable(R.drawable.right_new), null);
            }
            cartHeadTvProprietary.setOnClickListener(v -> {
                RoutersUtils.open(cartItemBean.getShopJumpUrl());
            });


            cartHeadCheck.setChecked(cartItemBean.getStatus() == 1);
            UiUtils.expandViewTouchDelegate(cartHeadCheck, ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100), ConvertUtils.dp2px(100));
            cartHeadCheck.setOnClickListener(v -> selectOrCancelAllItem(cartHeadCheck.isChecked(), cartItemBean.getOrgId(), cartItemBean.getIsThirdCompany() + ""));
            if (cartItemBean.getItemType() == CartItemBean.header_recommend) {
                cartHeadTvProprietary.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
                cartHeadLl.setVisibility(View.GONE);
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) cartHeadTvProprietary.getLayoutParams();
                params.leftMargin = ConvertUtils.dp2px(10);
            } else {
                cartHeadLl.setVisibility(View.VISIBLE);
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) cartHeadTvProprietary.getLayoutParams();
                params.leftMargin = 0;
            }
        } else {
            cartHeadBg.setVisibility(View.GONE);
        }

    }

    /**
     * 初始化购物车数据
     */
    public void getMyCart() {
        requestCartData();
    }

    private void getMyRecommend() {
        if (!isKaUser) {
            page = 1;
            getRecommendData(page);
        }
    }

    private void requestCartData() {
        merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);

        HttpManager.getInstance().post(AppNetConfig.CART_V1, params, new BaseResponse<CartBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartBean> obj, CartBean cartBean) {
                getMyRecommend();
                dismissProgress();
                setRefreshing();
                if (cartListLv == null) {
                    return;
                }

                if (obj != null) {
                    if (obj.isSuccess()) {
                        adapter.setFlowData(mFlowData);
                        if (cartBean != null) {
                            //每次请求购物车，先清空数据库对应商品，再同步.
                            goodsDao.deleteItems();

                            setActivity(cartBean);

                            //购物车数量大于0才显示底部栏
                            setBottomBar(cartBean);
                            //显示超重提示
                            setOverWeightBar(cartBean);
                            //领券结算 平台券入口展示状态赋值及显示
                            setPlatformCoupon(cartBean);
                            // 初始化设置超重提示
                            setFreightPopwindow(cartBean);

                            mDiscountData = cartBean.promoAmountDto;
                            if (mCartDiscountUtils != null) {
                                mCartDiscountUtils.updateData(mDiscountData);
                            }
                            // 设置折后价明细
                            setDiscountView();

                            ArrayList<CartItemBean> groupListNew = new ArrayList<>();
                            /*------------------------正常商品的组-------------------------*/
                            List<CartCompanyBean> company = cartBean.getCompany();
                            setProductList(company, groupListNew);

                            /*------------------------失效商品的组-------------------------*/
                            CartShoppingGroupFrontBean novalidGroup = cartBean.getNovalidGroup();
                            setFailureList(novalidGroup, groupListNew);

                            /*------------------------显示head-------------------------*/
                            boolean isShow = isShowHead(groupListNew);

                            if (groupList == null) {
                                groupList = new ArrayList<>();
                            }
                            groupList.clear();
                            groupList.addAll(groupListNew);

                            adapter.setNewData(groupList);

                            if (isKaUser) {
                                adapter.notifyDataSetChanged();
                                adapter.setEnableLoadMore(false);
                            } else {
                                adapter.setEnableLoadMore(true);
                                adapter.loadMoreComplete();
                            }
                            if (groupListNew.size() > 0 && adapter.getHeaderLayoutCount() > 0) {
                                //购物车有数据先清除头布局
                                adapter.removeAllHeaderView();
                            }

                            //更新悬浮条
                            if (isShow && showHead) {
                                cartHeadBg.setAlpha(0);
                                showHead = false;
                            }
                            cartHeadBg.setVisibility(isShow ? View.VISIBLE : View.GONE);
                            updateSuspensionBar();

                            //计算当前购物车已选中商品是否满足起送价
                            setDifferenceAmount(cartBean);
                            //更改商品数量后，通知Activity更新需要付费的总金额
                            handler.sendMessage(handler.obtainMessage(WHAT_ALLPRICE_SELECTED, cartBean.getPayAmount()));
                            handler.sendMessage(handler.obtainMessage(WHAT_FAVORABLEMONEY_SELECTED, cartBean.getRePrice()));
                            handler.sendMessage(handler.obtainMessage(WHAT_COUPON_REDUCE_MONEY, cartBean.getVoucherDiscountAmount()));

                            if (mIntent == null) {
                                mIntent = new Intent();
                            }
                            mIntent.setAction(IntentCanst.ACTION_SHOPNUMBER);
                            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(mIntent);
                        }
                    } else {
                        adapter.setNewData(groupList);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                getMyRecommend();
                dismissProgress();
                if (cartListLv == null) {
                    return;
                }
                setRefreshing();
                adapter.setNewData(groupList);
            }
        });
    }


    private int canSettle;

    private void setFreightPopwindow(CartBean cartBean) {
        this.cartBean = cartBean;
        canSettle = cartBean.canSettle;
        popWindowPackage = new ShowFreightPopWindow(getContext());
        popWindowPackage
                .setOutsideTouchable(false)
                .setContent(cartBean)
                .setOnButtonClickListener((notSubmitOrderOrgIds) -> {
                    goCheckOut(notSubmitOrderOrgIds);
                    popWindowPackage.dismiss();
                });

    }

    private void setPlatformCoupon(CartBean cartBean) {//平台券赋值展示
        hasPlatFormVouchers = cartBean.getHasPlatFormVouchers();
        hasVouchersNotReceived = cartBean.getHasVouchersNotReceived();
        tvPlatformCoupon.setVisibility(hasPlatFormVouchers == 1 ? View.VISIBLE : View.GONE);
    }

    /**
     * 设置折后价明细
     */
    private void setDiscountView() {

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) cartLl2Tv5.getLayoutParams();
        if (mDiscountData == null) {
            layoutParams.removeRule(RelativeLayout.START_OF);
            layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            cartLl2Tv5.setLayoutParams(layoutParams);
            tvCartDiscountShowDetail.setVisibility(View.GONE);
        } else {
            tvCartDiscountShowDetail.setVisibility(View.VISIBLE);
            layoutParams.removeRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            layoutParams.addRule(RelativeLayout.START_OF, tvCartDiscountShowDetail.getId());
        }
    }

    private void setOverWeightBar(CartBean cartBean) {//显示超重提示条
        llFreightOverWeightTips.setVisibility(cartBean.getSpecialProductTipsShowStatus() == 1 ? View.VISIBLE : View.GONE);//1展示 0不展示
        rtvOverWeightLook.setText(TextUtils.isEmpty(cartBean.getSpecialProductUrlTitle()) ? getString(R.string.str_freight_over_weight_cart_bottom_look) : cartBean.getSpecialProductUrlTitle());
        tvOverWeightTips.setText(TextUtils.isEmpty(cartBean.getSpecialProductTips()) ? getString(R.string.str_freight_over_weight_cart_bottom_tip) : cartBean.getSpecialProductTips());
        tvOverWeightTips.setOnClickListener(v -> {//点击显示运费规则
            new FreightTipDialog(getNotNullActivity()).showTip();
        });
    }

    private void setCartEmptyView() {//购物车数据为空设置空
        View EmptyView = View.inflate(getNotNullActivity(), R.layout.layout_cart_empty_data_head_view, null);
        if (adapter != null) {
            if (adapter.getHeaderLayoutCount() > 0)
                adapter.removeAllHeaderView();
            adapter.addHeaderView(EmptyView);
        }
    }

    private int page = 1;
    private int limit = 10;
    private boolean isLoadData;//是否加载过数据（是否上传过埋点数据）
    private BaseFlowData recommendFlowData = FlowDataAnalysisManagerKt.generateDefaultBaseFlowData();

    /**
     * 获取推荐数据
     */
    private void getRecommendData(int mPage) {
        RequestParams params = new RequestParams();
        params.put("offset", mPage + "");
        params.put("limit", limit + "");
        params.put("pageType", "5");//2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐 5 购物车页推荐 6 付款结果页推荐 7 我的-个人中心页内推荐
        params.put("csuId", SpUtil.getMerchantid());
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("timestamp", System.currentTimeMillis() + "");
        if (isLoadData)
            FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, recommendFlowData);
        HttpManager.getInstance().post(AppNetConfig.HOME_RECOMMENDED_SKU, params, new BaseResponse<RefreshWrapperPagerBean<RowsBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<RefreshWrapperPagerBean<RowsBean>> obj, RefreshWrapperPagerBean<RowsBean> data) {
                super.onSuccess(content, obj, data);
                if (obj != null && obj.isSuccess() && data != null && data.getRows().size() > 0) {
                    List<RowsBean> recommendList = data.getRows();
                    if (groupList != null && groupList.size() == 0) {//购物车无数据
                        setCartEmptyView();
                    }
                    // guanchong 为你推荐（购物车底部）
                    if (mPage == 1) {
                        CartItemBean companyTitle = new CartItemBean();
                        companyTitle.setItemType(CartItemBean.header_recommend);
                        companyTitle.setCompanyName(getString(R.string.maybe_like));
                        groupList.add(companyTitle);
                    }
                    ArrayList<CartItemBean> recommendDataList = new ArrayList<>();
                    for (RowsBean rowsBean : recommendList) {
                        CartItemBean itemBean = new CartItemBean();
                        itemBean.rowsBean = rowsBean;
                        itemBean.setItemType(CartItemBean.content_recommend);
                        recommendDataList.add(itemBean);
                    }
                    groupList.addAll(recommendDataList);

                    // 请求并更新折后价
                    AdapterUtils.INSTANCE.getAfterDiscountPriceForCart(recommendDataList, groupList, adapter);
                    //adapter.setNewData(groupList);
                    adapter.notifyDataChangedAfterLoadMore(data.getRows().size() == limit);

                    if (!isLoadData) {
                        recommendFlowData.setSId(data.getSid());
                        recommendFlowData.setSpId(data.getSpId());
                        recommendFlowData.setSpType(data.getSpType());
                        isLoadData = true;
                        FlowDataEventAnalysisKt.flowDataPageCommoditySearch(recommendFlowData);
                        if (goodsListAdapter != null) {
                            goodsListAdapter.setFlowData(recommendFlowData);
                        }
                    }
                } else {
                    adapter.loadMoreEnd();
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                adapter.loadMoreFail();
            }

            @Override
            public BaseBean json(String content, Type type) {
                return super.json(content, new TypeToken<BaseBean<RefreshWrapperPagerBean<RowsBean>>>() {
                }.getType());
            }
        });
    }

    private void setDifferenceAmount(CartBean cartBean) {
        differenceAmount = cartBean.getDifferenceAmount();
        if (!adapter.getFinishOrEdit()) {
            judgeDifferenceAmount(differenceAmount);
        }
    }

    private void setBottomBar(CartBean cartBean) {
        if (cartBean.getCompany() != null && cartBean.getCompany().size() > 0) {
            mBottomBar.setVisibility(View.VISIBLE);
        } else {
            mBottomBar.setVisibility(View.GONE);
        }
    }

    private CartDiscountDataBean mDiscountData;
    private CartDiscountUtils mCartDiscountUtils;

    private void showDiscountDialog() {
        if (mCartDiscountUtils == null && mDiscountData != null) {
            mCartDiscountUtils = new CartDiscountUtils(flCartDiscount, mBottomBar, tvCartDiscountShowDetail, mDiscountData);
        }
        mCartDiscountUtils.dispatchDisCountAnim();
    }

    private void setActivity(CartBean cartBean) {
        bindData(cartBean.getActivity());
        setNotifyData(cartBean.getNotify());
    }

    private boolean isShowHead(ArrayList<CartItemBean> groupListNew) {
        int index = 0;
        int parentPostPos = index;
        boolean isShow = false;
        if (groupListNew != null && groupListNew.size() > 0) {
            for (CartItemBean itemBean : groupListNew) {
                if (itemBean.getItemType() == CartItemBean.header_proprietary_head) {
                    itemBean.setParentPostPos(index);
                    parentPostPos = index;
                    isShow = true;
                } else {
                    itemBean.setParentPostPos(parentPostPos);
                }
                index++;
            }
        }
        return isShow;
    }

    private void setProductList(List<CartCompanyBean> company, ArrayList<CartItemBean> groupListNew) {
        if (company != null && company.size() > 0) {
            boolean isCheck = true;
            int normalSize = 0;
            for (int i = 0; i < company.size(); i++) {

                //公司list
                CartCompanyBean cartcompanyBean = company.get(i);
                /*------------------------自营或第三方商家title-------------------------*/
                CartItemBean companyTitle = new CartItemBean();
                companyTitle.setCompanyName(cartcompanyBean.getCompanyName());
                companyTitle.setShopJumpUrl(cartcompanyBean.getShopJumpUrl());
                companyTitle.setCompanyType(cartcompanyBean.getCompanyType());
                companyTitle.setCompanyCode(cartcompanyBean.getCompanyCode());
                companyTitle.setIsThirdCompany(cartcompanyBean.getIsThirdCompany());
                if (cartcompanyBean.getIsThirdCompany() == 1 && cartcompanyBean.getShop() != null && cartcompanyBean.getShop().size() > 0) {
                    companyTitle.setIsHaveVoucher(cartcompanyBean.getShop().get(0).getIsHaveVoucher());
                    companyTitle.setShopCode(cartcompanyBean.getShop().get(0).getShopCode());
                }
                companyTitle.setOrgId(cartcompanyBean.getOrgId());
                companyTitle.setMainShopCode(cartcompanyBean.getMainShopCode());
                companyTitle.setStatus(cartcompanyBean.getSelectStatus());
                companyTitle.setItemType(CartItemBean.header_proprietary_head);
                companyTitle.setFreightTipsShowStatus(cartcompanyBean.getFreightTipsShowStatus());
                companyTitle.setFreightTips(cartcompanyBean.getFreightTips());
                companyTitle.setFreightUrlText(cartcompanyBean.getFreightUrlText());
                companyTitle.setFreightJumpUrl(cartcompanyBean.getFreightJumpUrl());
                companyTitle.setFreightIconShowStatus(cartcompanyBean.getFreightIconShowStatus());
                groupListNew.add(companyTitle);

                //公司下面的商城list
                List<CartShopList> shop = cartcompanyBean.getShop();
                /*------------------------商城-------------------------*/
                if (shop != null && shop.size() > 0) {

                    for (int a = 0; a < shop.size(); a++) {
                        List<CartItemBean> shopItemList = new ArrayList<>();//店铺下的商品集合
                        CartShopList cartShopList = shop.get(a);
                        CartItemBean shopTitle = null;
                        if (cartcompanyBean.isThirdCompany() && !TextUtils.isEmpty(cartShopList.getShopName())) {
                            shopTitle = new CartItemBean();
                            shopTitle.setShopName(cartShopList.getShopName());
                            shopTitle.setShopType(cartShopList.getShopType());
                            shopTitle.setShopCode(cartShopList.getShopCode());
                            shopTitle.setStatus(cartShopList.getSelectStatus());
                            shopTitle.setIsHaveVoucher(cartShopList.getIsHaveVoucher());
                            shopTitle.setVoucherUrl(cartShopList.getVoucherUrl());
                            shopTitle.setAppLinkUrl(cartShopList.getAppLinkUrl());
                            shopTitle.freightTipsShowStatus = cartShopList.freightTipsShowStatus;
                            shopTitle.freightTips = cartShopList.freightTips;
                            shopTitle.setCompanyCode(cartcompanyBean.getCompanyCode());
                            shopTitle.setCompanyName(cartcompanyBean.getCompanyName());
                            shopTitle.setCompanyType(cartcompanyBean.getCompanyType());
                            shopTitle.setItemType(CartItemBean.header_shop_head);
                            groupListNew.add(shopTitle);
                        }

                        /*------------------------优惠券信息文案---------------------------*/
                        if (cartcompanyBean.isThirdCompany() && cartShopList.getReturnVoucherInfo() != null) {
                            CartActivityBean returnVoucherInfo = cartShopList.getReturnVoucherInfo();
                            if (!TextUtils.isEmpty(returnVoucherInfo.text)) {
                                CartItemBean returnVoucherInfoTitle = new CartItemBean();
                                returnVoucherInfoTitle.setTitle(returnVoucherInfo.text);
                                returnVoucherInfoTitle.setIsMatch(returnVoucherInfo.isMatch);
                                returnVoucherInfoTitle.setTitleUrl(returnVoucherInfo.action);
                                returnVoucherInfoTitle.setItemType(CartItemBean.header_return_ticket_head);
                                groupListNew.add(returnVoucherInfoTitle);
                            }
                        }

                        /*------------------------满减title和商品的组-------------------------*/
                        List<CartShoppingGroupFrontBean> shoppingGroupFront = cartShopList.getShoppingGroupFrontDtos();
                        if (shoppingGroupFront != null && shoppingGroupFront.size() > 0) {

                            for (int b = 0; b < shoppingGroupFront.size(); b++) {

                                CartShoppingGroupFrontBean cartShoppingGroupFrontBean = shoppingGroupFront.get(b);
                                /*------------------------满减title-------------------------*/
                                if (!TextUtils.isEmpty(cartShoppingGroupFrontBean.getTitle())) {
                                    CartItemBean cartShoppingGroupTitle = new CartItemBean();
                                    cartShoppingGroupTitle.setTitle(cartShoppingGroupFrontBean.getTitle());
                                    cartShoppingGroupTitle.setType(cartShoppingGroupFrontBean.getType());
                                    cartShoppingGroupTitle.setTitleUrlText(cartShoppingGroupFrontBean.getTitleUrlText());
                                    cartShoppingGroupTitle.setTitleUrl(cartShoppingGroupFrontBean.getTitleUrl());
                                    cartShoppingGroupTitle.setOrgId(cartShoppingGroupFrontBean.getOrgId());
                                    cartShoppingGroupTitle.setCompanyName(cartShoppingGroupFrontBean.getCompanyName());
                                    cartShoppingGroupTitle.setIsThirdCompany(cartShoppingGroupFrontBean.getIsThirdCompany());
                                    cartShoppingGroupTitle.setProductTotalNum(cartShoppingGroupFrontBean.getProductTotalNum());
                                    cartShoppingGroupTitle.setProductVarietyNum(cartShoppingGroupFrontBean.getProductVarietyNum());
                                    cartShoppingGroupTitle.setStatus(cartShoppingGroupFrontBean.getSelectStatus());
                                    cartShoppingGroupTitle.setTotalAmount(cartShoppingGroupFrontBean.getTotalAmount());
                                    cartShoppingGroupTitle.setItemType(CartItemBean.header_money_off);
                                    groupListNew.add(cartShoppingGroupTitle);
                                }

                                List<CartSortedNewBean> sorted = cartShoppingGroupFrontBean.getSorted();
                                /*------------------------商品-------------------------*/
                                for (int c = 0; c < sorted.size(); c++) {

                                    CartSortedNewBean groupBean = sorted.get(c);
                                    int itemType = groupBean.getItemType();
                                    CartItemBean item = groupBean.getItem();
                                    /*-------------------------itemType == 3表示套餐----------------------------*/
                                    if (itemType == 3) {
                                        /*-------------------------套餐title----------------------------*/
                                        CartItemBean itemTitle = new CartItemBean();
                                        itemTitle.setPrice(item.getPrice());
                                        itemTitle.setStatus(item.getStatus());
                                        itemTitle.setAmount(item.getAmount());
                                        itemTitle.setPackageId(item.getPackageId());
                                        itemTitle.setOrigPrice(item.getOrigPrice());
                                        itemTitle.setValid(item.getValid());
                                        itemTitle.setOrgId(cartShoppingGroupFrontBean.getOrgId());
                                        itemTitle.setItemType(CartItemBean.header_combo_head);
                                        groupListNew.add(itemTitle);

                                        goodsDao.updateItem(item.getPackageId(), item.getAmount(), true);
                                        /*-------------------------套餐商品----------------------------*/
                                        List<CartItemBean> subItemList = groupBean.getSubItemList();
                                        for (int e = 0; e < subItemList.size(); e++) {
                                            CartItemBean cartGroupBean_content = subItemList.get(e);
                                            cartGroupBean_content.setItemType(CartItemBean.content_combo);
                                            cartGroupBean_content.setPackageId(item.getPackageId());
                                            cartGroupBean_content.setFreightTips(item.getFreightTips());
                                            groupListNew.add(cartGroupBean_content);
                                        }
                                        /*-------------------------套餐尾部----------------------------*/
                                        CartItemBean cartGroupBean_end = new CartItemBean();
                                        cartGroupBean_end.setDiscount(item.getDiscount());
                                        cartGroupBean_end.setSubtotal(item.getSubtotal());
                                        cartGroupBean_end.setAmount(item.getAmount());
                                        cartGroupBean_end.setValid(item.getValid());
                                        cartGroupBean_end.setPrice(item.getPrice());
                                        cartGroupBean_end.setStatus(item.getStatus());
                                        cartGroupBean_end.setPackageId(item.getPackageId());
                                        cartGroupBean_end.setOrigPrice(item.getOrigPrice());
                                        cartGroupBean_end.setOrgId(cartShoppingGroupFrontBean.getOrgId());

                                        if (b == shoppingGroupFront.size() - 1 && c == sorted.size() - 1) {
                                            cartGroupBean_end.setItemType(CartItemBean.header_combo_end);
                                        } else {
                                            cartGroupBean_end.setItemType(CartItemBean.header_combo_lose_end);
                                        }
                                        groupListNew.add(cartGroupBean_end);
                                    } else {
                                        /*-------------------------正常item商品----------------------------*/
                                        //满减商品
                                        if (!TextUtils.isEmpty(cartShoppingGroupFrontBean.getTitle())) {
                                            item.setItemType(CartItemBean.content_preferential_combo);
                                        } else {
                                            item.setItemType(CartItemBean.content_normal);
                                        }
                                        //满减最后一个商品
                                        if (c == sorted.size() - 1 && !TextUtils.isEmpty(cartShoppingGroupFrontBean.getTitle())) {
                                            item.setItemType(CartItemBean.content_preferential_combo_end);
                                        }
                                        //shop下最后一个商品
                                        if (b == shoppingGroupFront.size() - 1 && c == sorted.size() - 1) {
                                            if (!TextUtils.isEmpty(cartShoppingGroupFrontBean.getTitle())) {
                                                item.setItemType(CartItemBean.content_preferential_end);
                                            } else {
                                                item.setItemType(CartItemBean.content_end);
                                            }
                                        }

                                        groupListNew.add(item);
                                        shopItemList.add(item);

                                        goodsDao.updateItem(item.getSkuId(), item.getAmount(), false);
                                    }
                                    if (item.getSkuStatus() == 2 || item.getSkuStatus() == 4 || item.getSkuStatus() == 91 || item.getSkuStatus() == 92 || item.getValid() == 0) {
                                        continue;
                                    } else {
                                        normalSize++;
                                    }
                                    if (item.getStatus() != 1) {
                                        isCheck = false;
                                    }
                                }
                            }
                        }
                        if (shopTitle != null) {
                            shopTitle.setShopItemList(shopItemList);
                        }
                    }

                }
                /*------------------------商城-------------------------*/

            }
            shopCheck.setChecked(isCheck && normalSize > 0);
            shopCheck.setEnabled(normalSize > 0);
            goodsDao.cache2Sp();
        } else {
            shopCheck.setChecked(false);
        }
    }

    private void setFailureList(CartShoppingGroupFrontBean novalidGroup, ArrayList<CartItemBean> groupListNew) {
        if (novalidGroup != null) {

            /*------------------------满减失效商品title-------------------------*/
            if (!TextUtils.isEmpty(novalidGroup.getTitle())) {
                CartItemBean cartShoppingGroupTitle = new CartItemBean();
                cartShoppingGroupTitle.setTitle(novalidGroup.getTitle());
                cartShoppingGroupTitle.setType(novalidGroup.getType());
                cartShoppingGroupTitle.setTitleUrlText(novalidGroup.getTitleUrlText());
                cartShoppingGroupTitle.setTitleUrl(novalidGroup.getTitleUrl());
                cartShoppingGroupTitle.setOrgId(novalidGroup.getOrgId());
                cartShoppingGroupTitle.setCompanyName(novalidGroup.getCompanyName());
                cartShoppingGroupTitle.setIsThirdCompany(novalidGroup.getIsThirdCompany());
                cartShoppingGroupTitle.setProductTotalNum(novalidGroup.getProductTotalNum());
                cartShoppingGroupTitle.setProductVarietyNum(novalidGroup.getProductVarietyNum());
                cartShoppingGroupTitle.setStatus(novalidGroup.getSelectStatus());
                cartShoppingGroupTitle.setTotalAmount(novalidGroup.getTotalAmount());
                cartShoppingGroupTitle.setItemType(CartItemBean.header_lose_efficacy);
                groupListNew.add(cartShoppingGroupTitle);
            }

            List<CartSortedNewBean> sorted = novalidGroup.getSorted();
            /*------------------------失效商品-------------------------*/
            for (int c = 0; c < sorted.size(); c++) {

                CartSortedNewBean groupBean = sorted.get(c);
                int itemType = groupBean.getItemType();
                CartItemBean item = groupBean.getItem();
                /*-------------------------itemType == 3表示套餐----------------------------*/
                if (itemType == 3) {
                    /*-------------------------套餐title----------------------------*/
                    CartItemBean itemTitle = new CartItemBean();
                    itemTitle.setPrice(item.getPrice());
                    itemTitle.setStatus(item.getStatus());
                    itemTitle.setAmount(item.getAmount());
                    itemTitle.setPackageId(item.getPackageId());
                    itemTitle.setOrigPrice(item.getOrigPrice());
                    itemTitle.setValid(item.getValid());
                    itemTitle.setOrgId(novalidGroup.getOrgId());
                    itemTitle.setItemType(CartItemBean.header_lose_combo_head);
                    groupListNew.add(itemTitle);

                    goodsDao.updateItem(item.getPackageId(), item.getAmount(), true);
                    /*-------------------------套餐商品----------------------------*/
                    List<CartItemBean> subItemList = groupBean.getSubItemList();
                    for (int e = 0; e < subItemList.size(); e++) {
                        CartItemBean cartGroupBean_content = subItemList.get(e);
                        cartGroupBean_content.setItemType(CartItemBean.content_combo);
                        cartGroupBean_content.setPackageId(item.getPackageId());
                        groupListNew.add(cartGroupBean_content);
                    }

                } else {
                    /*-------------------------item商品----------------------------*/
                    item.setItemType(CartItemBean.content_commodity);
                    //失效最后一个商品
                    if (c == sorted.size() - 1 && !TextUtils.isEmpty(novalidGroup.getTitle())) {
                        item.setItemType(CartItemBean.content_invalid_commodity_end);
                    }
                    groupListNew.add(item);

                    goodsDao.updateItem(item.getSkuId(), item.getAmount(), false);
                }
            }
        }
    }

    private void setRefreshing() {
        if (cartListLv != null) {
            cartListLv.setRefreshing(false);
        }
    }

    /**
     * 购物车顶部活动图片
     *
     * @param activity 购物车顶部title Bean
     */
    private void bindData(final CartActivityBean activity) {
        if (activity == null) {
            ivCartAction.setVisibility(View.GONE);
            return;
        }
        ivCartAction.setData(activity);
    }

    /**
     * 购物车顶部活动图片
     *
     * @param notify 购物车顶部title Bean
     */
    private void setNotifyData(final CartActivityBean notify) {
        if (notify == null) {
            ivCartNotice.setVisibility(View.GONE);
            return;
        }
        ivCartNotice.setData(notify);
    }

    /*
     * 全选按钮点击事件
     * */
    private class onClickListener implements View.OnClickListener {

        private CheckBox cb;

        private onClickListener(CheckBox cb) {
            this.cb = cb;
        }

        @Override
        public void onClick(View v) {
            if (v.getId() == R.id.shop_check) {
                if (flCartDiscount.getVisibility() == View.VISIBLE) {
                    mCartDiscountUtils.dismissDiscountView();
                }
                checkSelectedAll(cb.isChecked());
                selectOrCancelAllItem(cb.isChecked());
            }
        }
    }

    private void selectOrCancelAllItem(boolean isChecked) {
        selectOrCancelAllItem(isChecked, "", "");
    }

    /**
     * 全选or全取消
     *
     * @param isChecked 是否全部选中
     *                  购物车取消选择所有商品 url => "cancelAllItem"
     *                  购物车选择所有商品 url => "selectAllItem"
     */
    private void selectOrCancelAllItem(final boolean isChecked, String id, String isThirdCompany) {
        showProgress();
        String netUrl;
        //不要在初始化的时候取值，因切换账户，所以id要随用随取
        merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(id)) {
            params.put("orgId", id);
        }
        if (!TextUtils.isEmpty(isThirdCompany)) {
            params.put("isThirdCompany", isThirdCompany);
        }

        if (isChecked) {
            netUrl = AppNetConfig.SELECT_ALL_ITEM;
        } else {
            netUrl = AppNetConfig.CANCEL_ALL_ITEM;
        }
        HttpManager.getInstance().post(netUrl, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (obj != null && obj.isSuccess()) {
                    if (isChecked) {
                        dismissProgress();
                        handler.sendMessage(handler.obtainMessage(WHAT_ALLPRICE_SELECTED, ((double) 0)));
                        handler.sendMessage(handler.obtainMessage(WHAT_FAVORABLEMONEY_SELECTED, ((double) 0)));
                        handler.sendMessage(handler.obtainMessage(WHAT_COUPON_REDUCE_MONEY, ((double) 0)));
                        handler.sendMessage(handler.obtainMessage(14, true));
                    } else {
                        handler.sendMessage(handler.obtainMessage(14, true));
                    }
                } else {
                    checkSelectedNo(isChecked);

                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                checkSelectedNo(isChecked);

            }
        });
    }

    /*
     * 全取消-全选
     * */
    private void checkSelectedAll(boolean isCheck) {
        if (null != groupList) {
            isCheckSelected.clear();
            ShopCheckAdapter.getIsCheckSelected().clear();
            if (groupList.size() > 0) {
                for (int i = 0; i < groupList.size(); i++) {
                    CartItemBean groupBean = groupList.get(i);

                    if (groupBean.getItemType() == CartItemBean.header_combo_head
                            || groupBean.getItemType() == CartItemBean.content_normal
                            || groupBean.getItemType() == CartItemBean.content_preferential_combo
                            || groupBean.getItemType() == CartItemBean.content_end
                            || groupBean.getItemType() == CartItemBean.content_preferential_end
                            || groupBean.getItemType() == CartItemBean.content_preferential_combo_end
                            || groupBean.getItemType() == CartItemBean.header_shop_head
                            || groupBean.getItemType() == CartItemBean.content_commodity
                            || groupBean.getItemType() == CartItemBean.header_proprietary_head
                            || groupBean.getItemType() == CartItemBean.header_lose_combo_head
                            || groupBean.getItemType() == CartItemBean.content_invalid_commodity_end) {

                        if (groupBean.getSkuStatus() != 2 && groupBean.getSkuStatus() != 4 && groupBean.getSkuStatus() != 91 && groupBean.getSkuStatus() != 92 && groupBean.getValid() != 0) {
                            if (isCheck) {
                                groupBean.setStatus(0);
                            } else {
                                if (groupBean.getStatus() == 1) {
                                    isCheckSelected.add(i);
                                }
                                groupBean.setStatus(1);
                                ShopCheckAdapter.getIsCheckSelected().add(i);
                            }
                        }
                    }
                }
            }
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        }
    }

    /**
     * 还原为原来状态
     */
    private void checkSelectedNo(boolean isCheck) {
        if (null != groupList) {
            ShopCheckAdapter.getIsCheckSelected().clear();
            if (groupList.size() > 0) {
                for (int i = 0; i < groupList.size(); i++) {
                    CartItemBean groupBean = groupList.get(i);
                    if (groupBean.getItemType() == CartItemBean.header_combo_head
                            || groupBean.getItemType() == CartItemBean.content_normal
                            || groupBean.getItemType() == CartItemBean.content_preferential_combo
                            || groupBean.getItemType() == CartItemBean.content_end
                            || groupBean.getItemType() == CartItemBean.content_preferential_end
                            || groupBean.getItemType() == CartItemBean.content_preferential_combo_end
                            || groupBean.getItemType() == CartItemBean.header_shop_head
                            || groupBean.getItemType() == CartItemBean.content_commodity
                            || groupBean.getItemType() == CartItemBean.header_proprietary_head
                            || groupBean.getItemType() == CartItemBean.header_lose_combo_head
                            || groupBean.getItemType() == CartItemBean.content_invalid_commodity_end) {
                        if (groupBean.getSkuStatus() != 2 && groupBean.getSkuStatus() != 4 && groupBean.getSkuStatus() != 91 && groupBean.getSkuStatus() != 92 && groupBean.getValid() != 0) {
                            if (isCheck) {
                                groupBean.setStatus(1);
                                ShopCheckAdapter.getIsCheckSelected().add(i);
                            } else {
                                groupBean.setStatus(0);
                                if (isCheckSelected.contains(i)) {
                                    groupBean.setStatus(1);
                                }
                            }
                        }
                    }
                }
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
            }
        }
    }

    /**
     * 收藏
     * url => "product/attention"
     * skuId => 商品id
     *
     * @param pos 当前item索引
     */
    private void setCollect(final int pos) {
        if (cartListLv == null || pos == -1) {
            return;
        }
        final String collect = "收藏成功";
        merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);//商户ID
        params.put("skuId", String.valueOf(pos));
        HttpManager.getInstance().post(AppNetConfig.COLLECT, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {
                        ToastUtils.showShort(collect);
                    }
                }
            }

        });
    }

    @OnClick({R.id.cart_bt, R.id.title_right, R.id.iv_ad_suspension, R.id.rtv_over_weight_look, R.id.cart_discount_detail, R.id.tv_platform_coupon})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.cart_bt:
                if (flCartDiscount.getVisibility() == View.VISIBLE) {
                    mCartDiscountUtils.dismissDiscountView();
                }
                if (cartBt.getText().equals(CLOSE_AN_ACCOUNT) || cartBt.getText().equals(CLOSE_AN_ACCOUNT_COUPON)) {
                    if (adapter.isCheckSelected()) {
                        if (popWindowPackage == null) {
                            popWindowPackage = new ShowFreightPopWindow(getContext());
                            popWindowPackage
                                    .setContent(cartBean)
                                    .setOutsideTouchable(false)
                                    .setOnButtonClickListener((notSubmitOrderOrgIds) -> {
                                        goCheckOut(notSubmitOrderOrgIds);
                                        popWindowPackage.dismiss();
                                    });
                        }
                        if (canSettle == 2 || canSettle == 3) {
                            popWindowPackage.show(cartBt);
                        } else {
                            goCheckOut("");
                        }
                    } else {
                        UiUtils.toast("您还没有选择商品哦");
                    }
                } else if (cartBt.getText().equals(DELETE)) {
                    deleteOrCheckOutShop();
                }
                break;
            case R.id.title_right:
                if (titleRight.getText().equals(EDIT)) {
                    adapter.setFinishOrEdit(true);
//                    cartLl1.setEnabled(true);
                    if (adapter != null) {
                        adapter.setCheckBoxVisibility(View.GONE);
                    }
//                    cancelAll();
                    titleRight.setText("完成");
                    cartBt.setEnabled(true);
                    cartBt.setText("删除");
                    cartRl2.setVisibility(View.GONE);
                    cartTvEdit.setVisibility(View.VISIBLE);
                    cartBt.setBackgroundResource(R.drawable.bg_cart_btn_delete);
                    cartListLv.setRefreshEnable(false);
                } else if (titleRight.getText().equals(ACCOMPLISH)) {
                    adapter.setFinishOrEdit(false);
//                    cartLl1.setEnabled(false);
                    if (adapter != null) {
                        adapter.setCheckBoxVisibility(View.VISIBLE);
                    }
                    handler.sendMessage(handler.obtainMessage(14, true));
                    titleRight.setText("编辑");
                    cartRl2.setVisibility(View.VISIBLE);
                    cartTvEdit.setVisibility(View.GONE);
                    judgeDifferenceAmount(differenceAmount);
                    cartListLv.setRefreshEnable(true);
                }
                break;
            case R.id.iv_ad_suspension:
                getCoupon();
                break;
            case R.id.rtv_over_weight_look://运费超重提示点击查看
                RoutersUtils.open("ybmpage://freightoverweightgoods");
                break;
            case R.id.cart_discount_detail:
                showDiscountDialog();
                break;
            case R.id.tv_platform_coupon://右上角平台券
                ShowBottomCartCouponDialog couponDialog = new ShowBottomCartCouponDialog();
                couponDialog.setType(QUERY_COUPON_TYPE_PLATFORM);
                couponDialog.show(view);
                break;
        }
    }

    //获取首页天降礼包
    private void getCoupon() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.HOME_COUPON).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<AdBagListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<AdBagListBean> obj, AdBagListBean adDataBeans) {
                if (titleTv == null) {
                    return;
                }

                if (obj != null && obj.isSuccess()) {

                    if (adDataBeans != null && adDataBeans.bagList != null && adDataBeans.bagList.size() > 0) {
                        AdDialog2.showDialog(adDataBeans);
                    }
                }
            }
        });
    }


    /**
     * @param notSubmitOrderOrgIds 不能提单的公司，ext: xyy0001,xyy002,xyy003
     */
    private void goCheckOut(String notSubmitOrderOrgIds) {
        cartBt.setEnabled(false);
        showProgress();
        //把商品id和商户id传递到服务器
        //不要在初始化的时候取值，因切换账户，所以id要随用随取
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(notSubmitOrderOrgIds)) {
            params.put("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
            ToastUtils.showLong("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化");
        }
        HttpManager.getInstance().post(AppNetConfig.ORDER_V1_PRESETTLE, params, new BaseResponse<SettleBean>() {

            @Override
            public void onSuccess(String content, BaseBean<SettleBean> obj, SettleBean settleBean) {

                dismissProgress();
                if (cartBt == null) {
                    return;
                }
                cartBt.setEnabled(true);
                if (null != obj) {
                    if (obj.isSuccess() && settleBean != null) {
                        Intent checkOutIntent = new Intent(getNotNullActivity(), PaymentActivity.class);
                        checkOutIntent.putExtra("tranNo", settleBean.tranNo);
                        if (!TextUtils.isEmpty(notSubmitOrderOrgIds)) {
                            checkOutIntent.putExtra("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
                        }
                        startActivity(checkOutIntent);
                    }
                }

            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if (cartBt == null) {
                    return;
                }
                cartBt.setEnabled(true);
            }
        });
    }

    /**
     * 删除单个商品
     *
     * @param i      当前item索引
     * @param isFlag 删除单个商品或者是加减商品减为0删除
     */
    private void singleDelete(int i, boolean isFlag) {
        if (groupList == null || groupList.size() <= i || i < 0) {
            ToastUtils.showShort(emptyStr);
        } else {
            String[] str = new String[2];
            List<String> productIds = new ArrayList<>();
            int packageId;
            int cartId;
            CartItemBean cartGroupBean = groupList.get(i);
            //套餐item
            if (cartGroupBean.getItemType() == CartItemBean.content_combo
                    || cartGroupBean.getItemType() == CartItemBean.header_combo_head
                    || cartGroupBean.getItemType() == CartItemBean.header_lose_combo_head) {
                packageId = cartGroupBean.getPackageId();
                if (packageId > 0) {
                    productIds.add(HandlerGoodsDao.getInstance().getId(cartGroupBean.getPackageId(), true));
                    str[0] = packageId + "";
                }
            } else {
                //正常商品
                cartId = cartGroupBean.getId();
                if (cartId > 0) {
                    productIds.add(HandlerGoodsDao.getInstance().getId(cartGroupBean.getSkuId(), false));
                    str[1] = cartId + "";
                }
            }
            if (isFlag) {
                showDialogDelete(str, productIds);
            } else {
                removeProductItem(str, productIds);
            }
        }
    }

    /*
     * 删除
     * */
    private void deleteOrCheckOutShop() {

        String[] str = new String[2];
        List<String> productIds = new ArrayList<>();
        StringBuilder sbCartId = new StringBuilder();
        StringBuilder itemType = new StringBuilder();
        int cartId;
        int packageId;

        if (groupList != null && groupList.size() > 0) {
            for (int i = 0; i < groupList.size(); i++) {

                CartItemBean groupBean = groupList.get(i);

                if (groupBean.getStatus() == 1) {
                    if (groupBean.getItemType() == CartItemBean.header_combo_head || groupBean.getItemType() == CartItemBean.header_lose_combo_head) {
                        packageId = groupBean.getPackageId();
                        if (packageId > 0) {
                            productIds.add(HandlerGoodsDao.getInstance().getId(groupBean.getPackageId(), true));
                            itemType.append(groupBean.getPackageId());
                            itemType.append(",");
                        }
                    } else {
                        cartId = groupBean.getId();
                        if (cartId > 0) {
                            productIds.add(HandlerGoodsDao.getInstance().getId(groupBean.getSkuId(), false));
                            sbCartId.append(cartId);
                            sbCartId.append(",");
                        }
                    }
                }

            }
        }

        if (itemType.length() > 0) {
            itemType.deleteCharAt(itemType.length() - 1);
        }
        if (sbCartId.length() > 0) {
            sbCartId.deleteCharAt(sbCartId.length() - 1);
        }

        if (itemType.toString().length() > 0 || sbCartId.toString().length() > 0) {
            str[0] = itemType.toString();
            str[1] = sbCartId.toString();
        }

        if (TextUtils.isEmpty(str[0]) && TextUtils.isEmpty(str[1])) {
            ToastUtils.showShort(emptyStr);
        } else {
            showDialogDelete(str, productIds);
        }
    }

    /**
     * 删除失效商品
     *
     * @param position 索引，从下标为position开始以下全部删除
     */
    public void deleteLoseEfficacy(int position) {

        String[] str = new String[2];
        List<String> productIds = new ArrayList<>();
        StringBuilder sbCartId = new StringBuilder();
        StringBuilder itemType = new StringBuilder();
        int cartId;
        int packageId;

        for (int i = position; i < groupList.size(); i++) {
            CartItemBean groupBean = groupList.get(i);

            if (groupBean.getItemType() == CartItemBean.header_combo_head
                    || groupBean.getItemType() == CartItemBean.header_lose_combo_head
            ) {
                packageId = groupBean.getPackageId();
                if (packageId > 0) {
                    productIds.add(HandlerGoodsDao.getInstance().getId(groupBean.getPackageId(), true));
                    itemType.append(groupBean.getPackageId());
                    itemType.append(",");
                }
            } else if (groupBean.getItemType() == CartItemBean.content_normal
                    || groupBean.getItemType() == CartItemBean.content_preferential_combo
                    || groupBean.getItemType() == CartItemBean.content_end
                    || groupBean.getItemType() == CartItemBean.content_preferential_end
                    || groupBean.getItemType() == CartItemBean.content_preferential_combo_end
                    || groupBean.getItemType() == CartItemBean.content_commodity
                    || groupBean.getItemType() == CartItemBean.content_invalid_commodity_end) {
                cartId = groupBean.getId();
                if (cartId > 0) {
                    productIds.add(HandlerGoodsDao.getInstance().getId(groupBean.getSkuId(), false));
                    sbCartId.append(cartId);
                    sbCartId.append(",");
                }
            }
        }
        if (itemType.length() > 0) {
            itemType.deleteCharAt(itemType.length() - 1);
        }
        if (sbCartId.length() > 0) {
            sbCartId.deleteCharAt(sbCartId.length() - 1);
        }

        if (itemType.toString().length() > 0 || sbCartId.toString().length() > 0) {
            str[0] = itemType.toString();
            str[1] = sbCartId.toString();
        }

        if (TextUtils.isEmpty(str[0]) && TextUtils.isEmpty(str[1])) {
            ToastUtils.showShort(emptyStr);
        } else {
            showDialogDelete(str, productIds);
        }
    }

    /**
     * 弹出对话框询问用户是否删除被选中的商品
     * url => "/batchRemoveProductFromCart"
     * packageIds =>   套餐id
     *
     * @param ids      套餐id和普通商品id的数组
     * @param delShopP 套餐id和普通商品id的集合
     */
    private void showDialogDelete(String[] ids, final List<String> delShopP) {
        final String packageIds = ids[0];
        final String cardIds = ids[1];

        String titleStr = "提示";
        String messageStr = !TextUtils.isEmpty(packageIds) && TextUtils.isEmpty(cardIds)
                ? "将删除整个套餐，是否删除?" : "你确定删除该商品吗?";
        String cancelStr = "取消";
        String confirmStr = "确定";

        final AlertDialogEx alert = new AlertDialogEx(getNotNullActivity());
        alert.setTitle(titleStr);
        alert.setMessage(messageStr);
        alert.setCancelButton(cancelStr, null);
        alert.setConfirmButton(confirmStr, (AlertDialogEx.OnClickListener) (dialog, button) -> {

            //删除数据上传到服务器
            merchantid = SpUtil.getMerchantid();
            RequestParams params = new RequestParams();
            params.put("merchantId", merchantid);
            if (!TextUtils.isEmpty(cardIds)) {
                params.put("ids", cardIds);
            }
            if (!TextUtils.isEmpty(packageIds)) {
                params.put("packageIds", packageIds);
            }
            showProgress();
            HttpManager.getInstance().post(AppNetConfig.CART_DELETE, params, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                    if (null != obj) {
                        if (obj.isSuccess()) {
                            goodsDao.deleteItems(delShopP);//批量删除数据
                            ShopCheckAdapter.getIsSelected().clear();
                            ShopCheckAdapter.getIsCheckSelected().clear();
                            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));
                            getMyCart();
                        }
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    dismissProgress();
                }
            });
        });
        alert.show();
    }

    /**
     * 购物车单个商品数量减为0-删除单个商品
     * url => "/batchRemoveProductFromCart"
     *
     * @param ids      套餐id数组
     * @param delShopP 数据库批量删除数据的集合
     */
    private void removeProductItem(String[] ids, final List<String> delShopP) {
        final String packageIds = ids[0];
        final String cardIds = ids[1];

        String titleStr = "提示";
        String messageStr = !TextUtils.isEmpty(packageIds) && TextUtils.isEmpty(cardIds)
                ? "将删除整个套餐，是否删除？" : "你确定删除该商品吗？";
        String cancelStr = "取消";
        String confirmStr = "确定";

        final AlertDialogEx alert = new AlertDialogEx(getNotNullActivity());
        alert.setTitle(titleStr);
        alert.setMessage(messageStr);
        alert.setCancelButton(cancelStr, null);
        alert.setConfirmButton(confirmStr, (AlertDialogEx.OnClickListener) (dialog, button) -> {
            merchantid = SpUtil.getMerchantid();
            RequestParams params = new RequestParams();
            params.put("merchantId", merchantid);
            if (!TextUtils.isEmpty(cardIds)) {
                params.put("ids", cardIds);
            }
            if (!TextUtils.isEmpty(packageIds)) {
                params.put("packageIds", packageIds);
            }
            showProgress();
            HttpManager.getInstance().post(AppNetConfig.CART_DELETE, params, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean emptyBean) {

                    if (null != obj) {
                        if (obj.isSuccess()) {
                            goodsDao.deleteItems(delShopP);//批量删除数据
                            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_BUY_PRODUCT));

                            getMyCart();
                        }
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    dismissProgress();
                }
            });
        });
        alert.show();

    }

    /**
     * 计算当前购物车已选中商品是否满足起送价
     * 已选中商品金额 > 起送价 返回-1
     * 已选中商品金额 < 起送价 返回差额
     * 已选中商品金额 = 起送价 返回0.00
     */
    private void judgeDifferenceAmount(double differenceAmount) {
        setCloseAnAccount();
//        if (differenceAmount > 0) {
//            setDifferenceAmount(differenceAmount);
//        } else {
//            setCloseAnAccount();
//        }
    }

    /**
     * 设置差额
     */
    private void setDifferenceAmount(double amount) {
        cartBt.setTextSize(13);
        cartBt.setText(String.format(getResources().getString(R.string.cart_difference_amount), UiUtils.transform(amount)));
        cartBt.setBackgroundResource(R.drawable.bg_cart_check_out_disable);
        cartBt.setEnabled(false);
    }

    private void setCloseAnAccount() {
        cartBt.setText(hasVouchersNotReceived == 1 ? CLOSE_AN_ACCOUNT_COUPON : CLOSE_AN_ACCOUNT);
        cartBt.setTextSize(16);
        cartBt.setBackgroundResource(R.drawable.bg_cart_check_out);
        cartBt.setEnabled(true);
    }

    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            //更改选中商品的总价格
            if (msg.what == WHAT_ALLPRICE_SELECTED) {
                if (cartLl2Tv2 == null) {
                    return;
                }
                double price = (double) msg.obj;
                String allPriceStr;
                if (price >= 0) {
                    allPriceStr = "合计：¥" + (price > 0 ? UiUtils.transform(price) : "0.0");
                    cartLl2Tv2.setText(allPriceStr);
                }
                //所有列表中的商品全部被选中，让全选按钮也被选中
            } else if (msg.what == WHAT_ALLBTN_SELECTED) {
                if (shopCheck == null) {
                    return;
                }
                shopCheck.setChecked((Boolean) msg.obj);
            } else if (msg.what == WHAT_CHECKNUB_SELECTED) {
                //改变品种被勾选数量
                if (cartLl2Tv1 == null) {
                    return;
                }
                String StrN = (String) msg.obj;
                String[] delShopIndex = StrN.split("#");
                if (delShopIndex.length == 2) {
                    String variety = delShopIndex[0];
                    String varietyN = delShopIndex[1];
                    String str = "共有" + variety + "种，已选" + varietyN + "种";
                    cartLl2Tv1.setText(str);
                }
            } else if (msg.what == WHAT_NEWDATA_SELECTED) {
                Boolean obj = (Boolean) msg.obj;
                if (obj) {
                    getMyCart();
                }
                //满减优惠金额
            } else if (msg.what == WHAT_FAVORABLEMONEY_SELECTED) {
                if (cartLl2Tv4 == null) {
                    return;
                }
                double rePrice = (double) msg.obj;
                String priceStr;
                if (rePrice >= 0) {
                    priceStr = "促销减：¥" + (rePrice > 0 ? UiUtils.transform(rePrice) : "0.0");
                    cartLl2Tv4.setText(priceStr);
                }
            } else if (msg.what == WHAT_COUPON_REDUCE_MONEY) {
                if (cartLl2Tv5 == null) {
                    return;
                }
                double rePrice = (double) msg.obj;
                String priceStr;
                if (rePrice >= 0) {
                    priceStr = "用券减：¥" + (rePrice > 0 ? UiUtils.transform(rePrice) : "0.0");
                    cartLl2Tv5.setText(priceStr);
                }
            }
        }
    };

    @Override
    protected void initTitle() {
        titleRight.setVisibility(View.VISIBLE);
        titleTv.setText("购物车");
    }

    /**
     * 其他页面跳转到购物车可以返回
     */
    public void setBackTitle(String name, final String action) {
        if (titleTv == null || titleRight == null) {
            this.name = name;
            this.action = action;
            return;
        }
        final String url = RoutersUtils.decodeRAWUrl(action);
        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(url)) {
            titleLeft.setVisibility(View.INVISIBLE);
            tvLeft.setOnClickListener(null);
        } else {
            titleLeft.setVisibility(View.VISIBLE);
            tvLeft.setText(name);
            tvLeft.setOnClickListener(v -> {
                try {
                    if (RoutersUtils.open(url)) {//点击后清空返回
                        cleanBackTitle();
                    }
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private void cleanBackTitle() {
        titleLeft.setVisibility(View.INVISIBLE);
        tvLeft.setOnClickListener(null);
    }

    @Override
    protected RequestParams getParams() {

        return new RequestParams();
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_cart;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        this.hidden = hidden;
        if (hidden) {
            if (cartListLv != null) {
                cartListLv.setRefreshEnable(false);
            }
        } else {
            if (adapter != null) {
                adapter.setFinishOrEdit(false);
            }
            initPage();
            getMyCart();
            setAdShow();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!hidden && !isFirst) {
            getMyCart();
        }
        isFirst = false;
        setAdShow();
    }

    private void setAdShow() {
        if (SpUtil.readInt("show_ad_collect_pop", 0) == 1) {
            adSuspension.setVisibility(View.GONE);
            return;
        }
        adSuspension.setVisibility(View.VISIBLE);
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_CHANG_CAR_NUMBER);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_POP);
        intentFilter.addAction(IntentCanst.ACTION_AD_COLLECT_HINT_POP);
        //intentFilter.addAction(IntentCanst.ACTION_CARTFRAGMENT_ADD_GOOD);
        LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_CHANG_CAR_NUMBER.equals(action)) {
                if (!hidden) {
                    getMyCart();
                }
            } else if (IntentCanst.ACTION_AD_COLLECT_POP.equals(intent.getAction())) {
                if (adSuspension != null) {
                    adSuspension.setVisibility(View.VISIBLE);
                }
            } else if (IntentCanst.ACTION_AD_COLLECT_HINT_POP.equals(intent.getAction())) {
                if (adSuspension != null) {
                    adSuspension.setVisibility(View.GONE);
                }
            } else if (IntentCanst.ACTION_CARTFRAGMENT_ADD_GOOD.equals(intent.getAction())) {//监听购物车加购
                if (!hidden) {
                    getMyCart();
                }
            }
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(getNotNullActivity().getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }

    /**
     * 显示凑单前的对话框
     */
    public void showBottomCouponDialog() {
        if (adapter != null) {
            adapter.showBottomCouponDialog();
        }
    }
}
