package com.ybmmarket20.home

import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.OftenBuyListAdapter
import com.ybmmarket20.bean.OftenBuyItem
import com.ybmmarket20.common.BaseFragment2
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.utils.analysis.updateFlowData
import com.ybmmarket20.viewmodel.OftenBuyNotifyViewModel
import com.ybmmarket20.viewmodel.OftenBuyViewModel
import kotlinx.android.synthetic.main.fragment_often_buy_filter.rvOftenBuy

/**
 * <AUTHOR>
 * @date 2022/12/2
 * @description
 */
class OftenBuyFilterFragment(
        private val mThirtyDays: String, private val mOrder: String,
        private val mOrderText: String) : BaseFragment2() {

    private val oftenBuyViewModel: OftenBuyViewModel by viewModels()
    private val oftenBuyNotifyViewModel: OftenBuyNotifyViewModel by activityViewModels()
    var mAdapter: OftenBuyListAdapter? = null
    var isUpdateAnalysis: Boolean = true

    override fun initData(content: String?) {
        initView()
        initObserver()
    }

    override fun getLayoutId(): Int = R.layout.fragment_often_buy_filter

    private fun initView() {
        rvOftenBuy.layoutManager = StaggeredGridLayoutManager(
                2,
                StaggeredGridLayoutManager.VERTICAL)
        mAdapter = OftenBuyListAdapter(
                oftenBuyViewModel.mList,
                JgTrackBean(
                        jgReferrer = "com.ybmmarket20.home.OftenBuyFilterFragment",
                        jgReferrerTitle = JGTrackManager.TrackMineFrequentPurchase.TITLE,
                        jgReferrerModule = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        pageId = JGTrackManager.TrackMineFrequentPurchase.PAGE_ID,
                        title = JGTrackManager.TrackMineFrequentPurchase.TITLE,
                        entrance = "我的-${JGTrackManager.TrackMineFrequentPurchase.TITLE}",
                        url = this.getFullClassName()))
        rvOftenBuy.adapter = mAdapter
        mAdapter?.setEnableLoadMore(true)
        mAdapter?.setOnLoadMoreListener {
            oftenBuyViewModel.getOftenBuyListData(
                    mThirtyDays,
                    mOrder)
        }
        mAdapter?.setOnItemClickListener { _, _, position ->
            val item = oftenBuyViewModel.mList[position]
            if (item is OftenBuyItem) {
                val keyWord = item.showName
                RoutersUtils.open("ybmpage://searchproduct?isOftenBuyFrom=1&masterStandardProductId=${item.masterStandardProductId ?: ""}&originalShowName=${item.originalShowName ?: ""}")
                XyyIoUtil.track(
                        "standardProductChart_click",
                        hashMapOf("standardProduct_id" to item.masterStandardProductId))

            }
        }
        mAdapter?.mProductClickTrackListener = { rowsBean, position,number ->
            var productTag = ""
            rowsBean.tags?.productTags?.let { tagList ->
                tagList.forEachIndexed { index, tagBean ->
                    if (index != tagList.size - 1) {
                        productTag += tagBean.text + "，"
                    } else {
                        productTag += tagBean.text
                    }
                }
            }

        }
        mAdapter?.resourceViewTrackListener = { productId, productName, productPrice, productTag, position ->

        }

        oftenBuyViewModel.getOftenBuyListData(
                mThirtyDays,
                mOrder)
    }

    private fun initObserver() {
        //常购清单列表
        oftenBuyViewModel.oftenBuyItemLiveData.observe(this) {
            dismissProgress()
            oftenBuyNotifyViewModel.notifyOftenData()
            mAdapter?.notifyDataChangedAfterLoadMore(true)
            if (it.isSuccess) {
                val data = it.data
                if (isUpdateAnalysis) {
                    updateFlowData(mFlowData, data.sptype, data.spid, data.sid)
                    flowDataPageCommoditySearch(mFlowData)
                    isUpdateAnalysis = false
                }
                mAdapter?.setOftenBuyFlowData(FlowData(data.sptype, data.spid, data.sid, "", "", null))
            }
        }
        //常购清单推荐商品列表
        oftenBuyViewModel.oftenBuyRecommendListLiveData.observe(this) {
            dismissProgress()
            oftenBuyNotifyViewModel.notifyOftenData()
            if (it.isSuccess) {
                val data = it.data
                mAdapter?.setOftenBuyRecommendFlowData(FlowData(data.spType?: data.sptype, data.spId?:data.spid, data.sid, data.nsid, "", null))
                AdapterUtils.addLocalTimeForRows(it.data.rows)
                // 请求并更新折后价
                AdapterUtils.getAfterDiscountPrice(
                    it.data.rows,
                    mAdapter!!
                )
                mAdapter?.notifyDataChangedAfterLoadMore(!oftenBuyViewModel.isOftenBuyRecommendListEnd)
            }
        }
    }

}