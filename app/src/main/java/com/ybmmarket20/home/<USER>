package com.ybmmarket20.home;

import static com.ybm.activity.ad.AdFragment.ACTION_ADBEAN;
import static com.ybm.activity.ad.AdFragment.ACTION_URL;
import static com.ybm.activity.ad.AdFragment.AD_SCM_STR;
import static com.ybm.activity.ad.AdFragment.AD_SPM_STR;
import static com.ybm.activity.ad.AdFragment.ActionUrlKey;
import static com.ybm.activity.ad.AdFragment.IMAGE_URL;
import static com.ybmmarket20.utils.analysis.ShopListAnalysisKt.shopListBottomTabClick;
import static com.ybmmarket20.view.DialImageView.DIAL_REQUEST_CODE;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.DrawableTypeRequest;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.GlideDrawableImageViewTarget;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.ViewTarget;
import com.github.mzule.activityrouter.annotation.Router;
import com.github.mzule.activityrouter.router.Routers;
import com.gyf.immersionbar.ImmersionBar;
import com.tbruyelle.rxpermissions2.Permission;
import com.xyy.canary.AppUpdate;
import com.ybm.activity.ad.AdFragment;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.IntentUtil;
import com.ybm.app.utils.MemoryManager;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybm.activity.ad.AdActivity;
import com.ybmmarket20.activity.LoginActivity;
import com.ybmmarket20.bean.AdDataBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartNumBean;
import com.ybmmarket20.bean.HomeAlertBean;
import com.ybmmarket20.bean.HomeAlertFlagBean;
import com.ybmmarket20.bean.HomeConfigBean;
import com.ybmmarket20.bean.MerchantInfo;
import com.ybmmarket20.bean.OrderStatusNumber;
import com.ybmmarket20.bean.cart.CartLayoutType;
import com.ybmmarket20.business.order.ui.MineOrderFragment;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.CountDownTimerHomeManager;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.PrivacyInitManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.TrackManager;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.ordertopmanager.OrderTopManager;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.dialog.listener.DismissOnBtnClickL;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.fragments.FindFragment;
import com.ybmmarket20.home.mine.MineFragment2;
import com.ybmmarket20.home.newpage.HomeSteadyLayoutFragmentV3;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.service.WakeNotificationService;
import com.ybmmarket20.utils.CommonUtil;
import com.ybmmarket20.utils.PushUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.YbmPushUtil;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.HeaderImgDialog;
import com.ybmmarket20.viewmodel.HomeAlertViewModel;
import com.ybmmarket20.viewmodel.MainRefreshTokenViewModel;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;
import com.ybmmarket20.xyyreport.page.cart.CartReport;
import com.ybmmarket20.xyyreport.page.mainFrequently.MainFrequentlyEvent;
import com.ybmmarket20.xyyreport.page.mine.MineReport;
import com.ybmmarket20.xyyreport.page.orderList.OrderlistConstant;
import com.ybmmarket20.xyyreport.spm.SpmUtil;
import com.ybmmarket20.xyyreport.spm.XyyReportActivity;
import com.ydmmarket.report.ReportManager;

import java.util.HashMap;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/*
 * 主页面
 * */
@Router({"http://ybm100.com/app/:tab", "xyy://ybm100.com/app", "main", "main/:tab", "maintab", "maintab/:tab/:sort", "main/:tab/:sort", "maintab/:tab/:sort/:name", "main/:routerPath"})
public class MainActivity extends MainAnalysisActivity {

    private static final String TAG = "MainActivity";
    @Bind(R.id.iv_ll_home)
    ImageView ivLlHome;
    @Bind(R.id.tv_ll_home)
    TextView tvLlHome;
    @Bind(R.id.ll_home)
    LinearLayout llHome;
//    @Bind(R.id.iv_sort)
//    ImageView ivSort;
//    @Bind(R.id.tv_sort)
//    TextView tvSort;
//    @Bind(R.id.ll_sort)
//    LinearLayout llSort;
    @Bind(R.id.ll_shopping_tv)
    TextView llShoppingTv;
    @Bind(R.id.iv_shopping)
    ImageView ivShopping;
    @Bind(R.id.tv_shopping)
    TextView tvShopping;
    @Bind(R.id.ll_shopping)
    LinearLayout llShopping;
    @Bind(R.id.ll_shopping_fl)
    FrameLayout llShoppingFl;
    @Bind(R.id.fl_bg)
    FrameLayout flBg;
    @Bind(R.id.iv_me)
    ImageView ivMe;
    @Bind(R.id.tv_me)
    TextView tvMe;
    @Bind(R.id.ll_me)
    LinearLayout llMe;
    @Bind(R.id.iv_order)
    ImageView ivOrder;
    @Bind(R.id.tv_order)
    TextView tvOrder;
    @Bind(R.id.ll_order)
    LinearLayout llOrder;
    @Bind(R.id.ll_order_tv)
    TextView llOrderTv;
    @Bind(R.id.iv_activity)
    ImageView ivActivity;
    @Bind(R.id.ll_activity)
    LinearLayout llActivity;
    @Bind(R.id.ll_root)
    FrameLayout llRoot;
    @Bind(R.id.fl_ad)
    FrameLayout flAd;
    @Bind(R.id.iv_discover)
    ImageView ivDiscover;
    @Bind(R.id.tv_discover)
    TextView tvDiscover;
    @Bind(R.id.ll_bottom_tab)
    LinearLayout ll_bottom_tab;
    @Bind(R.id.ll_discover)
    LinearLayout llDiscover;
    @Bind(R.id.iv_frequently)
    ImageView ivFrequently;
    @Bind(R.id.tv_frequently)
    TextView tvFrequently;
    @Bind(R.id.ll_frequently)
    LinearLayout llFrequently;

    @Bind(R.id.main_bottom_view_masking)
    View main_bottom_view_masking;
    @Bind(R.id.ll_to_top)
    LinearLayout llToTop;
    @Bind(R.id.iv_single_top)
    ImageView ivSingleTop;
    @Bind(R.id.iv_to_top)
    ImageView ivToTop;
    @Bind(R.id.vs_ping_an_mask)
    ViewStub vsPingAnMask;

    public static final int HOME_TYPE_STEADY = 0; //静态首页 对应新版首页
    public static final int HOME_TYPE_STEADY_V1 = 3; //静态首页 对应新版首页 newLayout
    public static final int HOME_TYPE_STEADY_V2 = 4; //静态首页 对应新版首页 newLayoutV2
    public static final int HOME_TYPE_CMS = 1;    //cms首页 对应new
    public static final int HOME_TYPE_OLD = 2;    //旧首页  对应old

    private FragmentTransaction ft;
    private boolean isCrash = false;
    public int position = -1;
    private static MainActivity mainActivity;
//    private HomeCmsFragment homeCmsFragment = null;
    //    private HomeSteadyLayoutFragment homeCmsFragment = null;
    private HomeSteadyLayoutFragmentV3 homeSteadyFragment = null;
    private HomeFragment homeFragment = null;
    private MainFrequentlyFragment mainFrequentlyFragment = null;
    private BrandFragment brandFragment = null;
    private CartFragmentV2 cartFragmentV2 = null;
    private CartFragmentV3 cartFragmentV3 = null;
    private MineFragment2 moreFragment = null;
    private KaMineFragment kaMineFragment = null;
    private MineOrderFragment mineOrderFragment = null;
    private Fragment mFindFragment = null;
    private HomeConfigBean bean;
    private boolean isDowning = false;
    //protected boolean needUpdate = false;
    private int retryCount = 0;
    private String name;
    private String action;
    private int sort = -1;
    private int tab = -1;
    private int currIndex = 0;
    public static boolean needCheckUpdate = true;
    private AppUpdateManagerV2 updateManagerV2;
    private boolean isShowVerificationAlert;

    // 是否显示 cms homefragment
//    private boolean showCms = false;
    private int homeType = HOME_TYPE_STEADY;
    private int homeSteadyType = HOME_TYPE_STEADY_V2;
    private int preHomeType = -1;
    private int preHomeSteadyType = -1;

    private boolean isToTop = true;
    private View maskLayout;

    private int startActivityCount;
    //订单列表埋点 jpspid
    private String jgspid = "";

    private MainRefreshTokenViewModel refreshTokenViewModel;

    private String mActionUrl;
    private String mImageUrl;
    private AdDataBean adDataBean;
    private GlideDrawable mAdDrawable;

    @Override
    protected void onResume() {
        super.onResume();
        try {
            GlobalViewModelStore.Companion.get().getGlobalViewModelStore().clear();
        } catch (Exception e) {
            e.printStackTrace();
        }
        ((YBMAppLike)getApplication()).mineSelectShop = false;
        Uri uri = getIntent().getData();
        if (uri != null)
            LogUtils.d("main resume：" + uri.toString() != null ? uri.toString() : "null");
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        //可能用户直接从搜索页退出app  所以进页面把这个通用属性去掉
        JGTrackManager.Companion.unRegisterSuperProperty(this,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgOperationInfo(null);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgSearchRowsBean(null);
        JGTrackManager.GlobalVariable.INSTANCE.setMJgSearchSomeField(null);
        initVoice();
        //注册全局的ViewModel
        GlobalViewModelStore.Companion.get().registerViewModelStore(getViewModelStore());
        Intent dataIntent = getIntent();
        Bundle dataBundle = dataIntent.getExtras();
        if (dataBundle != null) {
            String adUrl = dataBundle.getString(ActionUrlKey);
            if (!StringUtil.isEmpty(adUrl)) {
                RoutersUtils.open(adUrl);
            }

            try {
                String changeCartStyleStr = dataBundle.getString(IntentCanst.ACTION_CART_STYLE);
                boolean changeCartStyle = Boolean.parseBoolean(changeCartStyleStr);
                if (changeCartStyle) {
                    getCartLayoutType();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        mainActivity = this;
        super.onCreate(savedInstanceState);
        isCrash = (savedInstanceState != null);
        initReceiver();
        getCancelAttention();
        openTab(true, dataIntent);
        startService(new Intent(this, WakeNotificationService.class));
        YbmPushUtil.bindPushtoken();
        initXyyIOExtra();
        ivLlHome.post(() -> checkUpdate());
        ImmersionBar.with(this).init();
        getUserCall();
        refreshTokenViewModel = new ViewModelProvider(this).get(MainRefreshTokenViewModel.class);
        refreshTokenViewModel.refreshToken();
        getOrderNumber();
        initAd(dataIntent.getBundleExtra("bundle"));
    }

    private void initAd(Bundle mAdBundle) {
        if (mAdBundle == null) return;
        mImageUrl = mAdBundle.getString(IMAGE_URL,"");
        mActionUrl = mAdBundle.getString(ACTION_URL,"");
        adDataBean = (AdDataBean) mAdBundle.getSerializable(ACTION_ADBEAN);
        String mAdSpmStr = mAdBundle.getString(AD_SPM_STR,"");
        String mAdScmStr = mAdBundle.getString(AD_SCM_STR,"");
        if (!mImageUrl.isEmpty() && !mActionUrl.isEmpty() && adDataBean != null){
            ImageHelper.with(this)
                    .load(mImageUrl)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate()
                    .dontTransform()
                    .into(new SimpleTarget<GlideDrawable>() {
                        @Override
                        public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                            mAdDrawable = resource;
                            //提前加载原始图片 再打开广告
                            AdFragment adFragment = new AdFragment();
                            Bundle mBundle = new Bundle();
                            mBundle.putSerializable(ACTION_ADBEAN, adDataBean);
                            mBundle.putString(IMAGE_URL, mImageUrl);
                            mBundle.putString(ACTION_URL, mActionUrl);
                            mBundle.putString(AD_SPM_STR, mAdSpmStr);
                            mBundle.putString(AD_SCM_STR, mAdScmStr);
                            adFragment.setArguments(mBundle);
                            getSupportFragmentManager().beginTransaction()
                                    .add(R.id.fl_ad, adFragment,"adTag").commit();
                            flAd.setVisibility(View.VISIBLE);
                        }
                    });
        }
    }

    public boolean isAdShowing(){
        return flAd.getVisibility() == View.VISIBLE;
    }

    private void initXyyIOExtra() {
        final String merchantId = SpUtil.getMerchantid();
        if (merchantId == null || merchantId.isEmpty()) {
            return;
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.BASE_INFO, params, new BaseResponse<MerchantInfo>() {

            @Override
            public void onSuccess(String content, BaseBean<MerchantInfo> obj, MerchantInfo data) {
                if (obj.isSuccess() && data != null) {

                    MerchantInfo.BaseInfo baseInfo = data.baseInfo;
                    if (baseInfo != null) {
                        XyyIoUtil.identify(merchantId, baseInfo);

                        //这里重新赋值缓存是为了兼容极光埋点未接入以前的用户 登录过一直覆盖安装就不会再设置 导致没数据，所以这里兼容一下 多设置一下
                        SpUtil.setMerchantid(merchantId);
                        JGTrackManager.Companion.setSuperProperty(YBMAppLike.getAppContext(),
                                JGTrackManager.Common.FIELD_MERCHANT_NAME, baseInfo.realName);
                        SpUtil.setMerchantBaseInfo(baseInfo);
                        PushUtil.setTags(data.tagList);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }


    /**
     * 是否显示认证的弹框
     */
    private void getIsShowVerificationAlert() {
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.ALERT_FLAG, params, new BaseResponse<HomeAlertFlagBean>() {

            @Override
            public void onSuccess(String content, BaseBean<HomeAlertFlagBean> obj, HomeAlertFlagBean homeAlertFlagBean) {
                super.onSuccess(content, obj, homeAlertFlagBean);
                if (homeAlertFlagBean == null) {
                    return;
                }
                // 1弹   3不弹
                int authAlertFlag = homeAlertFlagBean.auth_alertFlag;
                boolean changeAlertFlag = homeAlertFlagBean.change_alertFlag;
                if (authAlertFlag == 1) {//弹
                    new HeaderImgDialog.Builder(getSupportFragmentManager())
                            .setHeadImg(R.drawable.certification_pop)
                            .setTitle("企业被委托人身份信息认证")
                            .setTitleTextColos(R.color.color_292933)
                            .setDesc("为了确保配送药品的安全，邀请您进行企业被委托人身份信息认证。")
                            .setDescTextColos(R.color.color_676773)
                            .setBtCancelTxt("暂不认证")
                            .setBtConfirmTxt("去认证")
                            .setConfirmClickListener(new DismissOnBtnClickL() {
                                @Override
                                public void onBtnClick() {
                                    CommonUtil.INSTANCE.skipCertification();
                                }
                            })
                            .show();
                } else if (changeAlertFlag) {//弹委托人变更弹框
                    new AlertDialogEx(MainActivity.this)
                            .setMessage("企业被委托人信息已变更，为了确保配送药品的安全，邀请您重新进行企业被委托人身份信息认证。")
                            .setCancelButton("暂不认证", (AlertDialogEx.OnClickListener) (dialog, button) -> dialog.dismiss())
                            .setCancelButtonTextColor(UiUtils.getColor(R.color.color_9494A6))
                            .setConfirmButton("去认证", (AlertDialogEx.OnClickListener) (dialog, button) -> CommonUtil.INSTANCE.skipCertification())
                            .show();
                }
                isShowVerificationAlert = false;
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    /**
     * 设置底部导航栏蒙版是否显示
     *
     * @param visibility
     */
    public void setBottomNavigationMasking(int visibility) {
        main_bottom_view_masking.setVisibility(visibility);
    }

    public void setBottomNavigationMaskingClickListener(View.OnClickListener l) {
        main_bottom_view_masking.setOnClickListener(l);
    }


    @Override
    public void initData() {
//        homeType = SpUtil.readInt(ConstantData.HOME_TYPE, HOME_TYPE_CMS);
        if (!isLogin()) {
            //未登录
            gotoAtivity(LoginActivity.class, null);
            return;
        }
        //如果是登录，初始化用户信息
//        setSelect(0);
        XyyIoUtil.track(XyyIoUtil.ACTION_HOMEPAGE);
        //检验密码强度，仅首次
        checkPwdSafety();
        //购物车数量
        getShopNumber();
        //是的显示认证弹框
        getIsShowVerificationAlert();
        //购物车动画
//        ivSort.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                if (llShoppingTv != null) {
//                    llShoppingTv.getLocationInWindow(YBMAppLike.endLocation);// llShoppingRl是那个购物车
//                }
//            }
//        }, 400);
        checkSD();
//        showGuide();
        handleExternalRouter(null);
        ImmersionBar.with(this)
                .statusBarDarkFont(true);
    }

    /**
     * 2025.3.18 购物金二期需求 屏蔽平安蒙层
     */
//    private void handlePingAnMask() {
//        if (moreFragment != null) {
//            moreFragment.setOnPingAnClickListener(isShowPingAnMaskFlag -> {
//                try {
//                    maskLayout  = vsPingAnMask.inflate();
//                    maskLayout.setVisibility(View.GONE);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                ImageView ivPingAnMask = maskLayout.findViewById(R.id.ivPingAnMask);
//                if (isShowPingAnMaskFlag == 1 && !SpUtil.getPingAnIsTipV2()) {
//                    ivPingAnMask.setImageResource(R.drawable.mask_ping_an1);
//                    ivPingAnMask.setTag(R.drawable.mask_ping_an1);
//                    SpUtil.setUnPingAnIsTip(true);
//                    SpUtil.setPingAnIsTipV2(true);
//                } else if (isShowPingAnMaskFlag == 0 && !SpUtil.getUnPingAnIsTip()) {
//                    ivPingAnMask.setImageResource(R.drawable.mask_ping_an3);
//                    ivPingAnMask.setTag(R.drawable.mask_ping_an3);
//                    SpUtil.setUnPingAnIsTip(true);
//                } else return null;
//                maskLayout.setVisibility(View.VISIBLE);
//                maskLayout.setOnClickListener(v -> {
//                    if ((int)ivPingAnMask.getTag() == R.drawable.mask_ping_an3) {
//                        vsPingAnMask.setVisibility(View.GONE);
//                    } else if ((int)ivPingAnMask.getTag() == R.drawable.mask_ping_an1){
//                        ivPingAnMask.setImageResource(R.drawable.mask_ping_an2);
//                        ivPingAnMask.setTag(R.drawable.mask_ping_an2);
//                    } else if ((int)ivPingAnMask.getTag() == R.drawable.mask_ping_an2) {
//                        ivPingAnMask.setImageResource(R.drawable.mask_ping_an3);
//                        ivPingAnMask.setTag(R.drawable.mask_ping_an3);
//                    }
//                });
//                return null;
//            });
//        }
//    }

    private void handlePingAnMask() {
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        openTab(false, intent);
        handleExternalRouter(intent);
        handleUserCallIntent(intent);
    }

    public void finishAd() {
        AlphaAnimation hideAnimation = new AlphaAnimation(1f, 0f);
        hideAnimation.setDuration(1000);
        hideAnimation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                flAd.setVisibility(View.GONE);
                FragmentManager fragmentManager = getSupportFragmentManager();
                FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
                AdFragment fragment = (AdFragment) getSupportFragmentManager().findFragmentByTag("adTag");
                if (fragment != null) {
                    fragmentTransaction.remove(fragment);
                    fragmentTransaction.commit();
                }
                mAdDrawable = null;
                //广告关闭后 有广告弹窗就弹
                homeSteadyFragment.showAlert(null);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }
        });
        flAd.startAnimation(hideAnimation);
    }

    public GlideDrawable getAdDrawable() {
        return mAdDrawable;
    }

    /**
     * 处理用户召回
     * @param intent
     */
    private void handleUserCallIntent(Intent intent) {
        getUserCall();
    }

    private void openTab(boolean isCreate, Intent intent) {
        if (!isLogin()) {
            return;
        }
        String tabStr = "0";
        name = null;
        if (intent != null) {
            String comment = intent.getStringExtra("comment");
            tabStr = intent.getStringExtra("tab");
            name = intent.getStringExtra("name");
            action = intent.getStringExtra("id");
            String sortStr = intent.getStringExtra("sort");
            if (!TextUtils.isEmpty(sortStr)) {
                try {
                    sort = Integer.parseInt(sortStr);
                } catch (Exception e) {
                    sort = 0;
                }
            }
            if (!TextUtils.isEmpty(comment) && TextUtils.isEmpty(tabStr)) {
                tabStr = comment;
            }
        }
        int index = 0;
        try {
            index = Integer.parseInt(tabStr);
        } catch (Exception e) {

        }
        tab = index;
        if (isCreate) {//没有打开

            if (tab == 0) {
                return;
            }
            tvLlHome.postDelayed(new Runnable() {
                @Override
                public void run() {
                    setSelect(tab);
                }
            }, 300);
        } else {
            if (tab == 3){ //来自路由跳转 订单页全部tab下 自动刷新
                showOrderShop("0",true);
            }else {
                setSelect(tab);
            }
        }
    }

    /**
     * 此方法先暂时不用，用于提示用户密码强度
     */
    private void checkPwdSafety() {
        /*boolean already_mention = SpUtil.readBoolean("already_mention", false);
        if (!already_mention) {
            RequestParams params = new RequestParams();
            String merchantId = SpUtil.getMerchantid();
            params.put("merchantId", merchantId);
            HttpManager.getInstance().post(AppNetConfig.PASSWORD_VERIFY + "?merchantId=" + merchantId, params, new BaseResponse<Boolean>() {

                @Override
                public void onSuccess(String content, BaseBean<Boolean> obj, Boolean aBoolean) {
                    if (aBoolean != null && !aBoolean) {
                        //密码简单请及时修改
                        final AlertDialogEx alert = new AlertDialogEx(MainActivity.this);
                        alert.setTitle("密码安全提示");//确认下单
                        alert.setMessage("尊敬的药帮忙用户您好，您当前的密码过于简单，存在安全风险，请尽快修改");
                        alert.setCancelButton("取消", null);//取消下单
                        alert.setConfirmButton("去修改", new AlertDialogEx.OnClickListener() {
                            @Override
                            public void onClick(AlertDialogEx dialog, int button) {
                                RoutersUtils.open("ybmpage://alterpassword");
                            }
                        });
                        alert.show();

                        SpUtil.writeBoolean("already_mention", true);
                    }
                }

                public void onFailure(NetError error) {

                }
            });
        }*/
    }

    //购物车数量
    private void getShopNumber() {
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.CART_NUM, params, new BaseResponse<CartNumBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartNumBean> obj, CartNumBean cartNumBean) {

                if (null != obj && obj.isSuccess()) {
                    if (cartNumBean != null) {
                        getCartNumber(cartNumBean.num);
                    }
                }
            }
        });
    }

    private void getOrderNumber(){
        final String merchantId = SpUtil.getMerchantid();
        if (merchantId.isEmpty()) {
            return;
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.ORDER_FIND_NUM_GROUP_BY_STATUS, params, new BaseResponse<OrderStatusNumber>() {
            @Override
            public void onSuccess(String content, BaseBean<OrderStatusNumber> obj, OrderStatusNumber orderStatusNumber) {
                if (orderStatusNumber != null){
                    setOrderNum(orderStatusNumber.waitPayNum);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    /**
     * @param num 购物车数量
     */
    private void getCartNumber(int num) {
        if (num <= 0) {
            num = 0;
        }
        setShopNum(num);
        YBMAppLike.cartNum = num;
        LocalBroadcastManager.getInstance(getApplication()).sendBroadcast(new Intent(IntentCanst.CART_NUM_CHANGED));
    }

    /**
     * @param num 购物车数量
     */
    private void setShopNum(int num) {
        if (llShoppingTv != null) {
            if (num > 0) {
                llShoppingTv.setVisibility(View.VISIBLE);
                String numStr = "";
                if (num > 99) {
                    numStr = "99+";
                } else {
                    numStr = String.valueOf(num);
                }
                llShoppingTv.setText(numStr);
            } else {
                llShoppingTv.setVisibility(View.INVISIBLE);
            }
        }
    }

    /**
     * 订单未支付数小气泡
     * @param num
     */
    private void setOrderNum(int num) {
        if (llOrderTv != null) {
            if (num > 0) {
                llOrderTv.setVisibility(View.VISIBLE);
                llOrderTv.setText(String.valueOf(num));
            } else {
                llOrderTv.setVisibility(View.INVISIBLE);
            }
        }
    }

    //动态注册广播
    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_LOGOUT);
        intentFilter.addAction(IntentCanst.ACTION_ADDRESS);
        intentFilter.addAction(IntentCanst.ACTION_SHOPNUMBER);
        intentFilter.addAction(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE);
        intentFilter.addAction(BaseYBMApp.APPISBACKGROUND);
        intentFilter.addAction(BaseYBMApp.APPISFOREGROUND);
        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER);
        intentFilter.addAction(IntentCanst.ACTION_CART_TO_COUPON_RESULT);
        intentFilter.addAction(IntentCanst.ACTION_HIDE_ORDER_BUBBLE);

        LocalBroadcastManager.getInstance(MainActivity.this).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    private void switchUserForOrderList() {
        if (mineOrderFragment != null) {
            FragmentManager fm = getSupportFragmentManager();
            for (Fragment fragment : fm.getFragments()) {
                if (fragment instanceof MineOrderFragment) {
                    ft.remove(fragment);
                }
            }
            mineOrderFragment = null;
        }
    }

    // broadcast receiver
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            int cart_num = intent.getIntExtra(IntentCanst.ACTION_CART_NUM, 0);
            if (IntentCanst.ACTION_SHOPNUMBER.equals(action)) {
                if (cart_num > 0) {
                    getCartNumber(cart_num);
                } else {
                    getShopNumber();
                }
            }
            if (IntentCanst.ACTION_SWITCH_USER.equals(intent.getAction())) {
                switchUserForOrderList();
                isShowVerificationAlert = true;
                getCartLayoutType();
                OrderTopManager.INSTANCE.clear();
            }
            //前台进入后台
            if (BaseYBMApp.APPISBACKGROUND.equals(action)) {
                CountDownTimerHomeManager.setHomeStartTimer(System.currentTimeMillis());
            }
            //从后台进入前台
            if (BaseYBMApp.APPISFOREGROUND.equals(action)) {
                if (CountDownTimerHomeManager.showHomeSplash()) {
                    Intent ad_intent = new Intent(MainActivity.this, AdActivity.class);
                    startActivity(ad_intent);
                }
            }
            // 切换用户后或者首次登录，要检测用户的首页配置状态，是否开启cms
            if (IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE.equals(action)) {
                LogUtils.e("xyd + 切换用户");
                getCancelAttention();
                OrderTopManager.INSTANCE.clear();
            }
            //购物车底部弹框优惠券去凑单进入凑单页，凑单页关闭时广播action
            if (IntentCanst.ACTION_CART_TO_COUPON_RESULT.equals(action)) {
                //当前显示购物车
                // TODO: 2021/11/9
//                if (currIndex == 2 && cartFragment != null) {
//                    cartFragment.showBottomCouponDialog();
//                }
            }
            if (IntentCanst.ACTION_HIDE_ORDER_BUBBLE.equals(action)){
                llOrderTv.setVisibility(View.INVISIBLE);
            }
        }
    };

    // 获取购物车样式
    private void getCartLayoutType() {
        RequestParams requestParams = new RequestParams();
        requestParams.put("scene", "getCart");
        HttpManager.getInstance().post(AppNetConfig.GET_CART_LAYOUT_TYPE, requestParams, new BaseResponse<CartLayoutType>() {
            @Override
            public void onSuccess(String content, BaseBean<CartLayoutType> obj, CartLayoutType cartLayoutType) {
                if (cartLayoutType != null) {
                    //System.out.println("xyd cart type res" + cartLayoutType.getStyleTemplate());
                    SpUtil.writeInt(ConstantData.CART_TYPE, cartLayoutType.getStyleTemplate());
                }
            }
        });
    }

    // 获取是否开启cms
    private void getCancelAttention() {
        homeType = HOME_TYPE_STEADY;
        preHomeSteadyType = homeSteadyType;
        homeSteadyType = HOME_TYPE_STEADY_V2;
        setDiscoverDefaultText();
        setSelect(0);
//        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GET_LAYOUT_TYPE_V2).build();
//        // old使用原admin布局，new使用cms配置布局
//        HttpManager.getInstance().post(params, new BaseResponse<String>() {
//            @Override
//            public void onSuccess(String content, BaseBean<String> obj, String s) {
////                if ("newLayout".equalsIgnoreCase(s)) {
////                    homeType = HOME_TYPE_STEADY;
////                } else
//                dismissProgress();
////                if ("new".equalsIgnoreCase(s)) {
////                    homeType = HOME_TYPE_CMS;
////                } else if ("newLayoutV2".equalsIgnoreCase(s)) {
////                    homeType = HOME_TYPE_STEADY;
////                    preHomeSteadyType = homeSteadyType;
////                    homeSteadyType = HOME_TYPE_STEADY_V2;
////                } else if ("newLayout".equalsIgnoreCase(s)) {
////                    homeType = HOME_TYPE_STEADY;
////                    preHomeSteadyType = homeSteadyType;
////                    homeSteadyType = HOME_TYPE_STEADY_V1;
////                } else {
////                    homeType = HOME_TYPE_OLD;
////                }
////                if (homeType == HOME_TYPE_STEADY && preHomeSteadyType == homeSteadyType && homeSteadyFragment != null) {
////                    homeSteadyFragment.getHeaderData();
////                } else if (homeType == HOME_TYPE_CMS && homeCmsFragment != null) {
////                    homeCmsFragment.doRefresh();
////                }
////                preHomeType = homeType;
//                homeType = HOME_TYPE_STEADY;
//                preHomeSteadyType = homeSteadyType;
//                homeSteadyType = HOME_TYPE_STEADY_V2;
//                setDiscoverDefaultText();
//                setSelect(0);
//            }
//
//            @Override
//            public void onFailure(NetError error) {
//                super.onFailure(error);
//                dismissProgress();
//            }
//        });

    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_main;
    }

    @OnClick({R.id.ll_home,R.id.ll_frequently, R.id.ll_shopping, R.id.ll_me, R.id.ll_order, R.id.iv_activity, R.id.ll_discover, R.id.ll_to_top, R.id.iv_to_top, R.id.iv_single_top})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.ll_home:
                onBottomTabClick(view.getTag(), 0, currIndex);
                handleSelectTabPvData(0, currIndex);
                XyyIoUtil.track(XyyIoUtil.ACTION_HOMEPAGE);
                setSelect(0);
                trackClickTabComponentExposure();
                break;
            case R.id.ll_frequently:
                onBottomTabClick(view.getTag(), 1, currIndex);
                handleSelectTabPvData(1, currIndex);
                XyyIoUtil.track(XyyIoUtil.ACTION_FREQUENTLY);
                setSelect(1);
                trackClickTabComponentExposure();
                break;
//            case R.id.ll_sort:
//                setSelect(1);
//                break;
            case R.id.ll_shopping:
                onBottomTabClick(view.getTag(), 2, currIndex);
                handleSelectTabPvData(2, currIndex);
                setSelect(2);
                TrackManager.Companion.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_SHOPPING_CART_CLICK, new HashMap<>());
                break;
            case R.id.ll_me:
                onBottomTabClick(view.getTag(), 4, currIndex);
                handleSelectTabPvData(4, currIndex);
                setSelect(4);
                TrackManager.Companion.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_ME_CLICK, new HashMap<>());
                break;
            case R.id.ll_order:{
                jgspid = "4201";
                onBottomTabClick(view.getTag(), 3, currIndex);
                handleSelectTabPvData(3, currIndex);
                if (mineOrderFragment != null) {
                    mineOrderFragment.setMTargetTabIndex(mineOrderFragment.getVpCurrIndex());
                }
                setSelect(3);
                break;
            }
            case R.id.ll_discover:
                //showFindWindow();
                if (bean != null && !TextUtils.isEmpty(bean.bottom_second_button_link)) {
                    RoutersUtils.open(bean.bottom_second_button_link);
                    onBottomTabClick(view.getTag(), 0, -1);
                } else {
                    onBottomTabClick(view.getTag(), 5, currIndex);
                    handleSelectTabPvData(5, currIndex);
                    setSelect(5);
                }
                break;
            case R.id.iv_activity:
                resetTabs();
                position = -1;
                try {
                    String action = (String) view.getTag(R.id.tag_action);
                    RoutersUtils.open(action);
                } catch (Exception e) {

                }
                break;
            // 回到顶部
            case R.id.iv_to_top:
                if (homeSteadyFragment != null) {
                    onBottomTabTopClick();
                    setSelect(0);
                    homeSteadyFragment.toTop();
                    XyyIoUtil.track("action_Home_Top");
//                    llToTop.postDelayed(() -> {
//                        llToTop.setVisibility(View.GONE);
//                        llHome.setVisibility(View.VISIBLE);
//                    }, 500);
                    TrackManager.Companion.clickEventTrack(TrackManager.TrackHome.EVENT_ACION_TOP_CLICK, new HashMap<>());
                }
                break;

            case R.id.iv_single_top:
                setSelect(0);
                break;
        }
    }

//    private void handleHomeCMS(FragmentManager fm) {
//        if (homeFragment != null) {
//            ft.remove(homeFragment);
//            homeFragment = null;
//        }
//        if (homeSteadyFragment != null) {
//            ft.remove((BaseFragment) homeSteadyFragment);
//            homeSteadyFragment = null;
//        }
//        if (homeCmsFragment == null) {
//            homeCmsFragment = new HomeCmsFragment();
//            if (isCrash) {
//                for (Fragment fragment : fm.getFragments()) {
//                    if (fragment instanceof HomeCmsFragment) {
//                        ft.remove(fragment);
//                    }
//                }
//            }
//            ft.add(R.id.fl_container, homeCmsFragment, HomeCmsFragment.class.getSimpleName());
//        }
//        ft.show(homeCmsFragment);
//    }

    private void handleHomeSteady(FragmentManager fm) {

        if (homeSteadyFragment == null) {
            homeSteadyFragment = new HomeSteadyLayoutFragmentV3();
            if (isCrash) {
                for (Fragment fragment : fm.getFragments()) {
                    if (fragment instanceof HomeSteadyLayoutFragmentV3) {
                        ft.remove(fragment);
                    }
                }
            }
            ft.add(R.id.fl_container, (BaseFragment) homeSteadyFragment, homeSteadyFragment.getClass().getSimpleName());
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(IntentCanst.REFRESH_HOT_SEARCH));
        ft.show((BaseFragment) homeSteadyFragment);
    }

    @SuppressLint("RestrictedApi")
    private void setSelect(int i) {
        if (i == 0 && homeType != HOME_TYPE_CMS) {
            llHome.setVisibility(isToTop ? View.GONE : View.VISIBLE);
            llToTop.setVisibility(isToTop ? View.VISIBLE : View.GONE);
        } else if (homeType != HOME_TYPE_CMS) {
            llHome.setVisibility(View.VISIBLE);
            llToTop.setVisibility(View.GONE);
        }

//        if (position == i && i == 0 && homeType == HOME_TYPE_CMS) {
//            if (homeCmsFragment != null && homeCmsFragment.isVisible()) {
//                homeCmsFragment.doRefresh();
//                return;
//            }
//        }

        position = i;
        FragmentManager fm = getSupportFragmentManager();
        ft = fm.beginTransaction();
        hideFragments(ft);
        resetTabs();
        //        LogUtils.d(position+" iscrash:"+isCrash);
        switch (i) {
            case 0:
                if (isShowVerificationAlert) {
                    //是的显示认证弹框
                    getIsShowVerificationAlert();
                }
                handleHomeSteady(fm);
                setHome(true);
                break;
            case 1:
                selectFrequentlyFragment(fm);
                break;
            case 10:
            case 11:
            case 12:
            case 13:
                if (brandFragment == null) {
                    brandFragment = new BrandFragment();
                    if (isCrash) {
                        for (Fragment fragment : fm.getFragments()) {
                            if (fragment instanceof BrandFragment) {
                                ft.remove(fragment);
                            }
                        }
                    }
                    ft.add(R.id.fl_container, brandFragment, BrandFragment.class.getSimpleName());
                }
//                setSort(true);
                ft.show(brandFragment);
                if (i >= 10) {
                    brandFragment.selecTab(i, sort, name);
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS);
                break;
            case 2:
                selectCartFragment(fm);
                break;
            case 3: {
                if (mineOrderFragment == null) {
                    mineOrderFragment = new MineOrderFragment();
                    if (isCrash) {
                        for (Fragment fragment : fm.getFragments()) {
                            if (fragment instanceof KaMineFragment) {
                                ft.remove(fragment);
                            }
                        }
                    }
                    ft.add(R.id.fl_container, mineOrderFragment, MineOrderFragment.class.getSimpleName());
                } else {
                    mineOrderFragment.pvTrack();
                }
                setMineOrder(true);
                ft.show(mineOrderFragment);
//                XyyIoUtil.track(XyyIoUtil.ACTION_ALLDRUGS);
                break;
            }
            case 4:
                if (isKaUser) {
                    if (kaMineFragment == null) {
                        kaMineFragment = new KaMineFragment();
                        if (isCrash) {
                            for (Fragment fragment : fm.getFragments()) {
                                if (fragment instanceof KaMineFragment) {
                                    ft.remove(fragment);
                                }
                            }
                        }
                        ft.add(R.id.fl_container, kaMineFragment, KaMineFragment.class.getSimpleName());
                    }
                    setMe(true);
                    ft.show(kaMineFragment);
                    XyyIoUtil.track(XyyIoUtil.ACTION_ME);
                } else {
                    if (moreFragment == null) {
                        moreFragment = new MineFragment2();
                        if (!SpUtil.getPingAnIsTipV2() || !SpUtil.getUnPingAnIsTip()) {
                            handlePingAnMask();
                        }
                        if (isCrash) {
                            for (Fragment fragment : fm.getFragments()) {
                                if (fragment instanceof MineFragment2) {
                                    ft.remove(fragment);
                                }
                            }
                        }
                        ft.add(R.id.fl_container, moreFragment, MineFragment2.class.getSimpleName());
                    }
                    setMe(true);
                    ft.show(moreFragment);
                    MineReport.pvTrack(this);
                    moreFragment.handlePv();
                    SpmUtil.checkAnalysisContext(this, xyyReportActivity -> {
                        MainActivity.this.trackCommonTabComponentExposure(xyyReportActivity);
                        return null;
                    });
                    XyyIoUtil.track(XyyIoUtil.ACTION_ME);
                }

                break;
            case 5:
                //  newlayoutV2  ->  店铺列表 ShopListFragment
                //  newlayout    ->  常购清单 HomeSteadyLayoutOftenBuyFragment
                //  new    ->  常购清单 HomeSteadyLayoutOftenBuyFragment
                //  old    ->  发现 FindFragment
                if (homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V2) {
                    shopListBottomTabClick(AnalysisConst.ShopList.SHOP_LIST_BOTTOM_CLICK, "店铺列表", "");

                    TrackManager.Companion.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_SHOPLIST_CLICK, new HashMap<>());
                }
                if (mFindFragment == null) {
                    if (homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V2) {
                        mFindFragment = new ShopListFragment();
                    } else if ((homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V1) || homeType == HOME_TYPE_CMS) {
                        mFindFragment = new HomeSteadyLayoutOftenBuyFragment();
                    } else {
                        mFindFragment = new FindFragment();
                    }
                    if (isCrash) {
                        for (Fragment fragment : fm.getFragments()) {
                            if (fragment instanceof ShopListFragment || fragment instanceof HomeSteadyLayoutOftenBuyFragment || fragment instanceof FindFragment) {
                                ft.remove(fragment);
                            }
                        }
                    }
                    ft.add(R.id.fl_container, mFindFragment, mFindFragment.getClass().getSimpleName());
                } else {
                    if (homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V2) {
                        if (mFindFragment instanceof FindFragment || mFindFragment instanceof HomeSteadyLayoutOftenBuyFragment) {
                            ft.remove(mFindFragment);
                            mFindFragment = new ShopListFragment();
                            ft.add(R.id.fl_container, mFindFragment, mFindFragment.getClass().getSimpleName());
                        }

                    } else if ((homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V1) || homeType == HOME_TYPE_CMS) {
                        if (mFindFragment instanceof FindFragment || mFindFragment instanceof ShopListFragment) {
                            ft.remove(mFindFragment);
                            mFindFragment = new HomeSteadyLayoutOftenBuyFragment();
                            ft.add(R.id.fl_container, mFindFragment, mFindFragment.getClass().getSimpleName());
                        }
                    } else {
                        if (mFindFragment instanceof HomeSteadyLayoutOftenBuyFragment || mFindFragment instanceof ShopListFragment) {
                            ft.remove(mFindFragment);
                            mFindFragment = new FindFragment();
                            ft.add(R.id.fl_container, mFindFragment, mFindFragment.getClass().getSimpleName());
                        }
                    }
                }
                setDiscover(true);
                ft.show(mFindFragment);
                if (mFindFragment instanceof HomeSteadyLayoutOftenBuyFragment) {
                    ((HomeSteadyLayoutOftenBuyFragment) mFindFragment).showTitle();
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_FIND);
                break;
        }
        //        LogUtils.d("position:"+position);
        ft.commitAllowingStateLoss();
        name = null;
        action = null;
        currIndex = i;
    }

    private void selectFrequentlyFragment(FragmentManager fm) {
        MainFrequentlyEvent.pv(this);
        if (mainFrequentlyFragment!=null) {
            mainFrequentlyFragment.analysisRefresh();
        }
        if (mainFrequentlyFragment == null) {
            mainFrequentlyFragment = new MainFrequentlyFragment();
            if (isCrash) {
                for (Fragment fragment : fm.getFragments()) {
                    if (fragment instanceof MainFrequentlyFragment) {
                        ft.remove(fragment);
                    }
                }
            }
            ft.add(R.id.fl_container, mainFrequentlyFragment, MainFrequentlyFragment.class.getSimpleName());
        }
        mainFrequentlyFragment.setCallBack(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                trackCommonTabComponentExposure(MainActivity.this);
                return null;
            }
        });
        mainFrequentlyFragment.onPv();
        setFrequently(true);
        ft.show(mainFrequentlyFragment);
        XyyIoUtil.track(XyyIoUtil.ACTION_SHOPPINGCART);
    }

    private void selectCartFragment(FragmentManager fm) {
        if (SpUtil.readInt(ConstantData.CART_TYPE, 3) == 3) {
            CartReport.pvCart(this);
            trackCommonTabComponentExposure(this);
            if (cartFragmentV3 == null) {
                cartFragmentV3 = new CartFragmentV3();
                if (isCrash) {
                    for (Fragment fragment : fm.getFragments()) {
                        if (fragment instanceof CartFragmentV3) {
                            ft.remove(fragment);
                        }
                    }
                }
                ft.add(R.id.fl_container, cartFragmentV3, CartFragmentV3.class.getSimpleName());
            }
            setShopping(true);
            if (!TextUtils.isEmpty(action)) {//购物车增加返回页面
                cartFragmentV3.setBackTitle(name, action);
            }
            ft.show(cartFragmentV3);
        } else {
            if (cartFragmentV2 == null) {
                cartFragmentV2 = new CartFragmentV2();
                if (isCrash) {
                    for (Fragment fragment : fm.getFragments()) {
                        if (fragment instanceof CartFragmentV2) {
                            ft.remove(fragment);
                        }
                    }
                }
                ft.add(R.id.fl_container, cartFragmentV2, CartFragmentV2.class.getSimpleName());
            }
            setShopping(true);
            if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(action)) {//购物车增加返回页面
                cartFragmentV2.setBackTitle(name, action);
            }
            ft.show(cartFragmentV2);
        }
        XyyIoUtil.track(XyyIoUtil.ACTION_SHOPPINGCART);
    }


    /**
     * 默认样式
     */
    private void resetTabs() {
        setHome(false);
        setFrequently(false);
        setDiscover(false);
        setShopping(false);
        setMineOrder(false);
        setMe(false);
//        setSort(false);
        if (ivActivity != null && llActivity != null && (llActivity.getVisibility() == View.VISIBLE) && bean != null && bean.bottom_centre_button_img_url != null) {
            if (bean.bottom_centre_button_img_url.startsWith("http")) {
                ImageHelper.with(this).load(bean.bottom_centre_button_img_url).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(ivActivity);
            } else {
                ImageHelper.with(this).load(AppNetConfig.getCDNHost() + bean.bottom_centre_button_img_url).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(ivActivity);
            }
        }
    }

    /**
     * 设置默认发现按钮文案
     */
    private void setDiscoverDefaultText() {
        //ka默认常购清单文案
        if (homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V2) {
            setButtonText(tvDiscover, "店铺列表");
        } else if ((homeType == HOME_TYPE_STEADY && homeSteadyType == HOME_TYPE_STEADY_V1) || homeType == HOME_TYPE_CMS) {
            setButtonText(tvDiscover, "常购清单");
        } else {
            setButtonText(tvDiscover, "发现");
        }
    }

    private void hideFragments(FragmentTransaction ft) {
        if (ft == null) {
            return;
        }
//        if (homeType == HOME_TYPE_CMS && homeCmsFragment != null) {
//            ft.hide(homeCmsFragment);
//        }
        if (homeType == HOME_TYPE_STEADY && homeSteadyFragment != null) {
            ft.hide((BaseFragment) homeSteadyFragment);
        }
        if (homeType == HOME_TYPE_OLD && homeFragment != null) {
            ft.hide(homeFragment);
        }
        if(mainFrequentlyFragment != null){
            ft.hide(mainFrequentlyFragment);
        }
        if (brandFragment != null) {
            ft.hide(brandFragment);
        }
        if (cartFragmentV2 != null) {
            ft.hide(cartFragmentV2);
        }
        if (cartFragmentV3 != null) {
            ft.hide(cartFragmentV3);
        }
        if (moreFragment != null) {
            ft.hide(moreFragment);
        }
        if (kaMineFragment != null) {
            ft.hide(kaMineFragment);
        }
        if (mFindFragment != null) {
            ft.hide(mFindFragment);
        }
        if (mineOrderFragment != null) {
            ft.hide(mineOrderFragment);
        }
    }

    @Override
    protected void onDestroy() {
        mainActivity = null;
        super.onDestroy();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(MainActivity.this).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            exit();
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    // 定义一个变量，来标识是否退出
    private static boolean isExit = false;

    @SuppressLint("HandlerLeak")
    public static Handler mHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            isExit = false;
        }
    };

    private void exit() {
        if (!isExit) {
            isExit = true;
            ToastUtils.showShort("再按一次退出程序");
            // 利用handler延迟发送更改状态信息
            mHandler.sendEmptyMessageDelayed(0, 2000);
        } else {
            if (isDowning) {//正在下载apk,禁止杀死app
                IntentUtil.openHome(this);
                isExit = false;
            } else {
                finish();
                System.exit(0);
            }
        }
    }


    private String fileAbsolutPath;

    // 检查升级
    @SuppressLint("CheckResult")
    private void checkUpdate() {
        AppUpdate.getInstance()
                .setAppUserId(SpUtil.getMerchantid())
                .start(false);

//        if (needCheckUpdate) {
//            updateManagerV2 = new AppUpdateManagerV2();
//            updateManagerV2.checkUpdate();
//            updateManagerV2.setDownloadListener(new AppUpdateManagerV2.OnDownloadListener() {
//                @Override
//                public void onDownloadSuccess(File file) {
//                    fileAbsolutPath = file.getAbsolutePath();
//                    updateManagerV2.installApk(fileAbsolutPath);
//
//                    needCheckUpdate = false;
//                }
//
//                @Override
//                public void onDownloading(int progress) {
//                    if (!isDestroy) {
//                        updateManagerV2.showProgressDialog(progress);
//                    }
//                }
//
//                @Override
//                public void onDownloadFailed(Exception e) {
//                    needCheckUpdate = false;
//                    updateManagerV2.dissmissProgressDialog();
//
//                }
//            });
//        }

        //首页是single，添加了切换账号的功能，所以merchantId从sp中取
        BugUtil.updateUserId(SpUtil.getMerchantid());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == INSTALL_REQUEST) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                boolean canInstallPackages = YBMAppLike.getAppContext().getPackageManager().canRequestPackageInstalls();
                if (canInstallPackages && !TextUtils.isEmpty(fileAbsolutPath)) {
                    updateManagerV2.installApk(fileAbsolutPath);
                }
            }
        } else if (requestCode == DIAL_REQUEST_CODE) {
            if (homeSteadyFragment != null && homeSteadyFragment instanceof HomeSteadyLayoutFragmentV3) {
                HomeSteadyLayoutFragmentV3 homeFragment = (HomeSteadyLayoutFragmentV3) homeSteadyFragment;
                homeFragment.requestHomeAlert(homeFragment.getDialSuspension().homeAlertType + "");
            }
        }
    }

    private void checkSD() {
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if (MemoryManager.SDFull() || MemoryManager.InternalFull()) {//sd卡已经满了
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showSDFullDialog();
                            }
                        });
                    }
                } catch (Throwable e) {
                    BugUtil.sendBug(e);
                }
            }
        });
    }

    private void showSDFullDialog() {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        String msg = "手机存储空间不足100MB了，为保存app正常使用，请清理存储空间后继续使用！";
        dialogEx.setMessage(msg).setCancelButton("去清理", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
                try {
                    Intent intent = new Intent(Settings.ACTION_INTERNAL_STORAGE_SETTINGS);
                    startActivity(intent);
                } catch (Throwable e) {
                    BugUtil.sendBug(e);
                }

            }
        }).setConfirmButton("知道了", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).show();
    }

    public void setDowning(boolean isDowning) {
        this.isDowning = isDowning;
    }

    public static MainActivity getMainActivity() {
        return mainActivity;
    }

    public static Intent getIntent2Me(boolean checkUpdate) {
        Intent intent = new Intent(YBMAppLike.getAppContext(), MainActivity.class);
        intent.putExtra("checkUpdate", checkUpdate);
        return intent;
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {//禁止恢复crash现场
        super.onSaveInstanceState(outState);
    }


    //设置活动入口是否可见
    public void setActivity(HomeConfigBean bean) {
        if (bean == null || llActivity == null || ivActivity == null) {
            return;
        }
        //needUpdate = !bean.needUpdate(this.bean);
        //        LogUtils.d("needUpdate:"+needUpdate);
        this.bean = bean;

//        if (TextUtils.isEmpty(bean.bottom_centre_button_action) || TextUtils.isEmpty(bean.bottom_centre_button_img_url)) {
//            llActivity.setVisibility(View.GONE);
//            ivActivity.setTag(R.id.tag_action, "");
//            if (position == 20) {
//                setSelect(0);
//            }
//        } else {
//            llActivity.setVisibility(View.VISIBLE);
//            ivActivity.setTag(R.id.tag_action, bean.bottom_centre_button_action);
//            setActivity(position == 20);
//        }

        setflBg(bean.bottom_background_image);
        setButtonText(tvLlHome, bean.bottom_first_button_text);
        setTabTag(llHome, bean.bottom_first_button_track_data);

        setButtonText(tvDiscover, bean.bottom_second_button_text);
        setTabTag(llDiscover, bean.bottom_second_button_track_data);

//        setButtonText(tvSort, bean.bottom_second_button_text);
        setButtonText(tvShopping, bean.bottom_third_button_text);
        setTabTag(llShopping, bean.bottom_third_button_track_data);

        if (!TextUtils.isEmpty(bean.bottom_fourth_button_text)) {
            setButtonText(tvOrder, bean.bottom_fourth_button_text);
            setTabTag(llOrder, bean.bottom_fourth_button_track_data);
        } else {
            setButtonText(tvOrder, "订单");
            bean.bottom_fourth_button_track_data = generateOrderTrackData();
            setTabTag(llOrder, bean.bottom_fourth_button_track_data);
        }

        if (!TextUtils.isEmpty(bean.bottom_frequently_button_text)) {
            setButtonText(tvFrequently, bean.bottom_frequently_button_text);
            setTabTag(llFrequently, bean.bottom_frequently_button_track_data);
        } else {
            setButtonText(tvFrequently, "常购常搜");
            bean.bottom_frequently_button_track_data = generateFrequentlyBuyTrackData();
            setTabTag(llFrequently, bean.bottom_frequently_button_track_data);
        }
        setButtonText(tvMe, bean.bottom_fifth_button_text);
        setTabTag(llMe, bean.bottom_fifth_button_track_data);
        handleTabTrackData(bean);
        //
        setHome(position == 0, true);
        setFrequently(position == 1,true);
//        setSort(position == 1 || position >= 10 && position <= 13, true);
        setShopping(position == 2, true);
//        setMineOrder(position == 3, true);
        setMe(position == 4, true);
        setDiscover(position == 5, true);
        //购物车动画
        try {
            if (llShoppingTv != null) {
                llShoppingTv.getLocationInWindow(YBMAppLike.endLocation);// llShoppingRl是那个购物车
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
    }

    public int getBottomTabHeight() {
        return flBg.getMeasuredHeight();
    }

    private void setflBg(String url) {
        if (TextUtils.isEmpty(url)) {//背景色
            url = "#ffffffff";
        }
        if (url.startsWith("#")) {
            flBg.setBackgroundColor(Color.parseColor(url));
        } else {
            if (!url.startsWith("http")) {
                url = AppNetConfig.getCDNHost() + url;
            }
            ImageHelper.with(this).load(url).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(new ViewTarget<View, GlideDrawable>(flBg) {
                @Override
                public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                    flBg.setBackground(resource.getCurrent());
                }
            });
        }
    }


    private int getBottomTextColor(boolean checked) {
        //选中的色值
        int color = getResources().getColor(R.color.home_text_02);
        if (bean != null && !TextUtils.isEmpty(bean.bottom_text_color)) {
            try {
                color = Color.parseColor(bean.bottom_text_color);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //没有选中的色值
        int def = getResources().getColor(R.color.text_9494A6);
        if (bean != null && !TextUtils.isEmpty(bean.bottom_text_color_def)) {
            try {
                def = Color.parseColor(bean.bottom_text_color_def);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return checked ? color : def;
    }

    private void setButtonText(TextView tv, String text) {
        // 为了cms  唉。。。快点把老配置搞掉吧
        if (homeType == HOME_TYPE_STEADY || homeType == HOME_TYPE_CMS) {
            if (!TextUtils.isEmpty(text)) {
                tv.setText(text);
                tv.setVisibility(View.VISIBLE);
            } else {
                tv.setText("");
                tv.setVisibility(View.GONE);
            }
        } else {
            if (!TextUtils.isEmpty(text)) {
                tv.setText(text);
            }
        }
    }

    private void setTabTag(View tagView, Object tag) {
        tagView.setTag(tag);
    }

    private void setActivity(boolean checked) {
        String url = null;
        if (bean != null) {
            url = bean.bottom_centre_button_img_url;
        }
        ImageHelper.with(this).load(url).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(ivActivity);
    }

    private void setHome(boolean checked) {
        setHome(checked, false);
    }

    private void setHome(boolean checked, boolean cache) {
        tvLlHome.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            if (!TextUtils.isEmpty(bean.bottom_first_button_img_select_url)) {
                //set1010Icon(checked, llHome, ivLlHome);
            }
            url = checked ? bean.bottom_first_button_img_select_url : bean.bottom_first_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(this).load(bean.bottom_first_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }

//        ImageHelper.with(this).load(url).dontAnimate().error(checked ? R.drawable.bid01 : R.drawable.bid02).placeholder(checked ? R.drawable.bid01 : R.drawable.bid02).dontTransform()
//                .diskCacheStrategy(DiskCacheStrategy.SOURCE).override(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22)).into(ivLlHome);

        if (homeSteadyType == HOME_TYPE_STEADY_V2 && checked && homeType == HOME_TYPE_STEADY) {
            llHome.setVisibility(View.GONE);
            llToTop.setVisibility(View.VISIBLE);
            ivSingleTop.setVisibility(!isToTop ? View.VISIBLE : View.GONE);
            ivToTop.setVisibility(isToTop ? View.VISIBLE : View.GONE);
            loadImage(ivSingleTop, url, R.drawable.bid01_checked_single_img, R.drawable.bid01_checked_single_img, R.drawable.bid02, checked);
        } else {
            llToTop.setVisibility(View.GONE);
            ivSingleTop.setVisibility(View.GONE);
            llHome.setVisibility(View.VISIBLE);
            loadImage(ivLlHome, url, R.drawable.bid01_checked_error, R.drawable.bid01_checked_error, R.drawable.bid02, checked);
        }
    }

    private void set1010MainIcon(boolean checked, LinearLayout ll, ImageView iv) {
        if (checked) {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, 0, 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(35);
            layoutParams.height = ConvertUtils.dp2px(35);
            iv.setLayoutParams(layoutParams);

        } else {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, ConvertUtils.dp2px(10), 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(22);
            layoutParams.height = ConvertUtils.dp2px(22);
            iv.setLayoutParams(layoutParams);

        }
    }

    // 只有中间【发现】按钮双十期间放大
    private void set1010Icon(boolean checked, LinearLayout ll, ImageView iv) {
        if (!"发现".equalsIgnoreCase(tvDiscover.getText().toString()) && homeType != HOME_TYPE_STEADY && !"常购清单".equalsIgnoreCase(tvDiscover.getText().toString())) {//增加新首页和常购清单不显示放大
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, ConvertUtils.dp2px(0), 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(50);
            layoutParams.height = ConvertUtils.dp2px(50);
            iv.setLayoutParams(layoutParams);
        } else {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, ConvertUtils.dp2px(10), 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(22);
            layoutParams.height = ConvertUtils.dp2px(22);
            iv.setLayoutParams(layoutParams);
        }

//        if (checked) {
//            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
//            lp.setMargins(0, 0, 0, 0);
//            ll.setLayoutParams(lp);
//
//            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
//            layoutParams.width = ConvertUtils.dp2px(35);
//            layoutParams.height = ConvertUtils.dp2px(35);
//            iv.setLayoutParams(layoutParams);
//
//        } else {
//            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
//            lp.setMargins(0, ConvertUtils.dp2px(10), 0, 0);
//            ll.setLayoutParams(lp);
//
//            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
//            layoutParams.width = ConvertUtils.dp2px(22);
//            layoutParams.height = ConvertUtils.dp2px(22);
//            iv.setLayoutParams(layoutParams);
//
//        }
    }

    private void set1010CartIcon(boolean checked, FrameLayout ll, ImageView iv) {
        if (checked) {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, 0, 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(35);
            layoutParams.height = ConvertUtils.dp2px(35);
            iv.setLayoutParams(layoutParams);

        } else {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ll.getLayoutParams();
            lp.setMargins(0, ConvertUtils.dp2px(10), 0, 0);
            ll.setLayoutParams(lp);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) iv.getLayoutParams();
            layoutParams.width = ConvertUtils.dp2px(22);
            layoutParams.height = ConvertUtils.dp2px(22);
            iv.setLayoutParams(layoutParams);

        }
    }

    private void setShopping(boolean checked) {
        setShopping(checked, false);
    }

    private void setShopping(boolean checked, boolean cache) {
        tvShopping.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            if (!TextUtils.isEmpty(bean.bottom_third_button_img_select_url)) {
                //set1010CartIcon(checked, llShoppingFl, ivShopping);
            }
            url = checked ? bean.bottom_third_button_img_select_url : bean.bottom_third_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(getApplicationContext()).load(bean.bottom_third_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }
//        ImageHelper.with(this).load(url).dontAnimate().error(checked ? R.drawable.bid05 : R.drawable.bid06).placeholder(checked ? R.drawable.bid05 : R.drawable.bid06).dontTransform()
//                .diskCacheStrategy(DiskCacheStrategy.SOURCE).override(ConvertUtils.dp2px(28), ConvertUtils.dp2px(28)).into(ivShopping);
        loadImage(ivShopping, url, R.drawable.bid05_checked_error, R.drawable.bid05_checked_error, R.drawable.bid06, checked);
    }

//    private void setSort(boolean checked) {
//        setSort(checked, false);
//    }
//
//    private void setSort(boolean checked, boolean cache) {
//        tvSort.setTextColor(getBottomTextColor(checked));
//        String url = null;
//        if (bean != null) {
//            if (!TextUtils.isEmpty(bean.bottom_second_button_img_select_url)) {
//                //set1010Icon(checked, llSort, ivSort);
//            }
//            url = checked ? bean.bottom_second_button_img_select_url : bean.bottom_second_button_img_url;
//        }
//        if (cache && bean != null) {
//            ImageHelper.with(this).load(bean.bottom_second_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
//        }
////        ImageHelper.with(this).load(url).dontAnimate().error(checked ? R.drawable.bid03 : R.drawable.bid04).placeholder(checked ? R.drawable.bid03 : R.drawable.bid04).dontTransform()
////                .diskCacheStrategy(DiskCacheStrategy.SOURCE).override(ConvertUtils.dp2px(28), ConvertUtils.dp2px(28)).into(ivSort);
//        loadImage(ivSort, url, R.drawable.bid03_checked_error, R.drawable.bid03_checked_error, R.drawable.bid04, checked);
//    }

    private void setDiscover(boolean checked) {
        setDiscover(checked, false);
    }

    private void setDiscover(boolean checked, boolean cache) {
        tvDiscover.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            if (!TextUtils.isEmpty(bean.bottom_second_button_img_select_url)) {
                set1010Icon(checked, llDiscover, ivDiscover);
            }
            url = checked ? bean.bottom_second_button_img_select_url : bean.bottom_second_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(this)
                    .load(bean.bottom_second_button_img_select_url)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }
//        ImageHelper.with(this)
//                .load(url)
//                .dontAnimate()
//                .error(checked ? R.drawable.bid09 : R.drawable.bid10)
//                .placeholder(checked ? R.drawable.bid09 : R.drawable.bid10)
//                .dontTransform()
//                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
//                .override(ConvertUtils.dp2px(28), ConvertUtils.dp2px(28))
//                .into(ivDiscover);
        loadImage(ivDiscover, url, R.drawable.bid09_checked_error, R.drawable.bid09_checked_error, R.drawable.bid10, checked);
    }

    private void setMe(boolean checked) {
        setMe(checked, false);
    }

    private void setMe(boolean checked, boolean cache) {
        tvMe.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            if (!TextUtils.isEmpty(bean.bottom_fifth_button_img_select_url)) {
                //set1010Icon(checked, llMe, ivMe);
            }
            url = checked ? bean.bottom_fifth_button_img_select_url : bean.bottom_fifth_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(this).load(bean.bottom_fifth_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }
        loadImage(ivMe, url, R.drawable.bid07_checked_error, R.drawable.bid07_checked_error, R.drawable.bid08, checked);
    }

    private void setMineOrder(boolean checked) {
        setMineOrder(checked, false);
    }

    private void setMineOrder(boolean checked, boolean cache) {
        tvOrder.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            url = checked ? bean.bottom_fourth_button_img_select_url : bean.bottom_fourth_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(this).load(bean.bottom_fourth_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }
        loadImage(ivOrder, url, R.drawable.icon_main_tab_order_true, R.drawable.icon_main_tab_order_true, R.drawable.icon_main_tab_order_false, checked);
    }

    private void setFrequently(boolean checked) {
        setFrequently(checked, false);
    }

    private void setFrequently(boolean checked, boolean cache) {
        tvFrequently.setTextColor(getBottomTextColor(checked));
        String url = null;
        if (bean != null) {
            url = checked ? bean.bottom_frequently_button_img_select_url : bean.bottom_frequently_button_img_url;
        }
        if (cache && bean != null) {
            ImageHelper.with(this).load(bean.bottom_frequently_button_img_select_url).diskCacheStrategy(DiskCacheStrategy.SOURCE).preload(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22));
        }
        loadImage(ivFrequently, url, R.drawable.icon_offten_buy_or_search_selected, R.drawable.icon_offten_buy_or_search_selected, R.drawable.icon_offten_buy_or_search, checked);
    }

    //返回自己
    @Override
    public BaseActivity getMySelf() {
        return getMainActivity();
    }

    public int getPosition() {
        return position;
    }

    @Override
    public String getPageName() {
        String pageName = "";
        switch (position) {
            case 0:
                pageName = XyyIoUtil.PAGE_HOMEPAGE;
                break;
            case 1:
            case 10:
            case 11:
            case 12:
            case 13:
                pageName = XyyIoUtil.PAGE_ALLDRUGS;
                break;
            case 2:
                pageName = XyyIoUtil.PAGE_SHOPPINGCART;
                break;
            case 3:
                pageName = XyyIoUtil.PAGE_ME;
                break;
            case 4:
                pageName = XyyIoUtil.PAGE_FIND;
                break;
        }
        return pageName;
    }

    /**
     * tab 加载图片
     *
     * @param iv
     * @param url
     * @param selectedRes
     * @param unSelectedRes
     * @param errorChecked
     * @param checked
     */
    private void loadImage(ImageView iv, String url, int selectedRes, int errorChecked, int unSelectedRes, boolean checked) {
        DrawableTypeRequest dtr = null;
        if (url != null) {
            dtr = ImageHelper.with(this).load(url);
        } else {
            dtr = ImageHelper.with(this).load(checked ? selectedRes : unSelectedRes);
        }
        dtr.diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .override(ConvertUtils.dp2px(22), ConvertUtils.dp2px(22))
                .error(checked ? errorChecked : unSelectedRes)
                .into(new GlideDrawableImageViewTarget(iv, 1));
    }

    /**
     * 处理跳转的路由
     */
    private void handleExternalRouter(Intent intent) {
        Intent externalIntent = intent;
        if (intent == null) {
            externalIntent = getIntent();
        }
        // 通过Routers.KEY_RAW_URL获取参数，避免参数中存在url参数导致的解析错误
        String url = externalIntent.getStringExtra(Routers.KEY_RAW_URL);
        if (!TextUtils.isEmpty(url) && url.startsWith("ybmpage://main?routerPath=")) {
            RoutersUtils.open(url.replace("ybmpage://main?routerPath=", ""));
        }
    }

    /**
     * 处理到顶部按钮
     *
     * @param isToTop
     */
    public void handleToTopBtn(boolean isToTop) {
        if (homeSteadyType == HOME_TYPE_STEADY_V2) {
            llHome.setVisibility(View.GONE);
            llToTop.setVisibility(View.VISIBLE);
            ivToTop.setVisibility(isToTop ? View.VISIBLE : View.GONE);
            ivSingleTop.setVisibility(!isToTop ? View.VISIBLE : View.GONE);
        }
        if (homeSteadyType == HOME_TYPE_STEADY_V1) {
            llHome.setVisibility(isToTop ? View.GONE : View.VISIBLE);
            llToTop.setVisibility(isToTop ? View.VISIBLE : View.GONE);
            ivToTop.setVisibility(isToTop ? View.VISIBLE : View.GONE);
            ivSingleTop.setVisibility(View.GONE);
        }
        this.isToTop = isToTop;
    }

    /**
     * 切换tab
     */
    public void showMoreShop() {
        setSelect(5);
    }

    private String mShopIndex = "";

    /**
     * 切换tab
     */
    public void showOrderShop(String index) {
        showOrderShop(index,false);
    }

    public void showOrderShop(String index,boolean needRefresh) {
        jgspid = "4202";
        mShopIndex = index;
        if (mineOrderFragment != null) {
            int indexInt = mineOrderFragment.getTabInt(index);
            mineOrderFragment.setMTargetTabIndex(indexInt);
        }
        if (mineOrderFragment == null && !TextUtils.equals(index, "0")) {
            putExtension(OrderlistConstant.ORDER_LIST_TRACK_PV_SWITCH, false);
        }
        setSelect(3);
        if (mineOrderFragment != null) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mineOrderFragment.changeTab(index);
                    if(needRefresh){
                        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH));
                    }
                }
            }, 0);

        }
    }

    /**
     * 初始化讯飞
     */
    private void initVoice() {
        PrivacyInitManager.INSTANCE.initVoice();
    }

    @Nullable
    @Override
    public IHomeSteadyFragment getHomeFragment() {
        return homeSteadyFragment;
    }
}
