package com.ybmmarket20.home.mine

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.business.order.ui.OrderListActivity
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.home.mine.bean.Mine2OrderBean
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import kotlinx.android.synthetic.main.view_mine2_order.view.*

class Mine2OrderView(context: Context, attr: AttributeSet?) :
    AbsMine2BaseView<List<Mine2OrderBean>>(context, attr) {

    var mItemList: MutableList<Mine2OrderBean> = mutableListOf()
    var mContainer: Mine2HeaderItemContainer<List<Mine2OrderBean>>? = null
    override fun getLayoutId(): Int = R.layout.view_mine2_order

    override fun initialize() {
        super.initialize()
        cl_mine2_all_order.setOnClickListener {
            val intent = Intent(context, OrderListActivity::class.java)
            intent.putExtra(IntentCanst.ORDER_STATE, "0")
            context.startActivity(intent)
            XyyIoUtil.track("action_Me_AllOrders")
        }
    }

    override fun setData(container: Mine2HeaderItemContainer<List<Mine2OrderBean>>) {
        if (!container.entry.isNullOrEmpty()) {
            mContainer = container
            container.entry?.let(mItemList::addAll)
            rv_mine2_order.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            rv_mine2_order.adapter = Mine2OrderAdapter(mItemList)
        }
    }

    /**
     * 设置气泡数量
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setOrderBubbleCount(bubbleCountList: List<Int>?) {
        if (bubbleCountList.isNullOrEmpty() || bubbleCountList.size != mItemList.size) return
        val adapter = rv_mine2_order.adapter ?: return
        mItemList = mItemList.zip(bubbleCountList) { item, count ->
            item.bubbleCount = count
            item
        }.toMutableList()
        adapter.notifyDataSetChanged()
    }

    inner class Mine2OrderAdapter(list: List<Mine2OrderBean>) :
        YBMBaseAdapter<Mine2OrderBean>(R.layout.item_mine2_order, list) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: Mine2OrderBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val ivMine2OrderIcon = holder.getView<ImageView>(R.id.iv_mine2_order_icon)
                val tvMine2OrderDes = holder.getView<TextView>(R.id.tv_mine2_order_des)
                val tvMine2OrderBubble = holder.getView<TextView>(R.id.tv_mine2_order_bubble)
                ivMine2OrderIcon.setImageResource(bean.icon)
                tvMine2OrderDes.text = bean.des
                tvMine2OrderBubble.visibility = if (bean.bubbleCount > 0) View.VISIBLE else View.GONE
                tvMine2OrderBubble.text = "${bean.bubbleCount}"
                holder.itemView.setOnClickListener {
                    val intent = Intent(context, OrderListActivity::class.java)
                    intent.putExtra(IntentCanst.ORDER_STATE, "${bean.orderType}")
                    context.startActivity(intent)
                    XyyIoUtil.track(bean.trackKey)
                }
            }
        }
    }
}