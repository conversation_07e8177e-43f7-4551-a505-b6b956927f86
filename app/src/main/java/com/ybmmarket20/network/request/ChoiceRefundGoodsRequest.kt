package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.QueryGiftRefundNum
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception


/**
 * 退款单计算赠品数量
 */
interface ChoiceRefundGoodsService {

    @FormUrlEncoded
    @POST("refund/queryGiftRefundNum")
    suspend fun queryGiftRefundNum(@Field("giftRefundDetailStr") giftRefundDetailStr: String, @Field("orderNo") orderNo: String):BaseBean<List<RefundProductListBean>>
}

class ChoiceRefundGoodsRequest {

    suspend fun queryGiftRefundNum(giftRefundDetailStr: String, orderNo: String): BaseBean<List<RefundProductListBean>> = try {
        NetworkService.instance.mRetrofit.create(ChoiceRefundGoodsService::class.java).queryGiftRefundNum(giftRefundDetailStr, orderNo)
    } catch (e: Exception) {
        BaseBean<List<RefundProductListBean>>().initWithException(e)
    }
}