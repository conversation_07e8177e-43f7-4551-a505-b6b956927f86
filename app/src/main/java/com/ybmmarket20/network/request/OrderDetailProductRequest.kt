package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.OrderDetailProductListData
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 14:48
 *    desc   :
 */
interface IOrderDetailProductService {

    @FormUrlEncoded
    @POST("order/queryProductDetailList")
    suspend fun getOrderProductList(@FieldMap params: HashMap<String, Any>): BaseBean<OrderDetailProductListData>
}

class OrderDetailProductRequest {
    //提醒发货
    suspend fun getOrderProductList(orderNo: String): BaseBean<OrderDetailProductListData> = try {
        NetworkService.instance.mRetrofit.create(IOrderDetailProductService::class.java)
            .getOrderProductList(
                hashMapOf(
                    "orderNo" to orderNo,
                    "merchantId" to SpUtil.getMerchantid(),
                )
            )
    } catch (e: Exception) {
        BaseBean<OrderDetailProductListData>().initWithException(e)
    }
}