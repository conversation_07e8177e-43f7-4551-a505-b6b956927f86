package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.network.ResponseIntercepter
import com.ybmmarketkotlin.bean.ApplyNoticeBean
import com.ybmmarketkotlin.bean.RebateVoucherBean
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception

interface PayNoticeService {

    @FormUrlEncoded
    @POST("order/pay/reminder")
    suspend fun getPayNotice(@Field("merchantId") merchantId: String): BaseBean<List<ApplyNoticeBean>>

}

class PayNoticeRequest {

    /**
     *  获取支付成功页面头部信息
     */
    suspend fun getPayNotice(merchantId: String): BaseBean<List<ApplyNoticeBean>> = try {
        NetworkService.instance.mRetrofit.create(PayNoticeService::class.java).getPayNotice(merchantId)
    } catch (e: Exception) {
        BaseBean<List<ApplyNoticeBean>>().initWithException(e)
    }


}



