package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.DeviceStatusOnPayBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.*

interface IQueryDeviceStatusOnPayService {

    @FormUrlEncoded
    @POST("card/device/query")
    suspend fun queryDeviceStatusOnPay(@Field("merchantId")merchantId: String): BaseBean<DeviceStatusOnPayBean>

    @FormUrlEncoded
    @POST("card/device/addOrUpdate")
    suspend fun registerAndUpdateFingerprint(@FieldMap params: Map<String, String>, @Header("timestamp")timestamp: String): BaseBean<Any>
}

class QueryDeviceStatusOnPayRequest {

    /**
     * 获取支付设备状态
     */
    suspend fun queryDeviceStatusOnPay(): BaseBean<DeviceStatusOnPayBean> = try {
        NetworkService.instance.mRetrofit.create(IQueryDeviceStatusOnPayService::class.java).queryDeviceStatusOnPay(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<DeviceStatusOnPayBean>().initWithException(e)
    }

    /**
     * 注册指纹
     */
    suspend fun registerAndUpdateFingerprint(@FieldMap params: Map<String, String>, timestamp: String): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(IQueryDeviceStatusOnPayService::class.java).registerAndUpdateFingerprint(params, timestamp)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }
}