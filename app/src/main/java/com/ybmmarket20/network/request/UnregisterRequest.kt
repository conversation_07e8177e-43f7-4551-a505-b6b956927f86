package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import retrofit2.http.*

interface UnregisterRequest {

    @FormUrlEncoded
    @POST("findCancelAnAccountStatus")
    suspend fun findCancelAnAccountStatus(@Field("merchantId") merchantId: String): BaseBean<Boolean>

    @FormUrlEncoded
    @POST("cancelAnAccount")
    suspend fun cancelAnAccount(@Field("merchantId") merchantId: String): BaseBean<EmptyBean>


}