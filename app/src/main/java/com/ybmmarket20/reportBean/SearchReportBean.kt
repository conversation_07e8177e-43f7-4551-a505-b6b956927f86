package com.ybmmarket20.reportBean

import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.transform2Date
import com.ydmmarket.report.annotation.ReportEventName
import com.ydmmarket.report.annotation.ReportParamsKey
import java.io.Serializable

/**
 * @class   SearchReportBean
 * <AUTHOR>
 * @date  2024/10/22
 * @description
 */
class SearchReportBean {
}


@ReportEventName("app_page_top_hot_word_exposure")
data class AppPageTopHotWordExposure(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("jgspid") var jgspid: String? = "",
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("click_name") var click_name: String? = "", //热词名称
        @ReportParamsKey("click_link") var click_link: String? = "" //配置的搜索热词是跳活动页
)

@ReportEventName("app_action_top_hot_word_click")
data class AppActionTopHotWordClick(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("jgspid") var jgspid: String? = "",
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("click_name") var click_name: String? = "", //热词名称
        @ReportParamsKey("click_link") var click_link: String? = "" //配置的搜索热词是跳活动页
) : Serializable

@ReportEventName("app_action_top_search_sug_click")
data class AppActionTopSearchSugClick(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("jgspid") var jgspid: String? = "",
        @ReportParamsKey("key_word") var key_word: String? = "",
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("click_name") var click_name: String? = "", //热词名称
        @ReportParamsKey("click_link") var click_link: String? = "" //配置的搜索热词是跳活动页
) : Serializable

@ReportEventName("app_action_top_search_click")
data class AppActionTopSearchClick(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("jgspid") var jgspid: String? = "",
        @ReportParamsKey("key_word") var key_word: String? = "",
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("click_link") var click_link: String? = "" //配置的搜索热词是跳活动页

)

@ReportEventName("page_list_build")
data class PageListBuild(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("sptype") var sptype: String? = "", //来源类型
        @ReportParamsKey("jgspid") var jgspid: String? = "", //来源ID
        @ReportParamsKey("sid") var sid: String? = "", //追踪ID
        @ReportParamsKey("result_cnt") var result_cnt: Int? = 0, //结果数量
        @ReportParamsKey("page_no") var page_no: Int? = 0, //当前页码
        @ReportParamsKey("page_size") var page_size: Int? = 0, //每页显示条数
        @ReportParamsKey("total_page") var total_page: Int? = 0, //总页数
        @ReportParamsKey("key_word") var key_word: String? = "", //搜索关键词
        @ReportParamsKey("search_sort_strategy_id") var search_sort_strategy_id: String? = "", //A/B策略ID
        @ReportParamsKey("paixu") var paixu: String? = "", //排序
        @ReportParamsKey("is_filter") var filter: Int? = 0, //是否过滤 1-有；0-没有
        @ReportParamsKey("changjia") var changjia: ArrayList<String?>? = arrayListOf(), //厂家筛选集合
        @ReportParamsKey("shangjia") var shangjia: ArrayList<String?>? = arrayListOf(), //商家筛选集合
        @ReportParamsKey("guige") var guige: ArrayList<String?>? = arrayListOf(), //规格筛选集合
        @ReportParamsKey("yplx") var yplx: ArrayList<String?>? = arrayListOf(), //药品类型筛选集合
        @ReportParamsKey("period_validity") var period_validity: String? = "", //有效期
        @ReportParamsKey("fenlei") var fenlei: ArrayList<String?>? = arrayListOf(), //分类
        @ReportParamsKey("minprice") var minprice: Double? = 0.0,
        @ReportParamsKey("maxprice") var maxprice: Double? = 0.0,
        @ReportParamsKey("tag_filter") var tag_filter: ArrayList<String?>? = arrayListOf(), //动态筛选标签名称
        @ReportParamsKey("tag_filter_type") var tag_filter_type: ArrayList<String?>? = arrayListOf(), //动态筛选标签类型
)

@ReportEventName("app_page_search_dynamic_filter_exposure")
data class AppPageSearchDynamicFilterExposure(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("key_word") var key_word: String? = "",
        @ReportParamsKey("tag_filter") var tag_filter: ArrayList<String?>? = arrayListOf(), //动态筛选标签名称
        @ReportParamsKey("tag_filter_type") var tag_filter_type: ArrayList<String?>? = arrayListOf(), //动态筛选标签类型
)

@ReportEventName("app_action_search_dynamic_filter_click")
data class AppPageSearchDynamicFilterClick(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("key_word") var key_word: String? = "",
        @ReportParamsKey("tag_filter") var tag_filter: ArrayList<String?>? = arrayListOf(), //动态筛选标签名称
        @ReportParamsKey("tag_filter_type") var tag_filter_type: ArrayList<String?>? = arrayListOf(), //动态筛选标签类型
)

@ReportEventName("app_action_search_filter_click")
data class AppActionSearchFilterClick(
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("key_word") var key_word: String? = "", //搜索关键词
        @ReportParamsKey("paixu") var paixu: String? = "", //排序
        @ReportParamsKey("changjia") var changjia: ArrayList<String?>? = arrayListOf(), //厂家筛选集合
        @ReportParamsKey("shangjia") var shangjia: ArrayList<String?>? = arrayListOf(), //商家筛选集合
        @ReportParamsKey("guige") var guige: ArrayList<String?>? = arrayListOf(), //规格筛选集合
        @ReportParamsKey("yplx") var yplx: ArrayList<String?>? = arrayListOf(), //药品类型筛选集合
        @ReportParamsKey("period_validity") var period_validity: String? = "", //有效期
        @ReportParamsKey("fenlei") var fenlei: ArrayList<String?>? = arrayListOf(), //分类
        @ReportParamsKey("minprice") var minprice: Double? = 0.0,
        @ReportParamsKey("maxprice") var maxprice: Double? = 0.0,
)

data class JGPageListCommonBean(
        @ReportParamsKey("sptype") var sptype: String? = "", //来源类型
        @ReportParamsKey("jgspid") var jgspid: String? = "", //来源ID
        @ReportParamsKey("sid") var sid: String? = "", //追踪ID
        @ReportParamsKey("result_cnt") var result_cnt: Int? = 0, //结果数量
        @ReportParamsKey("page_no") var page_no: Int? = 0, //当前页码
        @ReportParamsKey("page_size") var page_size: Int? = 0, //每页显示条数
        @ReportParamsKey("total_page") var total_page: Int? = 0, //总页数
        @ReportParamsKey("key_word") var key_word: String? = "", //搜索关键词
)

@ReportEventName("page_list_product_exposure")
data class PageListProductExposure(
        var jGPageListCommonBean: JGPageListCommonBean? = null,
        @ReportParamsKey("\$url") var url:String? = JGTrackManager.TrackSearchResult.TRACK_URL,
        @ReportParamsKey("\$title") var title:String? = JGTrackManager.TrackSearchResult.TITLE,
        @ReportParamsKey("\$referrer") var referrer:String? = JGTrackManager.TrackSearchResult.TRACK_URL,
        @ReportParamsKey("search_sort_strategy_id") var search_sort_strategy_id: String? = "", //A/B策略ID
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("operation_id") var operation_id: String? = "", //运营位ID
        @ReportParamsKey("operation_rank") var operation_rank: Int? = null, //运营位位置
        @ReportParamsKey("list_position_type") var list_position_type: String? = "", //搜索坑位类型
        @ReportParamsKey("list_position_typename") var list_position_typename: String? = "", //搜索坑位类型名称
        @ReportParamsKey("product_id") var product_id: Long? = 0, //商品ID
        @ReportParamsKey("product_name") var product_name: String? = "", //	商品名称
        @ReportParamsKey("product_first") var product_first: String? = "", //商品一级分类
        @ReportParamsKey("product_price") var product_price: Double? = 0.0, //商品现价
        @ReportParamsKey("product_type") var product_type: String? = "", //商品类型
        @ReportParamsKey("product_activity_type") var product_activity_type: String? = "", //商品营销活动类型
        @ReportParamsKey("product_shop_code") var product_shop_code: String? = "", //商品店铺编码
        @ReportParamsKey("product_shop_name") var product_shop_name: String? = "", //	商品店铺名称

)

@ReportEventName("action_list_product_click")
data class PageListProductClick(
        var jGPageListCommonBean: JGPageListCommonBean? = null,
        @ReportParamsKey("\$url") var url:String? = JGTrackManager.TrackSearchResult.TRACK_URL,
        @ReportParamsKey("\$title") var title:String? = JGTrackManager.TrackSearchResult.TITLE,
        @ReportParamsKey("\$referrer") var referrer:String? = JGTrackManager.TrackSearchResult.TRACK_URL,
        @ReportParamsKey("search_sort_strategy_id") var search_sort_strategy_id: String? = "", //A/B策略ID
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("operation_id") var operation_id: String? = "", //运营位ID
        @ReportParamsKey("operation_rank") var operation_rank: Int? = null, //运营位位置
        @ReportParamsKey("list_position_type") var list_position_type: String? = "", //搜索坑位类型
        @ReportParamsKey("list_position_typename") var list_position_typename: String? = "", //搜索坑位类型名称
        @ReportParamsKey("product_id") var product_id: Long? = 0, //商品ID
        @ReportParamsKey("product_name") var product_name: String? = "", //	商品名称
        @ReportParamsKey("product_first") var product_first: String? = "", //商品一级分类
        @ReportParamsKey("product_price") var product_price: Double? = 0.0, //商品现价
        @ReportParamsKey("product_type") var product_type: String? = "", //商品类型
        @ReportParamsKey("product_activity_type") var product_activity_type: String? = "", //商品营销活动类型
        @ReportParamsKey("product_shop_code") var product_shop_code: String? = "", //商品店铺编码
        @ReportParamsKey("product_shop_name") var product_shop_name: String? = "", //	商品店铺名称
)

@ReportEventName("add_to_cart")
data class AddToCart(
        var jGPageListCommonBean: JGPageListCommonBean? = null,
        @ReportParamsKey("\$url") var url:String? = "",
        @ReportParamsKey("\$title") var title:String? = "",
        @ReportParamsKey("\$referrer") var referrer:String? = "",
        @ReportParamsKey("search_sort_strategy_id") var search_sort_strategy_id: String? = "", //A/B策略ID
        @ReportParamsKey("rank") var rank: Int? = 1,
        @ReportParamsKey("operation_id") var operation_id: String? = "", //运营位ID
        @ReportParamsKey("operation_rank") var operation_rank: Int? = null, //运营位位置
        @ReportParamsKey("list_position_type") var list_position_type: String? = "", //搜索坑位类型
        @ReportParamsKey("list_position_typename") var list_position_typename: String? = "", //搜索坑位类型名称
        @ReportParamsKey("product_id") var product_id: Long? = 0, //商品ID
        @ReportParamsKey("product_name") var product_name: String? = "", //	商品名称
        @ReportParamsKey("product_first") var product_first: String? = "", //商品一级分类
        @ReportParamsKey("product_number") var product_number: Int? = 0, //商品数量
        @ReportParamsKey("product_price") var product_price: Double? = 0.0, //商品现价
        @ReportParamsKey("product_type") var product_type: String? = "", //商品类型
        @ReportParamsKey("product_activity_type") var product_activity_type: String? = "", //商品营销活动类型
        @ReportParamsKey("product_shop_code") var product_shop_code: String? = "", //商品店铺编码
        @ReportParamsKey("product_shop_name") var product_shop_name: String? = "", //商品店铺名称
        @ReportParamsKey("direct") var direct: String? = "0", //例："0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
        @ReportParamsKey("add_cart_time") var add_cart_time: String? = transform2Date(), //加购时间

)