package com.ybmmarket20.utils

import android.content.Context
import com.ybmmarket20.common.AlertDialogEx

class AlertDialogHtml {

    companion object {

        @JvmStatic
        fun showAlertDialogAuthorization(context: Context, html: String, routerUrl: String?, callback: (router: String?) -> Unit) {
            AlertDialogEx(context)
                .setTitle("实名授权协议")
                .setHtml(true)
                .setMessage(html)
                .setCorner()
                .setCancelButton("我再想想", "#9494A5") { _, _ -> }
                .setConfirmButton("同意协议并继续") { _, _ ->
                    callback(routerUrl)
                }
                .show()
        }
    }
}