package com.ybmmarket20.view;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.view.View;
import android.widget.LinearLayout;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.util.ConvertUtils;

import java.util.List;


/**
 * 全部药品页的综合排序弹框
 */
public class AllGoodsPopWindowRanking extends ListFilterPopWindow {

    @Override
    public void show(View token) {
        setMarginUI();
        listView.setBackgroundResource(R.drawable.shape_all_goods_sort_pop_bg);
        listView.setPadding(1, 0, 1, 1);
        super.show(token);
    }

    public void setNewData(List<SearchFilterBean> list) {
        this.list = list;
        if (adapter != null) {
            setLayoutParams(ConvertUtils.dp2px(40) * list.size());
            adapter.setNewData(list);
        }
    }


    @Override
    protected Drawable getPopWindowBackgroundDrawable() {
        return new ColorDrawable(Color.parseColor("#00000000"));
    }

    /**
     * 应UI样式需求，全部药品页的综合排序样式和搜索等页面的综合排序样式略有区别，因此在此处改变设置marginLeft和marginRight
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR1)
    private void setMarginUI(){
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) listView.getLayoutParams();
        layoutParams.setMarginStart(ConvertUtils.dp2px(74));
//        layoutParams.setMarginEnd(ConvertUtils.dp2px(40));
    }
}
