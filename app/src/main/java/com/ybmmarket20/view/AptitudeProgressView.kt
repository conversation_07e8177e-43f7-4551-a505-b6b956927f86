package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapGridLayoutManager
import com.ybmmarket20.R

/**
 * 进度节点
 */
data class AptitudeProgressEntry(
    var selectedPosition: Int,
    var text: String?
) {
    constructor(text: String?): this(0, text)
}

/**
 * 资质进度
 */
class AptitudeProgressView(context: Context, attributeSet: AttributeSet) : RecyclerView(context, attributeSet) {

    var mEntrys: MutableList<AptitudeProgressEntry?>? = null
    var mAdapter: AptitudeProgresAdapter? = null
    var mSelectedPosition: Int = 0

    /**
     * 设置数据
     */
    fun setData(entrys: MutableList<AptitudeProgressEntry?>?) {
        if (mEntrys == null) mEntrys = mutableListOf()
        entrys?.apply {
            mEntrys?.clear()
            mEntrys?.addAll(entrys)
            layoutManager = WrapGridLayoutManager(context, mEntrys!!.size)
            mAdapter = AptitudeProgresAdapter(mEntrys)
            adapter = mAdapter
        }
    }

    /**
     * 设置选中位置
     */
    fun setSelected(position: Int) {
        if (mSelectedPosition != position) {
            mSelectedPosition = position
            mEntrys?.map {
                it?.selectedPosition = position
            }
            mAdapter?.notifyDataSetChanged()
        }
    }

    inner class AptitudeProgresAdapter(entrys: MutableList<AptitudeProgressEntry?>?)
        : YBMBaseAdapter<AptitudeProgressEntry>(R.layout.item_aptitude_pregress, entrys) {

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: AptitudeProgressEntry?) {
            t?.apply {
                val dot = baseViewHolder?.getView<View>(R.id.v_dot)
                val leftLine = baseViewHolder?.getView<View>(R.id.v_line_left)
                val rightLine = baseViewHolder?.getView<View>(R.id.v_line_right)
                val text = baseViewHolder?.getView<TextView>(R.id.tv_text)
                setStartAndEndItem(baseViewHolder, leftLine, rightLine)
                setAllItemStatus(baseViewHolder, leftLine, rightLine, dot, t)
                setText(baseViewHolder, text, t)
            }
        }

        /**
         * 设置开始节点和结束节点的线的显隐
         */
        private fun setStartAndEndItem(baseViewHolder: YBMBaseHolder?, leftLine: View?, rightLine: View?) {
            if (baseViewHolder?.layoutPosition == 0) {
                leftLine?.visibility = View.INVISIBLE
            } else {
                leftLine?.visibility = View.VISIBLE
            }
            if (baseViewHolder?.layoutPosition == itemCount - 1) {
                rightLine?.visibility = View.INVISIBLE
            } else {
                rightLine?.visibility = View.VISIBLE
            }
        }

        /**
         * 设置所有的Item状态
         */
        private fun setAllItemStatus(baseViewHolder: YBMBaseHolder?, leftLine: View?, rightLine: View?, dot: View?, t: AptitudeProgressEntry) {
            if (baseViewHolder?.layoutPosition?: 0 < t.selectedPosition) {
                //选中前的
                setDotStatus(dot, true)
                setLineStatus(leftLine, true)
                setLineStatus(rightLine, true)
            } else if(baseViewHolder?.layoutPosition?: 0 == t.selectedPosition) {
                //选中的
                setDotStatus(dot, true)
                setLineStatus(leftLine, true)
                setLineStatus(rightLine, false)
            } else {
                // 非选中状态
                setDotStatus(dot, false)
                setLineStatus(leftLine, false)
                setLineStatus(rightLine, false)
            }
        }

        /**
         * 设置圆点状态
         */
        private fun setDotStatus(view: View?, isSelected: Boolean) {
            if (view == null) return
            ContextCompat.getDrawable(mContext, if (isSelected) R.drawable.shape_aptitude_dot else R.drawable.shape_aptitude_dot_gray).let(view::setBackground)
        }

        /**
         * 设置线状态
         */
        private fun setLineStatus(view: View?, isSelected: Boolean) {
            if (view == null) return
            ContextCompat.getDrawable(mContext, if (isSelected) R.color.color_00b377 else R.color.colors_DDDDDD).let(view::setBackground)
        }

        /**
         * 设置文本及状态
         */
        private fun setText(baseViewHolder: YBMBaseHolder?, text: TextView?, t: AptitudeProgressEntry) {
            text?.text = t.text
            if (baseViewHolder?.layoutPosition?: 0 <= t.selectedPosition) {
                // 选中状态
                text?.setTextColor(ContextCompat.getColor(mContext, R.color.color_292933))
            } else {
                // 非选中状态
                text?.setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))
            }
        }

    }

}