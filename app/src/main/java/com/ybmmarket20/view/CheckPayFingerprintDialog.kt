package com.ybmmarket20.view

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity

/**
 * 验证指纹底部弹窗
 */
class CheckPayFingerprintDialog(val activity: BaseActivity) :
    ShowAptitudeBottomAddImageDialog(activity), View.OnClickListener {

    private var mUsePWCallback: (CheckPayFingerprintDialog.()->Unit)? = null
    private var mCheckFingerprintCallback: (CheckPayFingerprintDialog.()->Unit)? = null
    private var mClosePayPWDialogCallback: (() -> Unit)? = null

    override fun getLayoutId(): Int = R.layout.dialog_check_pay_fingerprint

    override fun initView() {
        super.initView()
        val ivBack = getView<ImageView>(R.id.ivBack)
        val tvUserPW = getView<TextView>(R.id.tvUserPW)
        val ivPayFingerprintBottom = getView<ImageView>(R.id.ivPayFingerprintBottom)
        ivBack.setOnClickListener(this)
        tvUserPW.setOnClickListener(this)
        ivPayFingerprintBottom.setOnClickListener(this)
    }

    fun setPayFingerprintTip(tip: String, textColor: String) {
        val tvPayFingerprintTip = getView<TextView>(R.id.tvPayFingerprintTip)
        tvPayFingerprintTip.text = tip
        tvPayFingerprintTip.setTextColor(Color.parseColor(textColor))
    }

    fun setUsePWCallback(usePWCallback: (CheckPayFingerprintDialog.()->Unit)?): CheckPayFingerprintDialog {
        mUsePWCallback = usePWCallback
        return this
    }

    fun setCheckFingerprint(checkFingerprintCallback: (CheckPayFingerprintDialog.()->Unit)?): CheckPayFingerprintDialog {
        mCheckFingerprintCallback = checkFingerprintCallback
        return this
    }

    fun setCloseCheckFingerprintDialog(closePayPWDialogCallback: (() -> Unit)?): CheckPayFingerprintDialog {
        mClosePayPWDialogCallback = closePayPWDialogCallback
        return this
    }

    override fun show() {
        super.show()
        mCheckFingerprintCallback?.invoke(this)
    }

    fun showAndCannotDismiss() {
        show()
        dialog.setCancelable(false)
        dialog.setCanceledOnTouchOutside(false)
    }

    override fun onClick(view: View?) {
        when(view?.id) {
            R.id.ivBack -> {
                dismiss()
                mClosePayPWDialogCallback?.invoke()
            }
            R.id.tvUserPW -> {
                mUsePWCallback?.invoke(this)
            }
            R.id.ivPayFingerprintBottom -> {
                mCheckFingerprintCallback?.invoke(this)
            }
        }
    }
}