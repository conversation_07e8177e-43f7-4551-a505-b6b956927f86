package com.ybmmarket20.view;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ClinicActivity;
import com.ybmmarket20.common.ResultListener;

/**
 * 诊所首页页面布局
 */

public class ClinicHomeLayout extends RecyclerRefreshLayout implements IBaseDynamicLayout{
    private DynamicHomeLayout homeLayout;
    private NoScrollview scrollview;
    public ClinicHomeLayout(Context context) {
        this(context,null);
    }

    public ClinicHomeLayout(final Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.common_dynamic_layout,this);
        homeLayout = (DynamicHomeLayout) findViewById(R.id.dhl_home);
        homeLayout.addIntercept(new IBaseDynamicIntercept() {
            @Override
            public boolean onInterceptAction(Uri uri) {
                if(uri !=null && uri.getHost().toLowerCase().contains("clinicactivity") && uri.getScheme().toLowerCase().contains("ybmaction")) {
                    String tab = uri.getQueryParameter("tab");
                    String index = uri.getQueryParameter("index");
                    if(TextUtils.isEmpty(index)){
                        index = "0";
                    }
                    if(TextUtils.isEmpty(tab) || TextUtils.isEmpty(index)){
                        return false;
                    }
                    if(context instanceof ClinicActivity){
                        try {
                            ((ClinicActivity) context).selTab(Integer.parseInt(tab),Integer.parseInt(index));
                            return true;
                        }catch (Exception e){
                            BugUtil.sendBug(e);
                        }
                    }

                }
                return false;
            }
        });
        scrollview = (NoScrollview) findViewById(R.id.sv_main);
        setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh() {
                getData(false);
            }
        });
    }


    public void setOnScrollListener(NoScrollview.OnScrollListener onScrollListener) {
        if(scrollview !=null){
            scrollview.setOnScrollListener(onScrollListener);
        }
    }

    public void setApi(String api) {
        homeLayout.setApi(api);
    }

    public final void getData(boolean show){
        if(homeLayout == null){
            return;
        }
        if(show){
            setRefreshing(true);
        }
        homeLayout.getNewData(new ResultListener<Integer>() {
            @Override
            public void result(boolean isSuccess, String errorMsg, Integer integer) {
                if(homeLayout == null){
                    return;
                }
                setRefreshing(false);
            }
        });
    }

    @Override
    public void onResume() {
        if(homeLayout !=null){
            homeLayout.onResume();
        }
    }

    @Override
    public void onPause() {
        if(homeLayout !=null){
            homeLayout.onPause();
        }
    }

    @Override
    public void onStop() {
        if(homeLayout !=null){
            homeLayout.onStop();
        }
    }

    @Override
    public void onRefresh() {
        if(homeLayout !=null){
            homeLayout.onRefresh();
        }
    }

    @Override
    public void onDestroy() {
        if(homeLayout !=null){
            homeLayout.onDestroy();
        }
    }

    public void fullScroll(int focusUp) {
        if(scrollview !=null){
            scrollview.fullScroll(focusUp);
        }
    }
}
