package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.luck.picture.lib.tools.ScreenUtils;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.NetUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.BigPicActivity;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.bean.ActivityTagBean;
import com.ybmmarket20.bean.ImagesVideosListBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.ybmmarket20.view.CommodityVideoView.VIDEO_COMMODITY_TYPE_FULL;

/**
 * 商品详情轮播图
 */

public class CommodityBannerLayout extends RelativeLayout {

    private ViewPager viewPager;
    private LinearLayout dotContainer;
    private LinearLayout ll;
    private TextView pager;
    private TextView pager2;
    private List<ImagesVideosListBean> items;
    private MyPagerAdapter adapter;
    private String mStrH;
    private ImageView mIvBrandMark;
    private PromotionTagView mPtv;

    private String url;
    private ProductDetailBean productDetailBean;
    public ViewGroup fullBg;
    public ViewGroup fullContainer;
    public List<CommodityVideoView> videos = new ArrayList<>();
    public String productId;
    public String productName;
    private String jgReferrer; // 页面来源

    public CommodityBannerLayout(Context context) {
        super(context);
        initData();
    }

    public CommodityBannerLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initData();
    }

    public CommodityBannerLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initData();
    }

    private void initData() {
        View.inflate(getContext(), R.layout.arl_arl, this);

        viewPager = (ViewPager) findViewById(R.id.vp_arl);
        dotContainer = (LinearLayout) findViewById(R.id.ll_arl);
        ll = (LinearLayout) findViewById(R.id.ll);
        pager = (TextView) findViewById(R.id.tv_pager);
        pager2 = (TextView) findViewById(R.id.tv_pager2);
        mIvBrandMark = (ImageView) findViewById(R.id.iv_brand_mark);
        //ll_activityTag = (ActivityTagView) findViewById(R.id.activity_tag);
        //ivActivityTag = (ImageView) findViewById(R.id.iv_activity_tag);
        mPtv = findViewById(R.id.view_ptv);
        mStrH = getResources().getString(R.string.detail_tv02);

        //获得viewpager的触摸事件
        viewPager.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        break;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return false;
            }

        });
    }

    public void setTagBg(ActivityTagBean activityTag){
        // 设置促销活动标签
        mPtv.setShowData(activityTag);
    }

    /**
     * 设置轮播图数据
     *
     * @param items 商品详情-视频实体类集合
     */
    public void setItemDataInVideo(List<ImagesVideosListBean> items, String url, ProductDetailBean productDetailBean) {
        if (mIvBrandMark == null) {
            return;
        }
        this.url = url;
        this.productDetailBean = productDetailBean;
//        //标签图片
//        if (TextUtils.isEmpty(url)) {
//            ImageHelper.with(getContext()).load(R.drawable.transparent).into(mIvBrandMark);
//        } else {
//            String markerUrl = url;
//            if (!url.startsWith("http")) {
//                markerUrl = AppNetConfig.LORD_TAG + url;
//            }
//            ImageHelper.with(getContext()).load(markerUrl)
//                    .placeholder(R.drawable.transparent).error(R.drawable.transparent)
//                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
//                    .into(mIvBrandMark);
//        }

        setItemDataInVideo(items);
    }

    /**
     * 设置轮播图数据
     *
     * @param items 商品详情-视频实体类集合
     */
    public void setItemDataInVideo(List<ImagesVideosListBean> items) {
        if (items == null || items.size() == 0) {
            return;
        }
        if (viewPager == null) {
            return;
        }
        this.items = items;
        // 处理viewpager
        adapter = new MyPagerAdapter();
        viewPager.setAdapter(adapter);
        // 处理textview
        viewPager.addOnPageChangeListener(PageListener);

        int nowPage = viewPager.getCurrentItem();
        ll.setBackgroundResource(R.drawable.bg_detail_pager_num_commodity);
        pager.setText(nowPage + "");
        pager2.setText("/" + items.size());
//        pager.setMinHeight(ConvertUtils.dp2px(23));
//        pager.setMinWidth(ConvertUtils.dp2px(44));
        ll.setMinimumHeight(ConvertUtils.dp2px(23));
        ll.setMinimumWidth(ConvertUtils.dp2px(44));

        // 移除上次遗留的所有点
//        dotContainer.removeAllViews();
        //重新添加点
//        addDots();
        viewPager.setOffscreenPageLimit(items.size() + 1);
        // 如果数据为空，onPageSelected(0)会出错
        PageListener.onPageSelected(0);
    }

    /*
     * 重新添加点
     * */
    private void addDots() {
        if (items == null) {
            return;
        }
        if (items.size() == 1) {
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            // 把10dp 转成对应的像素
            View view = new View(getContext());

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 5, getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotWidth);
            params.setMargins(0, 0, dotWidth, 0);
            view.setLayoutParams(params);
            // 指定背景是选择器，在pagechangelistener中只去改变状态，更加面向对象，易于控制
            view.setBackgroundResource(R.drawable.detail_arl_ball_bg_selector);
            dotContainer.addView(view);
        }
    }

    /*
     *  改变点的状态
     *  监听回调
     * */
    public ViewPager.OnPageChangeListener PageListener = new ViewPager.OnPageChangeListener() {

        @Override
        public void onPageSelected(int position) {
            position++;
            pager.setText(position + "");

            // 改变点的状态
//            int size = items.size();
//            if (size > 1) {
//                for (int i = 0; i < size; i++) {
//                    dotContainer.getChildAt(i).setEnabled(i == position % size);
//                }
//            }
            if (mOnPageChangeListener != null) {
                mOnPageChangeListener.onPageChange(position, mIsFlag);
            }
        }

        @Override
        public void onPageScrolled(int arg0, float arg1, int arg2) {

        }

        @Override
        public void onPageScrollStateChanged(int arg0) {

        }
    };

    public static class MarkVideoLayout extends RelativeLayout {
        private final ImageView mImageView;
        private final ImageView mMarkImage;

        public MarkVideoLayout(@NonNull @NotNull Context context) {
            super(context);
            mImageView = new ImageView(context);
            mImageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT
                    , RelativeLayout.LayoutParams.MATCH_PARENT);
            params.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
            this.addView(mImageView, params);

            mMarkImage = new ImageView(context);
            mMarkImage.setScaleType(ImageView.ScaleType.FIT_XY);
            params = new RelativeLayout.LayoutParams(ConvertUtils.dp2px(225)
                    , ConvertUtils.dp2px(225));
            params.leftMargin = ConvertUtils.dp2px(20);
            params.topMargin = ConvertUtils.dp2px(10);
//            params.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);

            this.addView(mMarkImage, params);
        }

        public ImageView imageView(){
            return this.mImageView;
        }

        public void setMarkImage(String url){
            //标签图片
            if (TextUtils.isEmpty(url)) {
                ImageHelper.with(getContext()).load(R.drawable.transparent).into(mMarkImage);
            } else {
                String markerUrl = url;
                if (!url.startsWith("http")) {
                    markerUrl = AppNetConfig.LORD_TAG + url;
                }
                ImageHelper.with(getContext()).load(markerUrl)
                        .placeholder(R.drawable.transparent).error(R.drawable.transparent)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                        .into(mMarkImage);
            }
        }

        /**
         * 设置高毛标签
         * @param productDetailBean
         */
        public void setSpellGroupGross(ProductDetailBean productDetailBean, MarkVideoLayout imageview) {
            if (productDetailBean != null && productDetailBean.highGrossGroupBuying == 1) {
                //显示高毛标签
                View grossTagView = View.inflate(getContext(), R.layout.layout_product_detail_gross_tag, null);
                TextView tvSpellGroupPrice = grossTagView.findViewById(R.id.tv_spell_group_price);
                TextView tvSuggestPrice = grossTagView.findViewById(R.id.tv_suggest_price);
                TextView tvGross = grossTagView.findViewById(R.id.tv_gross);
                View v_price_bg = grossTagView.findViewById(R.id.v_price_bg);
                String suggestPrice = productDetailBean.suggestPrice;
                if (suggestPrice == null || TextUtils.equals(suggestPrice, "")) {
                    suggestPrice = "";
                } else if(!suggestPrice.contains(".")) {
                    suggestPrice += ".00";
                } else if(suggestPrice.contains(".")){
                    String[] suggestPriceArray = suggestPrice.split("\\.");
                    if (suggestPriceArray[1].length() == 1) {
                        suggestPrice += "0";
                    }
                }

                tvSuggestPrice.setText("建议零售价￥" + suggestPrice);
                tvGross.setText("终端毛利率" + productDetailBean.grossMargin);
                if (TextUtils.isEmpty(suggestPrice) || TextUtils.isEmpty(productDetailBean.grossMargin)) {
                    v_price_bg.setVisibility(View.GONE);
                    tvSuggestPrice.setVisibility(View.GONE);
                    tvGross.setVisibility(View.GONE);
                }
                if(productDetailBean.actPtBean == null){
                    return; //某些情况下读不到活动，会出现异常，那就不要再添加高毛的显示框了
                }
                String assemblePrice = "";
                if (productDetailBean.actPtBean.isStepPrice()) {
                    assemblePrice = productDetailBean.actPtBean.getMinSkuPrice();
                } else {
                    assemblePrice = productDetailBean.actPtBean.assemblePrice + "";
                }
                if (!assemblePrice.contains(".")) {
                    assemblePrice += ".00";
                }
                String[] assemblePriceArray = assemblePrice.split("\\.");
                if (assemblePriceArray[1].length() == 1) {
                    assemblePrice += "0";
                }
                assemblePriceArray = assemblePrice.split("\\.");
                SpannableStringBuilder assemblePriceStr1 = new SpannableStringBuilder("￥");
                SpannableStringBuilder assemblePriceStr2 = new SpannableStringBuilder(assemblePriceArray[0]);
                assemblePriceStr2.append(".");
                SpannableStringBuilder assemblePriceStr3 = new SpannableStringBuilder(assemblePriceArray[1]);
                if (productDetailBean.actPtBean.isStepPrice()) {
                    assemblePriceStr3.append("起");
                }
                assemblePriceStr1.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), productDetailBean.actPtBean.isStepPrice()? 16: 23)), 0, assemblePriceStr1.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                assemblePriceStr2.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), productDetailBean.actPtBean.isStepPrice()? 26: 40)), 0, assemblePriceStr2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                assemblePriceStr3.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), productDetailBean.actPtBean.isStepPrice()? 16: 23)), 0, assemblePriceStr3.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                assemblePriceStr1.append(assemblePriceStr2).append(assemblePriceStr3);
                tvSpellGroupPrice.setText(assemblePriceStr1);
                addView(grossTagView);
                LayoutParams lp = (LayoutParams) grossTagView.getLayoutParams();
                lp.leftMargin = ScreenUtils.dip2px(getContext(), 24);
                lp.rightMargin = ScreenUtils.dip2px(getContext(), 24);
                lp.topMargin = ScreenUtils.dip2px(getContext(), 9);
                lp.bottomMargin = ScreenUtils.dip2px(getContext(), 6);
                grossTagView.setLayoutParams(lp);

                // 调整mImageView的宽度等于grossTagView，高度等比缩放，并居中显示
                post(new Runnable() {
                    @Override
                    public void run() {
                        // 获取grossTagView的高度
                        int grossTagViewHeight = grossTagView.getHeight()-ScreenUtils.dip2px(getContext(), 10);
                        if (grossTagViewHeight > 0) {
                            // 设置mImageView的布局参数
                            RelativeLayout.LayoutParams imageParams = new RelativeLayout.LayoutParams(
                                    RelativeLayout.LayoutParams.WRAP_CONTENT,
                                    grossTagViewHeight);
                            imageParams.addRule(RelativeLayout.CENTER_IN_PARENT);

                            // 设置mImageView的ScaleType为FIT_CENTER，保持宽高比并居中
                            imageview.mImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
                            imageview.mImageView.setLayoutParams(imageParams);
                        }
                    }
                });
            }
        }
    }


    public interface onPageChangeListener {
        void onPageChange(int position, boolean isFlag);
    }

    private onPageChangeListener mOnPageChangeListener = null;

    public void setOnPageChangeListener(onPageChangeListener listener) {
        this.mOnPageChangeListener = listener;
    }

    private boolean mIsFlag;

    private class MyPagerAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return items == null ? 0 : items.size();
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

        List<MarkVideoLayout> imageViews = new ArrayList<>();

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ImagesVideosListBean imagesVideosListBean = items.get(position);
            if (imagesVideosListBean.type == 2 && productDetailBean != null) {
                mIsFlag = true;
                FrameLayout bannerContainer = new FrameLayout(getContext());
                String url = AppNetConfig.LORD_IMAGESVIDEOS + imagesVideosListBean.videoUrl;
                CommodityVideoView videoPlayer = new CommodityVideoView(getContext());
                bannerContainer.addView(videoPlayer);
                container.addView(bannerContainer);
                videoPlayer.setUp(url, true, "");
                videoPlayer.setFullContainerBg(fullBg);
                videoPlayer.setMFullContainer(fullContainer);
                videoPlayer.setMBannerContainer(bannerContainer);
                videos.add(videoPlayer);
                MarkVideoLayout imageView = getMarkVideoLayout(imagesVideosListBean, position);
                videoPlayer.setThumbImageView(imageView);
                videoPlayer.setThumbPlay(true);
                videoPlayer.getStartButton().setVisibility(View.VISIBLE);
                //是否拼团进行中
                boolean isSpellGroup = false;
                if (productDetailBean.actPtBean == null) isSpellGroup = false;
                else if(productDetailBean.actPtBean.assembleStatus == 1) isSpellGroup = true;
                if (!isSpellGroup && NetUtil.isWifiConnected(getContext())) {
                    //自动播放
                    videoPlayer.startPlayLogic();
                    videoPlayer.setVoiceOpen(false);
                    HashMap<String, String> map = new HashMap<>();
                    map.put("commodityName", productName);
                    map.put("productId", productId);
                    XyyIoUtil.track("action_CommodityDetails_autoPlay", map);
                } else {
                    videoPlayer.setVoiceOpen(true);
                }
                try {
                    LifecycleOwner lifecycle = (FragmentActivity)getContext();
                    videoPlayer.setLifeCycle(lifecycle.getLifecycle());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                videoPlayer.setSwitchBlock(integer -> {
                    if (integer == VIDEO_COMMODITY_TYPE_FULL) {
                        HashMap<String, String> map = new HashMap<>();
                        map.put("commodityName", productName);
                        map.put("productId", productId);
                        XyyIoUtil.track("action_CommodityDetails_fullScreen", map);
                    }
                    return null;
                });
                videoPlayer.setStartClickBlock(() -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("commodityName", productName);
                    map.put("productId", productId);
                    XyyIoUtil.track("action_CommodityDetails_play", map);
                    return null;
                });
                return bannerContainer;
            }

            if (imageViews.isEmpty()) {
                MarkVideoLayout imageView = new MarkVideoLayout(getContext());
                imageViews.add(imageView);
            }
            final MarkVideoLayout imageView = imageViews.remove(0);
            setImageView(imageView.imageView(), imagesVideosListBean, position);
            container.addView(imageView);

            imageView.setTag(R.id.tag_action, position);
            imageView.setOnClickListener(itemClick);
            imageView.setMarkImage(CommodityBannerLayout.this.url);
            if (productDetailBean != null && productDetailBean.highGrossGroupBuying == 1 && position == 0 && !productDetailBean.isControlUnShowPrice()) {
                //显示高毛标签并且是第一张图&&非控销展示
                imageView.setSpellGroupGross(productDetailBean,imageView);
            }
            return imageView;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            // 把viewpager不使用的imageview放到缓存中
            imageViews.add((MarkVideoLayout) object);
            container.removeView((View) object);
        }

        private MarkVideoLayout getMarkVideoLayout(ImagesVideosListBean imagesVideosListBean, int position) {
            MarkVideoLayout imageView = new MarkVideoLayout(getContext());
            RelativeLayout.LayoutParams lp = (LayoutParams) imageView.imageView().getLayoutParams();
            imageView.imageView().setLayoutParams(lp);
            imageView.setBackgroundResource(R.color.white);
            setImageView(imageView.imageView(), imagesVideosListBean, position);
            imageView.setTag(R.id.tag_action, position);
            imageView.setMarkImage(CommodityBannerLayout.this.url);
            if (productDetailBean != null && productDetailBean.highGrossGroupBuying == 1 && position == 0 && !productDetailBean.isControlUnShowPrice()) {
                //显示高毛标签并且是第一张图&&非控销展示
                imageView.setSpellGroupGross(productDetailBean,imageView);
                if (productDetailBean.actPtBean != null) {
                    lp.leftMargin = ScreenUtils.dip2px(getContext(), 24);
                    lp.rightMargin = ScreenUtils.dip2px(getContext(), 24);
                    lp.topMargin = ScreenUtils.dip2px(getContext(), 12);
                    lp.bottomMargin = ScreenUtils.dip2px(getContext(), 6);
                }
            }
            return imageView;
        }
    }

    private ItemClick itemClick = new ItemClick();

    /*
     * 通用点击跳转
     * */
    private class ItemClick implements OnClickListener {
        @Override
        public void onClick(View v) {
            if (jgReferrer != null && !jgReferrer.isEmpty()) {
                JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getContext(), "头图", jgReferrer);
            }
            int action = (int) v.getTag(R.id.tag_action);
            List<String> strs = new ArrayList<>(items.size());
            for (int i = 0; i < items.size(); i++) {

                if (TextUtils.isEmpty(items.get(i).imageUrl) || items.get(i).type == 2) {
                    continue;
                }
                if (i == 0) {
                    if (!items.get(i).imageUrl.startsWith("http")) {
                        strs.add(AppNetConfig.LORD_BIDIMAGE + items.get(i).imageUrl);
                    } else {
                        strs.add(items.get(i).imageUrl);
                    }
                } else {
                    if (!items.get(i).imageUrl.startsWith("http")) {
                        if (mIsFlag && i == 1) {
                            strs.add(AppNetConfig.LORD_BIDIMAGE + items.get(i).imageUrl);
                        } else {
                            strs.add(AppNetConfig.LORD_PICIMAGE + items.get(i).imageUrl);
                        }
                    } else {
                        strs.add(items.get(i).imageUrl);
                    }
                }
            }

            if (items.get(action).type == 2) {
//                if (!TextUtils.isEmpty(items.get(action).videoUrl)) {
//                    String url = "ybmpage://playeractivity/" + items.get(action).videoUrl;
//                    RoutersUtils.open(url);
//                }
            } else {
                if (mIsFlag && action > 0) {
                    action--;
                }
                getContext().startActivity(BigPicActivity.getIntent(getContext()
                        , strs.toArray(new String[]{}), action, null, "大图"));
            }
        }
    }

    /**
     * 设置轮播图资源
     *
     * @param imageView            轮播图imageView
     * @param imagesVideosListBean 商品详情-视频实体类 type字段(1:图片；2视频)
     * @param position             当前轮播图list集合索引
     */
    public void setImageView(final ImageView imageView, ImagesVideosListBean imagesVideosListBean, int position) {
        try {
            String imageUrl = imagesVideosListBean.imageUrl;
            if (TextUtils.isEmpty(imageUrl)) {
                return;
            }
            if (position > 0) {
                String strUrl = "";
                if(imageUrl.startsWith("http")){
                    strUrl =  imageUrl;
                }
                else if (mIsFlag && position == 1) {
                    strUrl = AppNetConfig.LORD_BIDIMAGE + imageUrl;
                } else{
                    strUrl = AppNetConfig.LORD_PICIMAGE + imageUrl;
                }
                ImageHelper.with(getContext()).load(strUrl).placeholder
                                (R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .override(UiUtils.getScreenWidth(), AppUtilKt.getDp(300))
                        .dontAnimate().dontTransform().into(imageView);
            } else {
                if (imagesVideosListBean.type == 2) {

                    final String url_video = imageUrl.startsWith("http")? imageUrl : AppNetConfig.LORD_IMAGESVIDEOS + imageUrl;
                    SmartExecutorManager.getInstance().execute(new Runnable() {

                        @Override
                        public void run() {
                            final Bitmap mVideoThumbnail = UiUtils.getVideoThumbnail(url_video);
                            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                                @Override
                                public void run() {
                                    if (mVideoThumbnail != null) {
                                        imageView.setImageBitmap(mVideoThumbnail);
                                    }
                                }
                            });
                        }
                    });
                } else {
                    ImageHelper.with(getContext()).load(AppNetConfig.LORD_BIDIMAGE + imageUrl).placeholder
                                    (R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .override(UiUtils.getScreenWidth(), AppUtilKt.getDp(300))
                            .dontAnimate().dontTransform().into(imageView);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    public void releaseAll() {
        for (CommodityVideoView video : videos) {
            if (video != null) video.releaseAllVideos();
        }
    }

    public boolean onBackPress() {
        for (CommodityVideoView video : videos) {
            if (video.onBackPress()) return true;
        }
        return false;
    }

    public String getJgReferrer() {
        return jgReferrer;
    }

    public void setJgReferrer(String jgReferrer) {
        this.jgReferrer = jgReferrer;
    }
}
