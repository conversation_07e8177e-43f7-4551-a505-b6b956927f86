package com.ybmmarket20.view;

import android.content.Context;
import android.text.ClipboardManager;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.google.gson.Gson;
import com.ybm.app.bean.DeviceEntity;
import com.ybmmarket20.R;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.text.DecimalFormat;
import java.util.Iterator;

import butterknife.Bind;
import butterknife.ButterKnife;

/**
 * 一个底部边出来的popwindows 窗口容器
 * 使用：new LeftPopWindow(R.layout.view).show
 */

public class DebugDeviceWindow {
    @Bind(R.id.tv_diskMem)
    TextView tvDiskMem;
    @Bind(R.id.tv_sys)
    TextView tvSys;
    @Bind(R.id.tv_cpu)
    TextView tvCpu;
    @Bind(R.id.tv_sysMem)
    TextView tvSysMem;
    @Bind(R.id.tv_sysfreeMem)
    TextView tvSysfreeMem;
    @Bind(R.id.tv_appMem)
    TextView tvAppMem;
    @Bind(R.id.tv_appfreeMem)
    TextView tvAppfreeMem;
    @Bind(R.id.tv_product)
    TextView tvProduct;
    @Bind(R.id.tv_model)
    TextView tvModel;
    @Bind(R.id.ll_model)
    LinearLayout llModel;
    @Bind(R.id.btn_cancel)
    Button btnCancel;
    @Bind(R.id.btn_copy)
    Button btnCopy;
    private PopupWindow popwindow;
    private View contentView;


    public DebugDeviceWindow() {
        this.contentView = LayoutInflater.from(YBMAppLike.getAppContext()).inflate(R.layout.debug_device_pop, null);
        ButterKnife.bind(this, contentView);
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        popwindow = new PopupWindow(this.contentView,
                LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT, true);
        initPop();
    }

    private void initView(final DeviceEntity bena) {
        tvSys.setText("API:"+bena.api+" ROM:"+bena.os);
        tvCpu.setText(bena.cpu_core+"核 主频:"+formetHZ(bena.cpu_hz*1000l));
        tvSysMem.setText(formetSize(bena.sysMemory)+" W/H:"+ bena.width+"/"+bena.heigth);
        tvSysfreeMem.setText(formetSize(bena.sysfreeMemory)+"/"+formetSize(bena.sysMemory-bena.sysfreeMemory));
        tvAppMem.setText(formetSize(bena.appMemory)+"/"+formetSize(bena.appMaxMemory));
        tvAppfreeMem.setText(formetSize(bena.appfreeMemory)+"/"+formetSize(bena.appMemory-bena.appfreeMemory));
        tvDiskMem.setText(formetSize(bena.diskFreeMemory)+"/"+formetSize(bena.diskMemory- bena.diskFreeMemory)+"/"+formetSize(bena.diskMemory));
        tvProduct.setText(bena.device);
        tvModel.setText(bena.product+" / "+bena.model);
        btnCopy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ClipboardManager cm = (ClipboardManager) v.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
                cm.setText(new Gson().toJson(bena));
                ToastUtils.showShort("已经复制");
            }
        });
    }

    private void initPop() {
        popwindow.setAnimationStyle(R.style.mypopwindow_anim_style);
        popwindow.setFocusable(true);
        popwindow.setOutsideTouchable(false);
    }

    public void show(View token, DeviceEntity bean) {
        if (popwindow == null) {
            return;
        }
        initView(bean);
        try {
            if (popwindow.isShowing()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        try {
            popwindow.showAtLocation(token, Gravity.BOTTOM, 0, 0);
        } catch (Exception e) {
            return;
        }
        popwindow.update();
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }


    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }


    private void pasteJsonObject(JSONObject jsonResponse, ViewGroup parent, boolean isVISIBLE) {
        Iterator<String> iter = jsonResponse.keys();
        while (iter.hasNext()) {
            final String key = iter.next();
            try {
                Object value = jsonResponse.get(key);
                parent.addView(createView(value, key.toString() + " : ", parent, isVISIBLE));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void pasteJsonArray(JSONArray jsonResponse, ViewGroup parent, boolean isVISIBLE) {
        for (int i = 0; i < jsonResponse.length(); i++) {
            try {
                Object value = jsonResponse.get(i);
                parent.addView(createView(value, "[" + i + "]", parent, isVISIBLE));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private LinearLayout createView(Object value, String name, ViewGroup parent, boolean isVISIBLE) {
        LinearLayout cell = (LinearLayout) LayoutInflater.from(parent.getContext()).inflate(R.layout.debug_api_list_item, null);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(30, 0, 0, 0);
        cell.setLayoutParams(params);
        if (isVISIBLE) {
            cell.setVisibility(View.VISIBLE);
        } else {
            cell.setVisibility(View.GONE);
        }
        TextView cellTitle = (TextView) cell.findViewById(R.id.name);
        TextView cellType = (TextView) cell.findViewById(R.id.type);
        TextView cellVaule = (TextView) cell.findViewById(R.id.vaule);
        cellTitle.setText(name);
        if (value == null || value.toString().equalsIgnoreCase("NULL")) {
            cellType.setText("Null");
            cellVaule.setText("null");
        } else if (value instanceof JSONArray) {
            cellType.setText("Array");
            cellVaule.setText("长度：" + ((JSONArray) value).length());
            cellTitle.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    ViewGroup viewGroup = (ViewGroup) view.getParent().getParent();
                    boolean isOpen = (boolean) view.getTag();
                    if (isOpen) {
                        for (int a = 1; a < viewGroup.getChildCount(); a++) {
                            viewGroup.getChildAt(a).setVisibility(View.GONE);
                        }
                        ((TextView) viewGroup.findViewById(R.id.status)).setText("+");
                    } else {
                        for (int a = 1; a < viewGroup.getChildCount(); a++) {
                            viewGroup.getChildAt(a).setVisibility(View.VISIBLE);
                        }
                        ((TextView) viewGroup.findViewById(R.id.status)).setText("-");
                    }
                    view.setTag(!isOpen);
                }
            });
            cellTitle.setTag(false);
            ((TextView) cell.findViewById(R.id.status)).setText("+");
            final JSONArray finalValue = (JSONArray) value;
            pasteJsonArray(finalValue, cell, false);
        } else if (value instanceof JSONObject) {
            cellType.setText("Object");
            cellVaule.setText("");
            cellTitle.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    ViewGroup viewGroup = (ViewGroup) view.getParent().getParent();
                    boolean isOpen = (boolean) view.getTag();
                    if (isOpen) {
                        for (int a = 1; a < viewGroup.getChildCount(); a++) {
                            viewGroup.getChildAt(a).setVisibility(View.GONE);
                        }
                        ((TextView) viewGroup.findViewById(R.id.status)).setText("+");
                    } else {
                        for (int a = 1; a < viewGroup.getChildCount(); a++) {
                            viewGroup.getChildAt(a).setVisibility(View.VISIBLE);
                        }
                        ((TextView) viewGroup.findViewById(R.id.status)).setText("-");
                    }
                    view.setTag(!isOpen);
                }
            });
            cellTitle.setTag(false);
            ((TextView) cell.findViewById(R.id.status)).setText("+");
            final JSONObject finalValue = (JSONObject) value;
            pasteJsonObject(finalValue, cell, false);
        } else if (value instanceof String) {
            value = value.toString();
            cellType.setText("String");
            cellVaule.setText(value + "");
        } else if (value instanceof Boolean) {
            value = value.toString();
            cellType.setText("Boolean");
            cellVaule.setText(value + "");
        } else if (value instanceof Integer) {
            value = value.toString();
            cellType.setText("Integer");
            cellVaule.setText(value + "");
        } else if (value instanceof Long) {
            value = value.toString();
            cellType.setText("Long");
            cellVaule.setText(value + "");
        } else if (value instanceof Double) {
            value = value.toString();
            cellType.setText("Double");
            cellVaule.setText(value + "");
        } else {
            value = value.toString();
            cellType.setText("其它");
            cellVaule.setText(value + "");
        }

        return cell;
    }

    public String formetSize(long fileS) {
        DecimalFormat df = new DecimalFormat("#.0");
        String sizeString = "";
        if (fileS < 1024) {
            sizeString = df.format((double) fileS) + "B";
        } else if (fileS < 1048576) {
            sizeString = df.format((double) fileS / 1024) + "KB";
        } else if (fileS < 1073741824) {
            sizeString = df.format((double) fileS / 1048576) + "MB";
        } else {
            sizeString = df.format((double) fileS / 1073741824) + "GB";
        }
        return sizeString;
    }

    public String formetHZ(long fileS) {
        DecimalFormat df = new DecimalFormat("#.0");
        String sizeString = "";
        if (fileS < 1024) {
            sizeString = df.format((double) fileS) + "HZ";
        } else if (fileS < 1048576) {
            sizeString = df.format((double) fileS / 1024) + "KHZ";
        } else if (fileS < 1073741824) {
            sizeString = df.format((double) fileS / 1048576) + "MHZ";
        } else {
            sizeString = df.format((double) fileS / 1073741824) + "GHZ";
        }
        return sizeString;
    }

}
