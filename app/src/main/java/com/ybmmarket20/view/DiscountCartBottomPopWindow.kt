package com.ybmmarket20.view

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.DiscountItemBean
import com.ybmmarket20.bean.ExpandableItem
import com.ybmmarket20.bean.cart.CartDiscountDataBean
import com.ybmmarket20.utils.UiUtils


/**
 * <AUTHOR> Brin
 * @date : 2020/11/16 - 10:30
 * @Description :
 * @version
 */
class DiscountCartBottomPopWindow {


    var rvDiscount: RecyclerView?
    var ivClose: ImageView?
    var mAdapter: DiscountCartAdapter
    var discountData: CartDiscountDataBean
    var mPopupWindow: PopupWindow?
    var isShowing: Boolean? = false
        get() {
            return mPopupWindow?.isShowing
        }

    constructor(discountData: CartDiscountDataBean) {
        this.discountData = discountData
        var inflater = BaseYBMApp.getApp().currActivity?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        var contentView = inflater.inflate(R.layout.show_dicount_cart_pop, null, false)
        mPopupWindow = PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.dp2px(390)).also {
            it.isOutsideTouchable = false
            it.isFocusable = true
            it.setOnDismissListener {
                dismissWindowShadow()
            }
        }

        ivClose = getView(R.id.iv_close)
        ivClose?.setOnClickListener { dismiss() }
        rvDiscount = getView(R.id.rv_discount)

        var discountListData = arrayListOf<DiscountItemBean>()
        discountData?.promoDiscountGroupList?.forEach {
            discountListData.add(DiscountItemBean(title = it?.name, discountPrice = it?.discountAmount).apply { isGroup = true })
            it?.promoDiscountDetailList?.forEach {
                discountListData.add(DiscountItemBean(typeTitle = it?.name, discountPrice = it?.discountAmount))
            }
        }

        mAdapter = DiscountCartAdapter(discountListData)
        val headerView = View.inflate(BaseYBMApp.getApp().currActivity, R.layout.show_dicount_pop_header, null)
        headerView.findViewById<TextView>(R.id.tv_content).visibility = View.GONE
        headerView.findViewById<TextView>(R.id.tv_group_title).text = "商品总额"
        headerView.findViewById<TextView>(R.id.tv_product_price).setText("¥${discountData.subTotal}")

        val footerView = View.inflate(BaseYBMApp.getApp().currActivity, R.layout.show_dicount_cart_pop_footer, null)
        footerView.findViewById<TextView>(R.id.tv_total_discount_price_title).setText("共优惠")
        footerView.findViewById<TextView>(R.id.tv_total_discount_price).setText("-¥${discountData.totalDiscount}")
        mAdapter.addHeaderView(headerView)
        mAdapter.addFooterView(footerView)
        rvDiscount?.setLayoutManager(LinearLayoutManager(BaseYBMApp.getApp().currActivity))
        rvDiscount?.setAdapter(mAdapter)
    }

    fun <T : View?> getView(viewId: Int): T? {
        return if (mPopupWindow == null) {
            null
        } else try {
            mPopupWindow?.contentView?.findViewById<View>(viewId) as T
        } catch (e: Throwable) {
            null
        }
    }

    fun showAtLocation(mBottomBar: LinearLayout, gravity: Int, x: Int, y: Int) {
        mPopupWindow?.showAtLocation(mBottomBar, gravity, x, y)
        mPopupWindow?.let {
            showWindowShadow()
        }
    }

    fun dismiss() {
        mPopupWindow?.dismiss()
        dismissWindowShadow()
    }

    private fun showWindowShadow() {
        val lp: WindowManager.LayoutParams = BaseYBMApp.getApp().currActivity.getWindow().getAttributes()
        lp.alpha = 0.5f //设置透明度（这是窗体本身的透明度，非背景）
        BaseYBMApp.getApp().currActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND) //此行代码主要是解决在华为 红米米手机上半透明效果无效的bug
        BaseYBMApp.getApp().currActivity.getWindow().setAttributes(lp)
    }

    private fun dismissWindowShadow() {
        val lp: WindowManager.LayoutParams = BaseYBMApp.getApp().currActivity.getWindow().getAttributes()
        lp.alpha = 1f //设置透明度（这是窗体本身的透明度，非背景）
        BaseYBMApp.getApp().currActivity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND) //不移除该Flag的话,在有视频的页面上的视频会出现黑屏的bug
        BaseYBMApp.getApp().currActivity.getWindow().setAttributes(lp)
    }

    inner class DiscountCartAdapter :
        YBMBaseMultiItemAdapter<DiscountItemBean> {

        constructor(data: List<DiscountItemBean>) : super(data) {
            addItemType(ExpandableItem.TYPE_GROUP, R.layout.item_discount_group)
            addItemType(ExpandableItem.TYPE_CONTENT, R.layout.item_discount_child)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: DiscountItemBean) {
            when (t.itemType) {
                ExpandableItem.TYPE_GROUP -> binGroupItemView(baseViewHolder, t)
                ExpandableItem.TYPE_CONTENT -> binChildItemView(baseViewHolder, t)
            }
        }

        private fun binChildItemView(ybmBaseHolder: YBMBaseHolder, bean: DiscountItemBean) {
            ybmBaseHolder.setGone(R.id.tv_coupon_used_status, false)
            ybmBaseHolder.setText(R.id.tv_child_title, bean.typeTitle)
            ybmBaseHolder.setText(R.id.tv_child_price, "-¥${bean.discountPrice}")

        }

        private fun binGroupItemView(ybmBaseHolder: YBMBaseHolder, bean: DiscountItemBean) {
            ybmBaseHolder.setText(R.id.tv_group_title, bean.title)
            ybmBaseHolder.setText(R.id.tv_group_price, "-¥${bean.discountPrice}")
        }
    }
}