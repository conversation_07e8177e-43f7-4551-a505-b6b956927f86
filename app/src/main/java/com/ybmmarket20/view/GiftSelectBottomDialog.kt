package com.ybmmarket20.view

import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.material.tabs.TabLayout
import com.jeremyliao.liveeventbus.LiveEventBus
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.GiftSelectContentBean
import com.ybmmarket20.bean.GiftSelectStatus
import com.ybmmarket20.bean.GiftSelectTabBean
import com.ybmmarket20.bean.GiftSelectTabStatus
import com.ybmmarket20.bean.cart.CartBean
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.init
import com.ybmmarket20.fragments.GiftSelectFragment
import com.ybmmarket20.viewmodel.GiftSelectBottomDialogVM
import kotlinx.android.synthetic.main.dialog_gift_select_bottom_4.tab_layout
import kotlinx.android.synthetic.main.dialog_gift_select_bottom_4.viewpager

/**
 * @class   GiftSelectBottomDialogFragment
 * <AUTHOR>
 * @date  2024/9/11  选择赠品底部弹窗 目前三种样式
 *                  1、dialog_gift_select_bottom_1 recycleView选择的样式 -选择赠品
 *                  2、dialog_gift_select_bottom_2 fragment样式-选择赠品
 *                  3、dialog_gift_select_bottom_4 tabLayout+fragment样式 -选择赠品
 * @description
 */
class GiftSelectBottomDialog(val mContext: FragmentActivity, val mType: Int = GIFT_SELECT_TYPE_1) : DialogFragment() {

    companion object{

        const val GIFT_SELECT_TYPE_1 = 1 //recycleView选择的样式 -选择赠品
        const val GIFT_SELECT_TYPE_2 = 2 //fragment样式-选择赠品
        const val GIFT_SELECT_TYPE_4 = 4 //tabLayout+fragment样式 -选择赠品
        const val GIFT_SELECT_DEFAULT_TITLE = "选择赠品"

        const val GIFT_SELECT_TAB_STATUS_NO_SELECT = 1  //未选
        const val GIFT_SELECT_TAB_STATUS_SELECTED = 2 //已选
        const val GIFT_SELECT_TAB_STATUS_SELECTED_PART = 3 //部分选
    }

    var mTitle: String? = GIFT_SELECT_DEFAULT_TITLE
        set(value) {
            field = value
            val titleTextView: TextView? = dialog?.findViewById(R.id.title)
            titleTextView?.post {
                titleTextView.text = value
            }
        }

    var confirmClickCallBack: (()->Unit)? = null
    var closeCallBack: (()->Unit)? = null

    //tablayout+fragment用
    private var mFragments: ArrayList<GiftSelectFragment> = arrayListOf()
    private var tabList = arrayListOf<GiftSelectTabBean>()
    private var canSelectNumber = 0
    private var promoId = ""
    private var bizSource = 0

    var needToBePerfectedActList: ArrayList<CartBean.NeedToBePerfectedActBean>? = arrayListOf()

    constructor(mContext: FragmentActivity,needToBePerfectedActList: ArrayList<CartBean.NeedToBePerfectedActBean>?,bizSource: Int):this(mContext,GIFT_SELECT_TYPE_4){
        this.needToBePerfectedActList = needToBePerfectedActList
        this.bizSource = bizSource
    }

    constructor(mContext: FragmentActivity,canSelectNumber:Int,promoId:String,bizSource:Int):this(mContext,GIFT_SELECT_TYPE_2){
        this.canSelectNumber = canSelectNumber
        this.promoId = promoId
        this.bizSource = bizSource
    }

    fun show(){
        val oldFragment: GiftSelectFragment? = mContext.supportFragmentManager.findFragmentByTag("GiftSelectBottomDialogFragment") as? GiftSelectFragment
        if (oldFragment != null) {
            val ft: FragmentTransaction = mContext.supportFragmentManager.beginTransaction()
            ft.remove(oldFragment)
            ft.commit()
        }
        show(mContext.supportFragmentManager, "GiftSelectBottomDialogFragment")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        return View.inflate(mContext,getLayoutResID(),null)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setCancelable(false) // 设置点击屏幕Dialog不消失
        initObserver()
        setTitleContent(mTitle)
        handleUI()

        val window = dialog?.window
        val layoutParams = window?.attributes
        layoutParams?.gravity = Gravity.BOTTOM // 底部显示
        layoutParams?.width = LayoutParams.MATCH_PARENT
        when(mType){
            GIFT_SELECT_TYPE_1 -> {
                layoutParams?.height = LayoutParams.WRAP_CONTENT
            }

            GIFT_SELECT_TYPE_2,GIFT_SELECT_TYPE_4 -> {
                layoutParams?.height = 452.dp
            }

            else -> {
                layoutParams?.height = LayoutParams.WRAP_CONTENT
            }

        }
        window?.attributes = layoutParams
    }

    private fun initObserver(){
        dialog?.findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
            closeCallBack?.invoke()
            this.dismiss()
        }
    }

    private fun handleUI(){
        when(mType){
            GIFT_SELECT_TYPE_1 -> {
                handleUIType1()
            }

            GIFT_SELECT_TYPE_2 -> {
                handleUIType2()
            }

            GIFT_SELECT_TYPE_4->{
                handleUIType4()
            }

            else -> {}

        }
    }

    //RecycleView样式UI
    private fun handleUIType1(){
        //这种情况暂时没有 目前需求不要了
        // FIXME: 李江 测试代码
        val dataList = arrayListOf<GiftSelectContentBean>(
                GiftSelectContentBean("1","已购满10件，下单即可获取赠品"),
                GiftSelectContentBean("2","放弃当前赠品"),
                GiftSelectContentBean("3","测试不可以选择", MutableLiveData(GiftSelectStatus.CANT_SELECT))
        )
        val rvGiftSelect = dialog?.findViewById<RecyclerView>(R.id.rv_gift_select)?:return
        rvGiftSelect.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(mContext)
        rvGiftSelect.adapter = GiftSelectContentAdapter(dataList)

        dialog?.findViewById<TextView>(R.id.tv_confirm)?.setOnClickListener {
            confirmClickCallBack?.invoke()
            this.dismiss()
        }
    }

    //fragment UI 选择赠品
    private fun handleUIType2(){
        childFragmentManager.beginTransaction().add(R.id.framelayout_gift_select,GiftSelectFragment().apply {
            arguments = Bundle().apply {
                putInt(GiftSelectFragment.CAN_SELECT_NUMBER, canSelectNumber)
                putString(GiftSelectFragment.PROMO_ID, promoId)
                putInt(GiftSelectFragment.BIZ_SOURCE, bizSource)
            }
        }).commit()
        dialog?.findViewById<TextView>(R.id.tv_confirm)?.setOnClickListener {
            confirmClickCallBack?.invoke()
            this.dismiss()
        }
    }


    //tabLayout+fragment UI
    private fun handleUIType4(){

        tabList = (needToBePerfectedActList?.mapIndexed { index, it ->
            mFragments.add(GiftSelectFragment().apply {
                arguments = Bundle().apply {
                    putInt(GiftSelectFragment.CAN_SELECT_NUMBER, it.giftPoolActTotalSelectedNum)
                    putString(GiftSelectFragment.PROMO_ID, it.promoId ?:"")
                    putInt(GiftSelectFragment.BIZ_SOURCE, bizSource)
                    putInt(GiftSelectFragment.TAB_TAG_POSITION, index)
                }
            })
            GiftSelectTabBean(id = it.promoId,title = it.labTitle)
        } as? ArrayList<GiftSelectTabBean>) ?: arrayListOf()

//        tabList = arrayListOf(
//                GiftSelectTabBean(id = "1","满赠1", MutableLiveData(GiftSelectTabStatus.NO_SELECT)),
//                GiftSelectTabBean(id = "2","满赠测试2", MutableLiveData(GiftSelectTabStatus.SELECTED)),
//                GiftSelectTabBean(id = "3","满赠测试测试3", MutableLiveData(GiftSelectTabStatus.PART_SELECT)))
//        repeat(tabList.size) { mFragments.add(GiftSelectFragment()) }
        initTabLayout()
        dialog?.findViewById<TextView>(R.id.tv_confirm)?.setOnClickListener {
            confirmClickCallBack?.invoke()
            this.dismiss()
        }
    }


    fun setTitleContent(mTitle:String?){
        dialog?.findViewById<TextView>(R.id.tv_title)?.let {
            it.text = mTitle?:""
        }
    }


    fun getLayoutResID():Int{
        return when(mType){
            GIFT_SELECT_TYPE_1 -> R.layout.dialog_gift_select_bottom_1

            GIFT_SELECT_TYPE_2 -> R.layout.dialog_gift_select_bottom_2

            GIFT_SELECT_TYPE_4 -> R.layout.dialog_gift_select_bottom_4

            else -> R.layout.dialog_gift_select_bottom_1
        }
    }

    /**
     * 初始化tab
     * 不用TabLayoutMediatorExt，tabLayout单独监听
     */
    private fun initTabLayout() {
        viewpager.init(this, mFragments)
        viewpager.isUserInputEnabled = false
        val mediator =
                TabLayoutMediatorExt(tab_layout, viewpager, true, false) { tab, position ->
                    val mCustomView = LayoutInflater.from(dialog?.context).inflate(R.layout.gift_select_tab_type,tab_layout,false)
                    val tvTab = mCustomView.findViewById<TextView>(R.id.tv_tab)
                    val tvTags = mCustomView.findViewById<TextView>(R.id.tv_tags)
                    tvTab.text = tabList[position].title?:""
                    when(tabList[position].selectStatus.value){
                        GiftSelectTabStatus.SELECTED ->{
                            tvTags.isVisible = true
                            tvTags.text = "已选"
                            tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_selected)
                        }
                        GiftSelectTabStatus.PART_SELECT ->{
                            tvTags.isVisible = true
                            tvTags.text = "部分选"
                            tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_part_selected)
                        }
                        GiftSelectTabStatus.NO_SELECT ->{
                            tvTags.isVisible = true
                            tvTags.text = "未选"
                            tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_no_selected)
                        }
                        else ->{
                            tvTags.isVisible = false
                        }
                    }

                    tab.customView = mCustomView
                }
        tab_layout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                val tvTab = tab.customView?.findViewById<TextView>(R.id.tv_tab)
                tvTab?.typeface = Typeface.DEFAULT_BOLD
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                val tvTab = tab.customView?.findViewById<TextView>(R.id.tv_tab)
                tvTab?.typeface = Typeface.DEFAULT
            }

            override fun onTabReselected(tab: TabLayout.Tab) {

            }
        }).also {
            viewpager.adapter?.notifyDataSetChanged()
            mediator.attach()
        }
        tab_layout.setSelectedTabIndicator(R.drawable.gift_select_tab_indicator)
        tab_layout.setSelectedTabIndicatorHeight(4.dp)

        LiveEventBus.get(LiveEventBusManager.GiftSelect.TAB_TAG_GIFT_SELECT,Pair::class.java).observe(mContext) {
            it as Pair<Int,Int>
            val tabPosition = it.first
            if (tabPosition >=0 && tabPosition <= tab_layout.size){
                val mCustomView = tab_layout.getTabAt(tabPosition)?.customView?:return@observe
                val tvTags = mCustomView.findViewById<TextView>(R.id.tv_tags)?:return@observe
                when(it.second){
                    GIFT_SELECT_TAB_STATUS_SELECTED ->{
                        tvTags.isVisible = true
                        tvTags.text = "已选"
                        tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_selected)
                    }
                    GIFT_SELECT_TAB_STATUS_SELECTED_PART ->{
                        tvTags.isVisible = true
                        tvTags.text = "部分选"
                        tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_part_selected)
                    }
                    GIFT_SELECT_TAB_STATUS_NO_SELECT ->{
                        tvTags.isVisible = true
                        tvTags.text = "未选"
                        tvTags.setBackgroundResource(R.drawable.shape_tag_bg_gift_no_selected)
                    }
                    else ->{
                        tvTags.isVisible = false
                    }
                }
            }
        }
    }

    private class GiftSelectContentAdapter(dataList:ArrayList<GiftSelectContentBean>): YBMBaseAdapter<GiftSelectContentBean>(R.layout.item_gift_select_content,dataList) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, bean: GiftSelectContentBean?) {
            bean?:return
            baseViewHolder?.apply {
                val tvContent = itemView.findViewById<TextView>(R.id.tv_content)
                val ivSelect = itemView.findViewById<ImageView>(R.id.iv_select)
                tvContent.text = bean.mContent
                when(bean.selectStatus.value){
                    GiftSelectStatus.NO_SELECT ->{
                        Glide.with(mContext).load(R.drawable.icon_gift_no_select).into(ivSelect)
                        ivSelect.setOnClickListener {
                            singleSelect(baseViewHolder.adapterPosition,GiftSelectStatus.SELECTED)
                        }
                    }

                    GiftSelectStatus.SELECTED ->{
                        Glide.with(mContext).load(R.drawable.icon_gift_select).into(ivSelect)
                        ivSelect.setOnClickListener {
                            singleSelect(baseViewHolder.adapterPosition,GiftSelectStatus.NO_SELECT)
                        }
                    }

                    GiftSelectStatus.CANT_SELECT->{
                        Glide.with(mContext).load(R.drawable.icon_gift_cant_select).into(ivSelect)
                        ivSelect.setOnClickListener(null)
                    }

                    else -> {}
                }
            }

        }

        fun singleSelect(position:Int,targetStatus:GiftSelectStatus){
            mData?.forEachIndexed { index, data ->
                data as GiftSelectContentBean
                if (index == position){
                    data.selectStatus.value = targetStatus
                }else if (data.selectStatus.value != GiftSelectStatus.CANT_SELECT){
                    data.selectStatus.value = GiftSelectStatus.NO_SELECT
                }
            }
            notifyDataSetChanged()
        }

    }
}