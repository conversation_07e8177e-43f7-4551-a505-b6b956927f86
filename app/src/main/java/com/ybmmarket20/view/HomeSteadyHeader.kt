package com.ybmmarket20.view

import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.ybmmarket20.R

class HomeSteadyHeader(context: Context?, attrs: AttributeSet?) : LinearLayout(context, attrs), RefreshHeader {

    var tip: ImageView? = null
    var animationDrawable: AnimationDrawable? = null

    init {
        View.inflate(context, R.layout.layout_home_steady_header, this)
        tip = findViewById(R.id.iv_refresh)
    }

    override fun onStateChanged(refreshLayout: RefreshLayout, oldState: RefreshState, newState: RefreshState) {
        when(newState) {
            RefreshState.None,
            RefreshState.PullDownToRefresh
                -> {
                    tip?.setImageResource(R.drawable.sx_12)
                }
            RefreshState.ReleaseToRefresh -> {
                    tip?.setImageResource(R.drawable.sx_13)
            }
            RefreshState.Loading -> {

            }

            else -> {}
        }
    }

    override fun getView(): View = this

    override fun getSpinnerStyle(): SpinnerStyle = SpinnerStyle.Translate

    override fun setPrimaryColors(vararg colors: Int) {
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
    }

    override fun onMoving(isDragging: Boolean, percent: Float, offset: Int, height: Int, maxDragHeight: Int) {
    }

    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
    }

    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        tip?.setImageResource(R.drawable.ybmlib_loading_home_steady)
        animationDrawable = tip?.drawable as AnimationDrawable
        animationDrawable?.start()
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        animationDrawable?.stop()
        tip?.setImageResource(R.drawable.ybmlib_load_end)
        (tip?.drawable as AnimationDrawable).start()
        return 1000
    }

    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {
    }

    override fun isSupportHorizontalDrag(): Boolean = false
}