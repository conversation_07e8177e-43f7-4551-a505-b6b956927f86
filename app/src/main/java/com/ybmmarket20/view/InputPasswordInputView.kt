package com.ybmmarket20.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.bean.AbstractMutiItemEntity
import com.ybmmarket20.R
import com.ybmmarket20.view.homesteady.whenAllNotNull
import kotlinx.android.synthetic.main.view_input_password_input.view.*

//数字
const val INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM = 0
//删除
const val INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE = 1
//空白
const val INPUT_PASSWORD_INPUT_ITEM_TYPE_SPACE = 2

class InputPasswordInputView(context: Context, attr: AttributeSet?): ConstraintLayout(context, attr) {

    var mCallBack: ((type: Int, num: Int) -> Unit)? = null

    init {
        View.inflate(context, R.layout.view_input_password_input, this)
        rv.layoutManager = GridLayoutManager(context, 3)
        rv.addItemDecoration(InputPasswordInputDecoration())
        (0 until 12).map {
            if (it < 9) {
                InputPasswordInputItem(it + 1, INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM)
            } else if (it == 10) {
                InputPasswordInputItem(0, INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM)
            }  else if (it == 11) {
                InputPasswordInputItem(-1, INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE)
            } else {
                InputPasswordInputItem(-1, INPUT_PASSWORD_INPUT_ITEM_TYPE_SPACE)
            }
        }.let {
            rv.adapter = InputPasswordInputViewAdapter(it.toMutableList())
        }
    }

    fun setOnInputChange(callback: (type: Int, num: Int) -> Unit) {
        mCallBack = callback
    }

    inner class InputPasswordInputViewAdapter(list: MutableList<InputPasswordInputItem>):
        YBMBaseMultiItemAdapter<InputPasswordInputItem>(list) {

        init {
            addItemType(INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM, R.layout.item_input_password_input_word_num)
            addItemType(INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE, R.layout.item_input_password_input_word_delete)
            addItemType(INPUT_PASSWORD_INPUT_ITEM_TYPE_SPACE, R.layout.item_input_password_input_word_delete)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: InputPasswordInputItem?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->
                when(bean.itemType) {
                    INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM -> bindItemViewNum(holder, bean)
                    INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE -> bindItemViewDelete(holder, bean)
                    INPUT_PASSWORD_INPUT_ITEM_TYPE_SPACE -> bindItemViewSpace(holder, bean)
                }
            }
        }

        private fun bindItemViewNum(holder: YBMBaseHolder, bean: InputPasswordInputItem) {
            holder.setText(R.id.tvNum, "${bean.num}")
            holder.itemView.setOnClickListener {
                mCallBack?.invoke(bean.type, bean.num)
            }
        }

        private fun bindItemViewDelete(holder: YBMBaseHolder, bean: InputPasswordInputItem) {
            holder.itemView.setOnClickListener {
                mCallBack?.invoke(bean.type, -1)
            }
        }

        private fun bindItemViewSpace(holder: YBMBaseHolder, bean: InputPasswordInputItem) {
            holder.setVisible(R.id.tvDelete, false)
            holder.itemView.setOnClickListener {
                mCallBack?.invoke(bean.type, bean.num)
            }
        }

    }

    data class InputPasswordInputItem(
        val num: Int,
        val type: Int
    ): AbstractMutiItemEntity() {
        override fun getItemType(): Int {
            return type
        }
    }

    inner class InputPasswordInputDecoration: RecyclerView.ItemDecoration() {

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val dp = ScreenUtils.dip2px(context, 1f)
            val position = parent.getChildLayoutPosition(view)
            outRect.top = dp
            when (position % 3) {
                0 -> outRect.right = dp
                2 -> outRect.left = dp
            }
        }
    }
}