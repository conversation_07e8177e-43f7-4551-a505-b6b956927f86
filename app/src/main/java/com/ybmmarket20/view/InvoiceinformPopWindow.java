package com.ybmmarket20.view;

import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.Html;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.util.Linkify;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.InvoiceBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;


public class InvoiceinformPopWindow extends BaseBottomPopWindow {

    TextView tv;
    ImageView ivClose;
    TextView tvTitleClass;
    RadioButton rbBillElec;
    RadioButton rbBillOrdinary;
    TextView tvZzsText;
    CheckBox cb;
    TextView tvExplain;
    TextView tvExplain02;
    TextView tvFpTitle;
    TextView tvFpGs;
    TextView tvFpYdmc;
    TextView tvFpNsrsbh;
    TextView tvFpMemo;
    TextView tvBtnOk;
    LinearLayout llPeerType;
    ConstraintLayout layoutLoadError;
    LinearLayout llLayout;

    private String peerType = "0";//勾选状态，0未勾选 1已勾选
    private int billType = 1;//1:普通发票 2:专用发票 3:纸质普通发票 4:增值税电子专用发票

    private InvoiceBean bean;

    public void setPeerType(String peerType) {
        this.peerType = peerType;
    }

    public void setBillType(int billType) {
        if (billType <= 0) {
            billType = 0;
        }
        this.billType = billType;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.invoice_inform_pop;
    }

    @Override
    protected void initView() {
        tv = getView(R.id.tv);
        ivClose = getView(R.id.iv_close);
        tvTitleClass = getView(R.id.tv_title_class);
        rbBillElec = getView(R.id.rb_bill_elec);
        rbBillOrdinary = getView(R.id.rb_bill_ordinary);
        tvZzsText = getView(R.id.tv_zzs_text);
        cb = getView(R.id.cb);
        tvExplain = getView(R.id.tv_explain);
        tvExplain02 = getView(R.id.tv_explain_02);
        tvFpTitle = getView(R.id.tv_fp_title);
        tvFpGs = getView(R.id.tv_fp_gs);
        tvFpYdmc = getView(R.id.tv_fp_ydmc);
        tvFpNsrsbh = getView(R.id.tv_fp_nsrsbh);
        tvFpMemo = getView(R.id.tv_fp_memo);
        tvBtnOk = getView(R.id.tv_btn_ok);
        llPeerType = getView(R.id.ll_peer_type);
        layoutLoadError = getView(R.id.layout_load_error);
        llLayout = getView(R.id.ll_layout);

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        rbBillElec.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setChecked(true);
                billType = 1;
            }
        });
        rbBillOrdinary.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setChecked(false);
                cb.setChecked(false);
                peerType = "0";
                billType = 3;
            }
        });
        tv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.BILL_REULE);
            }
        });
        tvBtnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                peerType = cb.isChecked() ? "1" : "0";
                if (bean != null) {
                    if (bean.title.equals("增值税专用发票")) {
                        billType = 2;
                    }else if (bean.title.equals("增值税电子专用发票")){
                        billType = 4;
                    }
                }

                if (mOnSelectListener != null) {
                    mOnSelectListener.getValue(new SearchFilterBean(billType + "", peerType));
                }
                dismiss();
            }
        });
        layoutLoadError.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getData();
            }
        });

        getData();

    }

    private void getData() {
        if (tvTitleClass == null) {
            return;
        }
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.ORDER_INVOICE, params, new BaseResponse<InvoiceBean>() {

            @Override
            public void onSuccess(String content, BaseBean<InvoiceBean> data, InvoiceBean bean) {
                if (data != null && data.isSuccess() && bean != null) {
                    setData(bean);
                    llLayout.setVisibility(View.VISIBLE);
                    layoutLoadError.setVisibility(View.INVISIBLE);

                } else {
                    llLayout.setVisibility(View.INVISIBLE);
                    layoutLoadError.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                llLayout.setVisibility(View.INVISIBLE);
                layoutLoadError.setVisibility(View.VISIBLE);
            }
        });
    }

    private void setData(InvoiceBean bean) {
        if (tvTitleClass == null || bean == null) {
            return;
        }
        this.bean = bean;
        tvTitleClass.setText(bean.title);
        rbBillElec.setText(bean.zzsTitle);
        tvZzsText.setText(bean.zzsText);
        tvFpTitle.setText(bean.fpTitle);
        tvFpGs.setText(bean.fpGs);
        tvFpYdmc.setText(bean.fpYdmc + ":" + bean.fpYdmcValue);
        tvFpNsrsbh.setText(bean.fpNsrsbh + ":" + (!TextUtils.isEmpty(bean.fpNsrsbhValue) ? bean.fpNsrsbhValue : ""));
        tvFpMemo.setText(Html.fromHtml(bean.fpMemo));
        tvFpMemo.setAutoLinkMask(Linkify.ALL);
        tvFpMemo.setMovementMethod(LinkMovementMethod.getInstance());

        rbBillOrdinary.setVisibility(bean.ptzzDis.equals("1") ? View.VISIBLE : View.INVISIBLE);
        llPeerType.setVisibility(bean.peerType.equals("1") ? View.VISIBLE : View.GONE);

        rbBillElec.setChecked(billType == 1 || billType == 2);
        if (billType == 1) {
            setChecked(true);
        }
        rbBillOrdinary.setChecked(billType == 3);
        if (billType == 3 || billType == 2) {
            setChecked(false);
            cb.setClickable(false);
        }
    }

    private void setChecked(boolean b) {
        cb.setChecked(b);
        peerType = b ? "1" : "0";
    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight() - ConvertUtils.dp2px(120));
    }

}
