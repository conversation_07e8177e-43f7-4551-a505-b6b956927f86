package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.InputType
import android.text.SpannableStringBuilder
import android.text.Spanned.SPAN_INCLUSIVE_EXCLUSIVE
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.Group
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.getSingleStepSpannableForGoodsList
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.jgTrackProductDetailNewBtnClick
import com.ybmmarket20.common.jgTrackProductDetailNewBtnExposure
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ClickDelayUtil
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.DialogUtil.DialogClickListener
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.ListItemAddCartViewModel
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport
import com.ybmmarketkotlin.utils.tagStyle
import java.math.BigDecimal

/**
 * 普通品底部弹窗 列表加购底部弹窗
 */
class ListItemAddCartPopWindow(val activity: BaseActivity): BaseBottomPopWindow() {

    lateinit var ivGoods: ImageView
    lateinit var tvTitle: TextView
    lateinit var tvEffect: TextView
    lateinit var tvPrice: TextView
    lateinit var tvInventory: TextView
    lateinit var tvTotal: TextView
    lateinit var tvTips: TextView
    lateinit var tvNumber: TextView
    lateinit var ivNumSub: AppCompatImageView
    lateinit var ivNumAdd: AppCompatImageView
    lateinit var mShowToken: View
    lateinit var rowsBean: RowsBean
    lateinit var rtvSubmit: RoundTextView
    lateinit var tvPriceAfterDiscount: TextView
    lateinit var groupDiscount: Group
    lateinit var divider1: View
    lateinit var llDiscount: LinearLayout
    lateinit var tvDiscountTag: TextView
    lateinit var tvDiscountDescription: TextView

    var addCartCallback: ((Int) -> Unit)? = null

    var composePostHandler = Handler(Looper.getMainLooper())
    var composePostRunnable: Runnable? = null
    var mSId: String? = null
    var mSpId: String? = null
    var mSpType: String? = null
    var jgTrackBean:JgTrackBean? = null

    val viewModel: ListItemAddCartViewModel by lazy {
        ViewModelProvider(activity).get(ListItemAddCartViewModel::class.java)
    }
    val clickDelayUtil by lazy { ClickDelayUtil() }


    override fun getLayoutId(): Int = R.layout.popwindow_list_item_pay

    override fun getLayoutParams(): LinearLayout.LayoutParams {
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    override fun initView() {
        ivGoods = contentView.findViewById(R.id.ivGoods)
        tvTitle = contentView.findViewById(R.id.tvTitle)
        tvEffect = contentView.findViewById(R.id.tvEffect)
        tvInventory = contentView.findViewById(R.id.tvInventory)
        tvTotal = contentView.findViewById(R.id.tvTotal)
        tvTips = contentView.findViewById(R.id.tvTips)
        tvNumber = contentView.findViewById(R.id.tv_number)
        ivNumSub = contentView.findViewById(R.id.iv_numSub)
        ivNumAdd = contentView.findViewById(R.id.iv_numAdd)
        tvPrice = contentView.findViewById(R.id.tvPrice)
        rtvSubmit = contentView.findViewById(R.id.rtvSubmit)
        tvPriceAfterDiscount = contentView.findViewById(R.id.tvPriceAfterDiscount)
        groupDiscount = contentView.findViewById(R.id.groupDiscount)
        divider1 = contentView.findViewById(R.id.divider1)
        llDiscount = contentView.findViewById(R.id.llDiscount)
        tvDiscountTag = contentView.findViewById(R.id.tvDiscountTag)
        tvDiscountDescription = contentView.findViewById(R.id.tvDiscountDescription)
        rtvSubmit.setOnClickListener {
            try {
                AddCartPopupWindowReport.trackAddCartBtnClick(<EMAIL>, rowsBean, rtvSubmit.text?.toString(), 4)
                XyyIoUtil.track("action_AddShoppingCart", hashMapOf(
                    "commodityId" to rowsBean.id,
                    "sid" to mSId,
                    "spid" to mSpId,
                    "sptype" to mSpType
                ))
                val number = "${tvNumber.text}".toInt()
                addCartCallback?.invoke(number)
                dismiss()
            } catch (e: Exception) {
                e.printStackTrace()
            }

            it.context.jgTrackProductDetailNewBtnClick(
                    url = jgTrackBean?.url,
                    pageId = jgTrackBean?.pageId,
                    title  = jgTrackBean?.title,
                    btnName = rtvSubmit.text?.toString()?:"",
                    btnDesc = "底部弹窗",
                    jgReferrer = jgTrackBean?.jgReferrer,
                    rank = jgTrackBean?.rank?:1,//
                    productId =jgTrackBean?.productId?:"",
                    productType =jgTrackBean?.productType?:"",
                    operationId =jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                    operationRank =jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1,
                    module =jgTrackBean?.module?:"",
                    navigation1 =jgTrackBean?.navigation_1?:"",
                    navigation2 = jgTrackBean?.navigation_2?:""
                                                   )
        }
        tvNumber.setOnClickListener {
            //编辑弹出对话框加减数量
            DialogUtil.addOrSubDialog(
                tvNumber.context as BaseActivity,
                InputType.TYPE_CLASS_NUMBER,
                tvNumber.text.toString(),
                rowsBean.stepNum,
                rowsBean.isSplit == 1,
                true,
                object : DialogClickListener {
                    private var mImm: InputMethodManager? = null
                    override fun confirm(content: String) {
//                        calcTotalAmount(rowsBean, content.toInt())
                        getCartTrial("${rowsBean.id}", content, rowsBean.orgId, rowsBean.productPrice,rowsBean.jgTrackBean?.entrance?:"",rowsBean.jgTrackBean?.activityEntrance?:"", rowsBean.productType)
                    }

                    override fun cancel() {}
                    override fun showSoftInput(view: View) {
                        mImm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        mImm?.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
                    }
                })
        }
    }

    override fun show(token: View?) {
        super.show(token)
        rtvSubmit.let {

            if (it.isVisible){
                token?.context?.jgTrackProductDetailNewBtnExposure(
                        url = jgTrackBean?.url,
                        pageId = jgTrackBean?.pageId,
                        title  = jgTrackBean?.title,
                        btnName = it.text?.toString(),
                        btnDesc = if (jgTrackBean?.pageId == JGTrackManager.TrackProductDetail.PAGE_ID) "商详页" else "底部弹窗",
                        jgReferrer = jgTrackBean?.jgReferrer,
                        rank = jgTrackBean?.rank?:1,
                        productId =jgTrackBean?.productId?:"",
                        productType =jgTrackBean?.productType?:"",
                        operationId =jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                        operationRank =jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1,
                        module =jgTrackBean?.module?:"",
                        navigation1 =jgTrackBean?.navigation_1?:"",
                        navigation2 = jgTrackBean?.navigation_2?:""
                                                                  )
            }

        }
    }

    fun setData(rowsBean: RowsBean, showToken: View, num: String): ListItemAddCartPopWindow {
        this.rowsBean = rowsBean
        mShowToken = showToken
        ImageUtil.load(ivGoods.context, AppNetConfig.LORD_IMAGE + rowsBean.imageUrl, ivGoods)
        tvTitle.text = "${rowsBean.productName}/${rowsBean.spec}"
        tvEffect.text = "有效期:" + rowsBean.nearEffect
        tvInventory.text = "库存 " + (if (rowsBean.availableQty > 100) "大于100" else "" + rowsBean.availableQty);
        ivNumSub.setOnClickListener {
            val num = tvNumber.text.toString().trim()
            numOnClick(num, false, rowsBean)
        }
        ivNumAdd.setOnClickListener {
            val num = tvNumber.text.toString().trim()
            numOnClick(num, true, rowsBean)
        }
        tvNumber.text = "${rowsBean.mediumPackageNum}"
        tvTips.text = ""
        if (rowsBean.rangePriceBean.isStep()) {
            //阶梯价
            tvPrice.text = rowsBean.rangePriceBean.getSingleStepSpannableForGoodsList()
        } else {
            val builder = UiUtils.getPriceWithFormat("￥${rowsBean.productPrice}", 10)
            tvPrice.text = builder
        }
        val numTemp = if (TextUtils.isEmpty(num) || num == "0") {
            "${rowsBean.mediumPackageNum}"
        } else {
            num
        }
        val shopNum = numTemp.toInt()
//        calcTotalAmount(rowsBean, shopNum)
        viewModel.getAfterDiscount("${rowsBean.id}")
        getCartTrial("${rowsBean.id}", numTemp, rowsBean.orgId, rowsBean.productPrice,rowsBean.jgTrackBean?.entrance?:"",rowsBean.jgTrackBean?.activityEntrance?:"", rowsBean.productType)
        setDiscount()
        return this
    }

    /**
     * 设置优惠信息
     */
    private fun setDiscount() {
        //只有阶梯价并且有满降标签的的时候处理
        val isShowDiscount = rowsBean.rangePriceBean.isStep() && rowsBean.tags?.manJiangTag != null
        groupDiscount.visibility = if (isShowDiscount) View.VISIBLE else View.GONE
        if (!isShowDiscount) return
        tvDiscountTag.tagStyle(rowsBean.tags?.manJiangTag)
        tvDiscountDescription.text = rowsBean.tags?.manJiangTag?.description
    }

    private fun numOnClick(num: String, isAdd: Boolean, rowsBean: RowsBean) {
        val isSplit = rowsBean.isSplit == 1
        val mediumPackageNum = rowsBean.mediumPackageNum
        //获取商品的数量
        var shopNum: Int = try {
            Integer.valueOf(num)
        } catch (ex: Exception) {
            0
        }
        if (!isAdd) {
            if (isSplit) {
                shopNum--
            } else {
                shopNum -= mediumPackageNum
            }
        } else {
            shopNum += mediumPackageNum
        }
        if (shopNum > 9999 || shopNum < 0) {
            return
        }
//        calcTotalAmount(rowsBean, shopNum)
        getCartTrial("${rowsBean.id}", "$shopNum", rowsBean.orgId, rowsBean.productPrice,rowsBean.jgTrackBean?.entrance?:"",rowsBean.jgTrackBean?.activityEntrance?:"", rowsBean.productType)
    }

    /**
     * 获取运费
     */
    private fun getCartTrial(skuId: String, quantity: String, orgId: String, productPrice: String,entrance:String,activityEntrance:String, productType : Int) {
        clickDelayUtil.checkClick(true) { count: Int?, aBoolean: Boolean ->
            if (aBoolean) {
                composePostRunnable?.let { composePostHandler.removeCallbacks(it) }
                composePostRunnable = Runnable {
                    clickDelayUtil.pushTask {
                        viewModel.addCartTrial(skuId, quantity, orgId, productPrice,entrance,activityEntrance, productType)
                    }
                }
                composePostHandler.postDelayed(composePostRunnable!!, 300)
            } else {
                val goodNumber = quantity.toIntOrNull()?:1
                val subTotal = (productPrice.toDoubleOrNull()?:0.00)* goodNumber
                formatTotalAmount(subTotal.toString(), goodNumber)
            }

        }
    }

    /**
     * 计算合计
     */
    private fun calcTotalAmount(rowsBean: RowsBean, num: Int) {
        val totalAmountStr = BigDecimal(rowsBean.getFob()).multiply(BigDecimal(num)).toString()
        formatTotalAmount(totalAmountStr, num)
    }

    /**
     * 格式化金额
     */
    private fun formatTotalAmount(totalAmountStr: String, num: Int) {
        val builder = SpannableStringBuilder("合计：")
        val totalSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(13f))
        val totalSpanColor = ForegroundColorSpan(Color.parseColor("#333333"))
        builder.setSpan(totalSpan, 0, builder.length, SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(totalSpanColor, 0, builder.length, SPAN_INCLUSIVE_EXCLUSIVE)
        var totalAmountBuilder = SpannableStringBuilder(totalAmountStr)
        if (!TextUtils.isEmpty(totalAmountStr)) {
            totalAmountBuilder = UiUtils.getPriceWithFormat("￥" + UiUtils.transform(totalAmountStr), 10)
        }
        builder.append(totalAmountBuilder)
        tvTotal.text = builder

        if (num != -1) {
            tvNumber.text = "$num"
        }
    }

    fun setObserver():ListItemAddCartPopWindow {
        if (viewModel.listItemAddCartLiveData.hasObservers()) {
            viewModel.listItemAddCartLiveData.removeObservers(activity)
        }
        //运费
        viewModel.listItemAddCartLiveData.observe(activity) {
            activity.dismissProgress()
            if (it.isSuccess) {
                try {
                    tvTips.text = Html.fromHtml(it.data.freightTips?:"")
                    formatTotalAmount(it.data.totalAmount?: "0.00", (it.data.num?: "1").toInt())
                    it.data.message?.let { msg -> ToastUtils.showShort(msg) }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        //折后价
        viewModel.afterDiscountLiveData.observe(activity) {
            val afterDiscountPriceText = if (it.price.isNullOrEmpty()) "" else it.price
            tvPriceAfterDiscount.text = afterDiscountPriceText
        }
        return this
    }
}