package com.ybmmarket20.view

import android.content.Context
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.core.view.isVisible
import com.lxj.xpopup.core.BottomPopupView
import com.lxj.xpopup.util.XPopupUtils
import com.ybmmarket20.R
import kotlinx.android.synthetic.main.pop_layout_pay_tips.view.cb_check_disclaimer
import kotlinx.android.synthetic.main.pop_layout_pay_tips.view.group
import kotlinx.android.synthetic.main.pop_layout_pay_tips.view.ivClose
import kotlinx.android.synthetic.main.pop_layout_pay_tips.view.tv_confirm
import kotlinx.android.synthetic.main.pop_layout_pay_tips.view.tv_content


/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/4/11 11:00
 *    desc   :
 */
class PayTipsPopWindow(
    var mContext: Context,
    var contentStr: String?,
    var confirmStr: String?,
    var canCheck: Boolean,
    private var confirmCallBack: () -> Unit?
) : BottomPopupView(mContext) {
    override fun getImplLayoutId() = R.layout.pop_layout_pay_tips
    override fun onCreate() {
        if (contentStr.isNullOrEmpty()) {
            tv_content?.visibility = View.GONE
        } else {
            tv_content?.visibility = View.VISIBLE
            tv_content?.text = contentStr
        }
        if (confirmStr.isNullOrEmpty()) {
            tv_confirm?.visibility = View.GONE
        } else {
            tv_confirm?.visibility = View.VISIBLE
            tv_confirm?.text = confirmStr
        }
        cb_check_disclaimer.setOnCheckedChangeListener { buttonView, isChecked ->
            group.isVisible = !isChecked
        }
        cb_check_disclaimer.isChecked = !canCheck
        cb_check_disclaimer.setEnabled(canCheck)
        ivClose?.setOnClickListener {
            dismiss()
        }
        tv_confirm?.setOnClickListener {
            if (cb_check_disclaimer.isChecked) {
                confirmCallBack.invoke()
                dismiss()
            } else {
                val shakeAnimation: Animation = AnimationUtils.loadAnimation(
                    mContext, R.anim.rotate_15
                )
                group.startAnimation(shakeAnimation)
            }
        }
    }

    override fun getMaxWidth(): Int {
        return XPopupUtils.getWindowWidth(mContext)
    }

    override fun getMaxHeight(): Int {
        return XPopupUtils.dp2px(mContext, 640f)
    }
}