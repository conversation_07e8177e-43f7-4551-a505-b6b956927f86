package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import android.text.Html;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.PayWayBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.widget.RoundTextView;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付方式选择
 */

public class PayTypeLayout extends LinearLayout {
    private MyListener listener;
    private List<CheckBox> checkBoxList = new ArrayList<>();
    private List<PayWayBean> dataList;
    public PayTypeLayout(Context context) {
        super(context);
        initViews();
    }

    public PayTypeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initViews();
    }

    public PayTypeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    private void initViews() {
        setOrientation(VERTICAL);

    }

    private void createdAndAdd(final PayWayBean bean, final int position){
        LinearLayout llPayway = (LinearLayout) LayoutInflater.from(getContext()).inflate(R.layout.layout_pay_type_item,this,false);
        final CheckBox cb = (CheckBox) llPayway.findViewById(R.id.cb);
        TextView tvTips = (TextView) llPayway.findViewById(R.id.tv_tips);
        TextView tvPayWay = (TextView) llPayway.findViewById(R.id.tv_payway);
        RoundTextView rtvDes = llPayway.findViewById(R.id.rtvDes);
        cb.setChecked(bean.checked);
        checkBoxList.add(cb);
        tvPayWay.setText(bean.payway);
        tvTips.setText(Html.fromHtml(bean.tips!=null?bean.tips:""));
        int color = getColor(bean.color);
        if(color !=0){
            tvTips.setTextColor(color);
        }
        rtvDes.setVisibility(TextUtils.isEmpty(bean.tipsDesc)? View.GONE: View.VISIBLE);
        rtvDes.setText(bean.tipsDesc);
        llPayway.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                for(CheckBox checkBox:checkBoxList){
                    checkBox.setChecked(false);
                }
                cb.setChecked(true);
                if(listener !=null){
                    listener.onClick(v,bean,position);
                }
                if(!TextUtils.isEmpty(bean.msg)){
                    showDialog(bean.msg);
                }
            }
        });
        if (bean.payType==1 || bean.payType == 5){//在线支付 显示问号提示
            Drawable drawable = ContextCompat.getDrawable(getContext(), R.drawable.icon_freight_tip);
            if (drawable != null) drawable.setBounds(0, 0, ConvertUtils.dp2px(11), ConvertUtils.dp2px(11));
            tvPayWay.setCompoundDrawables(null,null, drawable,null);
            tvPayWay.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    showPayTypeTips(bean.payType);
                }
            });
        }
        addView(llPayway);
    }

    private void showPayTypeTips(int payType) {
        AlertDialogEx dialogEx = new AlertDialogEx(getContext());
        dialogEx.setTitle("温馨提示")
                .setMessage(payType == 1?"建议您选择企业支付宝、企业微信、企业网银进行付款": "1.小药药自有账期仅支持购买控销及自营仓商品。\n2.还款后需财务核款，会导致额度未及时返还或逾期未清的场景，该类问题可联系专属销售处理。")
                .setConfirmButton("我知道啦", (dialog, button) -> {
                })
                .setMessageGravity(Gravity.CENTER)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .show();
    }

    private void showDialog(String msg){
        AlertDialogEx dialogEx = new AlertDialogEx(getContext());
        dialogEx.setMessage(msg).setCancelButton("我知道了",null).show();
    }


    private int getColor(String color) {
        if(TextUtils.isEmpty(color)){
            return 0;
        }
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }

    public void setListener(MyListener listener){
        this.listener = listener;
    }

    public interface MyListener{
        void onClick(View view,PayWayBean bean,int position);
    }

    public void setData(List<PayWayBean> data) {
        dataList = data;
        checkBoxList.clear();
        if (data == null || data.isEmpty()) {
            this.setVisibility(View.GONE);
            return;
        }
        setVisibility(View.VISIBLE);
        removeAllViews();
        for (int a = 0; a < data.size(); a++) {
            if(data.get(a) !=null){
               createdAndAdd(data.get(a),a);
            }
        }
    }
}
