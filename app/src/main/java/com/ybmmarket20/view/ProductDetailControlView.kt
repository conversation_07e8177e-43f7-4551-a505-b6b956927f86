package com.ybmmarket20.view

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.luck.picture.lib.tools.ScreenUtils
import com.luck.picture.lib.tools.StringUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetailBean
import com.ybmmarket20.common.AlertDialogEx
import kotlinx.android.synthetic.main.view_product_detail_control.view.*

/**
 * 商品详情控销
 */
class ProductDetailControlView(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs) {

    init {
        View.inflate(context, R.layout.view_product_detail_control, this)
    }

    /**
     * 设置控销数据
     */
    fun setControlData(productDetail: ProductDetailBean, isSpellGroup: Boolean) {
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 签署协议后可见 原本文字后的小感叹号以及点击后出现的弹窗，均保留 文字 “签署协议后可见” 替换为商品价格；
        if (productDetail.controlType == 5) {
            visibility = View.GONE
            return
        }
        if (TextUtils.isEmpty(productDetail.controlTitle)) {
            visibility = View.GONE
            return
        } else {
            visibility = View.VISIBLE
        }
        if (isSpellGroup) {
            //拼团
            cl_product_aptitude_control.visibility = View.VISIBLE
            tv_control_aptitude.text = productDetail.controlTitle
            tv_product_control.visibility = View.GONE
            setPriceGoneReasonIcon(tv_control_aptitude, productDetail)
        } else {
            cl_product_aptitude_control.visibility = View.GONE
            tv_product_control.visibility = View.VISIBLE
            tv_product_control.text = productDetail.controlTitle
            setPriceGoneReasonIcon(tv_product_control, productDetail)
        }
    }

    /**
     * 设置图标和点击事件
     */
    private fun setPriceGoneReasonIcon(tv: TextView, productDetail: ProductDetailBean) {
        val isShowIcon = !TextUtils.isEmpty(productDetail.controlNotes)
        val drawable: Drawable? = if (isShowIcon) ContextCompat.getDrawable(
            context, R.drawable.icon_product_detail_tips_yellow
        ) else null
        tv.compoundDrawablePadding = ScreenUtils.dip2px(context, 4f)
        StringUtils.modifyTextViewDrawable(tv, drawable, 2)
        tv.setOnClickListener {
            if (isShowIcon) {
                AlertDialogEx(context)
                    .setMessage(productDetail.controlNotes)
                    .setConfirmButton("知道了",
                        AlertDialogEx.OnClickListener { dialog: AlertDialogEx, _: Int -> dialog.dismiss() })
                    .show()
            }
        }
    }

}