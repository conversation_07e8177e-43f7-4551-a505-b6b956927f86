package com.ybmmarket20.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.RotateAnimation;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartDataBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick;
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JgOperationPositionInfo;
import com.ybmmarket20.bean.JgRequestParams;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarket20.utils.ControlGoodsDialogUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.productEdit.IProductEditAnalysis;
import com.ybmmarket20.view.productEdit.ProductEditAnalysis;
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport;
import com.ybmmarket20.xyyreport.page.order.ChangeCartUtil;
import com.ydmmarket.report.ReportManager;

import java.util.Objects;

/**
 * 商品增加、减少、输入布局
 */
public class ProductEditLayoutNew extends FrameLayout {
    private long lastTime;
    protected static RequestParams editShopNumberParams;
    public final static int DIFF_TIME = 400;
    private int ADD_STEP = 10;//加号处理
    private boolean split = true;//可拆开卖
    private boolean needHide = true;//true 可以收起 false不能收起
    protected ImageView iv_numSub;
    protected ImageView iv_numAdd;
    protected TextView tv_number;
    protected int number;
    protected int fromPage;
    public final static int FROMPAGE_ACTIVITS = 0;//活动页面，收藏，常用采购，商品的二级页面
    public final static int FROMPAGE_ALL_PRODUCT = 1;//全部药品
    public final static int FROMPAGE_CART = 2;//采购单
    public final static int FROMPAGE_COMMODITY = 3;//商品详情
    public final static int FROMPAGE_HOME = 4;//首页
    public final static int FROMPAGE_CART_RECOMMEND = 5;//购物车为你推荐页
    public final static int FROMPAGE_TV_LIVE_List = 6;//直播间商品列表页
    public final static int FROMPAGE_TV_LIVE_DIALOG = 7;//直播间商品列表页
    public final static int FROMPAGE_DEFAULT = 8;
    private long proId;
    private boolean isPackage;
    private int height;
    private ImageView animView;
    protected ViewGroup rootView;
    private boolean modifyViewStyle = false;
    protected FrameLayout fl_root_view;
    private boolean animationEnable = true;
    private AddCartListener mAddCartListener;
    private DelProductListener delProductListener;
    private FoldingListener mFoldingListener;
    private boolean hadFolded;

    private String sourceId;
    private String categoryId;
    // 按钮类型-减
    public static final String BUTTON_TYPE_REDUCE = "1";
    // 按钮类型-输入
    public static final String BUTTON_TYPE_INPUT = "2";
    // 按钮类型-加
    public static final String BUTTON_TYPE_ADD= "3";

    public String mSource;
    public String mIndex;

    public boolean isAddCartShowPopupWindow;
    private int showPopupWindowResultNum;
    public RowsBean rowsBean;

    public String mSId;
    public String mSpId;
    public String mSpType;
    public int rowsBeanPosition;

    public JgTrackBean jgTrackBean = null;
    private OnNumberListener mOnNumberListener;
    public JGPageListCommonBean jgPageListCommonBean = null;
    public IProductEditAnalysis mProductEditAnalysis = new ProductEditAnalysis();

    public ProductEditLayoutNew(Context context) {
        this(context, null);
    }

    public ProductEditLayoutNew(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductEditLayoutNew(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    public void initViews() {
        View view = View.inflate(getContext(), R.layout.product_edit_layout_new, this);
        iv_numSub = view.findViewById(R.id.iv_numSub);
        iv_numAdd = view.findViewById(R.id.iv_numAdd);
        tv_number = view.findViewById(R.id.tv_number);
        fl_root_view = view.findViewById(R.id.rl_layout);
        tv_number.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null) {
                    if (s.length() <= 3) {
                        tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
                    }
                    if (s.length() >= 4) {
                        tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    public int getProductNum(){
        return number;
    }

    public void setOnNumberListener(OnNumberListener numberListener){
        mOnNumberListener = numberListener;
    }

    public interface OnNumberListener{
        void onClick(View view,int number);
    }

    /**
     * 最小的状态，使整个控件最小
     */
    private void toMin() {
        iv_numSub.setVisibility(GONE);
        tv_number.setVisibility(GONE);
        iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
        fl_root_view.getLayoutParams().width = FrameLayout.LayoutParams.WRAP_CONTENT;
        if (mFoldingListener != null) {
            mFoldingListener.onFolding(true);
        }
    }

    private void toMax() {
        ViewGroup.LayoutParams layoutParams = (LayoutParams) fl_root_view.getLayoutParams();
        layoutParams.width = ConvertUtils.dp2px(75);
        fl_root_view.setLayoutParams(layoutParams);
        iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
        iv_numSub.setVisibility(VISIBLE);
        tv_number.setVisibility(VISIBLE);
        if (mFoldingListener != null) {
            mFoldingListener.onFolding(false);
        }
    }

    /**
     * @param skuId
     * @param status
     * @param isBuy
     * @param fromPage
     * @param animView
     * @param needHide 当为0时或者小于0，减号那部分是否需要隐藏
     * @param step
     * @param split
     */
    public void bindData(final long skuId, final int status, final boolean isBuy, int fromPage, ImageView animView, boolean needHide, int step, boolean split) {
        bindData(skuId, status, isBuy, fromPage, needHide, step, split);
        this.animView = animView;
    }

    //套餐商品专用

    /**
     * @param skuId
     * @param status
     * @param isBuy
     * @param fromPage
     * @param needHide 当为0时或者小于0，减号那部分是否需要隐藏
     * @param step
     * @param split
     */
    public void bindData(final long skuId, final int status, final boolean isBuy, int fromPage, boolean needHide, int step, final boolean split) {
        this.fromPage = fromPage;
        this.proId = skuId;
        this.split = split;
        this.needHide = needHide;
        if (step <= 0) {
            step = 1;
        }
        ADD_STEP = step;
        if (isBuy) {
            tv_number.setInputType(InputType.TYPE_NULL);
            //初始化商品数量
            tv_number.setTag(R.id.tag_3, false);
            tv_number.setText(String.valueOf(0));
            tv_number.setTextColor(getResources().getColor(R.color.coupon_limit_tv01));
            tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
            tv_number.setOnClickListener(v -> {
                mProductEditAnalysis.onProductEditNumClick(getContext(), rowsBean, rowsBeanPosition, tv_number.getText().toString());
                if (v.getContext() instanceof BaseActivity) {
                    ((BaseActivity) v.getContext()).hideSoftInput();
                }
                final TextView textNum = (TextView) v;
                //编辑弹出对话框加减数量
                try {
                    BaseActivity act = null;
                    if (v.getContext() instanceof BaseActivity) {
                        act = ((BaseActivity) v.getContext());
                    } else if(Abase.getCurrentActivity() instanceof BaseActivity){
                        act = ((BaseActivity) Abase.getCurrentActivity());
                    }
                    DialogUtil.addOrSubDialog(act, InputType.TYPE_CLASS_NUMBER, textNum.getText().toString(), ADD_STEP
                            , split, true, new DialogUtil.DialogClickListener() {

                                private InputMethodManager mImm;

                                @Override
                                public void confirm(String content) {
                                    if (!checkProductCanBuy(status)) {
                                        return;
                                    }
                                    sendShopNum(skuId, content);
                                }

                                @Override
                                public void cancel() {

                                }

                                @Override
                                public void showSoftInput(View view) {
                                    mImm = (InputMethodManager) (view.getContext()).getSystemService(Context.INPUT_METHOD_SERVICE);
                                    mImm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                                }

                            });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            int number = HandlerGoodsDao.getInstance().getNumber(skuId, isPackage);
            if (number < 0) {
                number = 0;
            }

            //如果是小于0并且需要隐藏，那么才隐藏,否则不隐藏
            boolean initSate = number <= 0 && needHide;
            //iv_numSub.setVisibility(initSate ? GONE : VISIBLE);
            //tv_number.setVisibility(initSate ? GONE : VISIBLE);
            if (initSate) {
                iv_numSub.setVisibility(View.GONE);
                tv_number.setVisibility(View.GONE);
                iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                ViewGroup.LayoutParams layoutParams = fl_root_view.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(25);
                fl_root_view.setLayoutParams(layoutParams);
//                toMin();
                if (mFoldingListener != null) {
                    mFoldingListener.onFolding(true);
                }
            } else {
                iv_numSub.setVisibility(View.VISIBLE);
                tv_number.setVisibility(View.VISIBLE);
                iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                ViewGroup.LayoutParams layoutParams = fl_root_view.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(75);
                fl_root_view.setLayoutParams(layoutParams);
//                toMax();
                if (mFoldingListener != null) {
                    mFoldingListener.onFolding(false);
                }
            }

            if (!modifyViewStyle) {
                if (initSate) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                } else {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                }
            }
            if (number >= 0) {
                tv_number.setText(String.valueOf(number));
            }
            iv_numSub.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mProductEditAnalysis.onSubtractBtnClick(getContext(), rowsBean, rowsBeanPosition);
                    if (mOnNumberListener != null){
                        mOnNumberListener.onClick(iv_numSub,Integer.valueOf(tv_number.getText().toString()));
                    }
                    numOnClick(skuId, status, false);
                }
            });
            iv_numAdd.setOnClickListener(v -> {
                int finalNumber = HandlerGoodsDao.getInstance().getNumber(skuId, isPackage);
                boolean addCartClickType = finalNumber <= 0 && needHide;
                if (addCartClickType) {
                    mProductEditAnalysis.onAddCartBtnClick(getContext(), rowsBean, rowsBeanPosition);
                } else {
                    mProductEditAnalysis.onAddBtnClick(getContext(), rowsBean, rowsBeanPosition);
                }
                if (mOnNumberListener != null){
                    mOnNumberListener.onClick(iv_numAdd,Integer.valueOf(tv_number.getText().toString()));
                }
                if (rowsBean.isControlGoods()) {
                    ControlGoodsDialogUtil.INSTANCE.showControlGoodsDialog(getContext(), rowsBean);
                    return;
                }
                if (isAddCartShowPopupWindow) {
                    ListItemAddCartPopWindow popWindow = new ListItemAddCartPopWindow((BaseActivity)getContext())
                            .setObserver()
                            .setData(rowsBean, ProductEditLayoutNew.this, tv_number.getText().toString());
                    popWindow.setAddCartCallback(integer -> {
                        showPopupWindowResultNum = integer;
                        numOnClick(skuId, status, true);
                        popWindowButtonClickJGTrack("确定",integer);
                        return null;
                    });
                    popWindow.setMSId(mSId);
                    popWindow.setMSpType(mSpType);
                    popWindow.setMSpId(mSpId);
                    popWindow.setJgTrackBean(jgTrackBean);
                    popWindow.show(iv_numAdd);
                } else {
                    numOnClick(skuId, status, true);
                }
            });
        }
    }

    private void popWindowButtonClickJGTrack(String btnName,int number) {
        ReportPDButtonClick pdBean = new ReportPDButtonClick();
        ReportPDExtendOuterBean jgExtendOuterBean = new ReportPDExtendOuterBean();
        if (jgPageListCommonBean != null){
            jgExtendOuterBean.setSptype(jgPageListCommonBean.getSptype());
            jgExtendOuterBean.setJgspid(jgPageListCommonBean.getJgspid());
            jgExtendOuterBean.setSid(jgPageListCommonBean.getSid());
            jgExtendOuterBean.setResultCnt(jgPageListCommonBean.getResult_cnt());
            jgExtendOuterBean.setPageNo(jgPageListCommonBean.getPage_no());
            jgExtendOuterBean.setPageSize(jgPageListCommonBean.getPage_size());
            jgExtendOuterBean.setTotalPage(jgPageListCommonBean.getTotal_page());
            jgExtendOuterBean.setKeyWord(jgPageListCommonBean.getKey_word());
            jgExtendOuterBean.setListPositionType(String.valueOf(rowsBean.positionType));
            jgExtendOuterBean.setListPositionTypename(rowsBean.positionTypeName);
            jgExtendOuterBean.setSearchSortStrategyId(rowsBean.searchSortStrategyCode);
            jgExtendOuterBean.setOperationId(rowsBean.operationId);
        }
        if (jgTrackBean != null){
            jgExtendOuterBean.setRank(jgTrackBean.getRank());
            if (jgTrackBean.getMJgOperationPositionInfo() != null){
                jgExtendOuterBean.setOperationRank(jgTrackBean.getMJgOperationPositionInfo().getOperationRank());
            }
            pdBean.setUrl(jgTrackBean.getUrl());
            pdBean.setReferrer(jgTrackBean.getJgReferrer());
            pdBean.setTitle(jgTrackBean.getTitle());
        }

        pdBean.setProductNumber(number);
        pdBean.setOuterBean(jgExtendOuterBean);
        pdBean.setProductId((int) rowsBean.getId());
        pdBean.setProductName(rowsBean.getShowName());
        pdBean.setProductFirst(rowsBean.categoryFirstId);
        pdBean.setProductPrice(rowsBean.getJgProductPrice());
        pdBean.setProductType(String.valueOf(rowsBean.productType));
        pdBean.setProductShopCode(rowsBean.shopCode);
        pdBean.setProductShopName(rowsBean.shopName);
        pdBean.setProductActivityType(rowsBean.productActivityType);
        pdBean.setBtnName(btnName);
        pdBean.setBtnDesc("列表页底部弹窗");
        pdBean.setDirect("1");
        ReportManager.getInstance().report(pdBean);
    }

    public void bindPackage(int packageId, int status, int fromPage) {
        isPackage = true;
        bindData(packageId, status, true, fromPage, false, 1, false);
    }

    public void refreshData() {
        int number = HandlerGoodsDao.getInstance().getNumber(proId);
        if (number >= 0) {
            tv_number.setText(String.valueOf(number));
        }
    }


    private boolean checkProductCanBuy(int status) {
        if (status == 2) {
            ToastUtils.showShort("产品已经售罄");
            return false;
        } else if (status == 4) {
            ToastUtils.showShort("产品已经下架不能购买");
            return false;
        }
        return true;
    }

    public void numOnClick(long skuId, final int status, boolean isAdd) {

        if (!checkProductCanBuy(status)) {
            return;
        }

        //获取商品的数量
        number = Integer.valueOf(tv_number.getText().toString());
        if (!isAdd) {
            if (split) {
                number -= 1;
            } else {
                number -= ADD_STEP;
            }
            if (number < 1) {
                number = 0;
            }
        } else {
            if (number <= 0) {//如果本来是小于0的，添加的时候显示出来
                //                iv_numSub.setVisibility(VISIBLE);
                //                tv_number.setVisibility(VISIBLE);
                toMax();
//                iv_numSub.setAnimation(showReduceViewAnim());
//                tv_number.setAnimation(showTranslateAnim());
                showReduceView();
                tv_number.setTag(R.id.tag_3, true);
            }
            if (!isAddCartShowPopupWindow) {
                number += ADD_STEP;
            } else {
                number = showPopupWindowResultNum;
            }
        }
        tv_number.setText(number + "");
        if (isAdd) {
            isFastDoubleClick(skuId, true, number);
        } else {
            isFastDoubleClick(skuId, false, number);
        }
    }


    private boolean isFastDoubleClick(long skuId, boolean isAdd, int number) {
        if (tv_number == null || proId <= 0) {
            return false;
        }
        executeLast(skuId, number, isAdd);
//        if (lastTime <= 0 || System.currentTimeMillis() - lastTime < DIFF_TIME) {
//            lastTime = System.currentTimeMillis();
//            return true;
//        } else {
//            tv_number.getHandler().removeCallbacks(lastRunnable);
//            lastTime = System.currentTimeMillis();
//            editShopNumber(skuId, isAdd, isPackage, number, tv_number, getButtonType(isAdd));//执行切换的任务
//        }
        tv_number.getHandler().removeCallbacks(lastRunnable);
        lastTime = System.currentTimeMillis();
        editShopNumber(skuId, isAdd, isPackage, number, tv_number, getButtonType(isAdd));//执行切换的任务
        return false;
    }

    private String getButtonType(boolean isAdd) {
        return isAdd? BUTTON_TYPE_ADD: BUTTON_TYPE_REDUCE;
    }

    /**
     * 页面关闭时调用
     */
    private void clean() {
        editShopNumberParams = null;
    }

    private void executeLast(long proId, int number, boolean lastIsAdd) {
        if (tv_number == null || proId <= 0) {
            return;
        }
        if (lastRunnable == null) {
            return;
        }
        tv_number.getHandler().removeCallbacks(lastRunnable);
        lastRunnable.setData(proId, number, lastIsAdd);
        tv_number.getHandler().postDelayed(lastRunnable, (long) (DIFF_TIME * 1.2));
    }

    private class LastRunnable implements Runnable {
        private long proId;
        private int number;
        private boolean lastIsAdd;

        @Override
        public void run() {
            if (tv_number == null) {
                return;
            }
            if (System.currentTimeMillis() - lastTime < DIFF_TIME) {
                return;
            }
            editShopNumber(proId, lastIsAdd, isPackage, number, tv_number, getButtonType(lastIsAdd));//执行切换的任务
        }

        public void setData(long proId, int number, boolean lastIsAdd) {
            this.proId = proId;
            this.lastIsAdd = lastIsAdd;
            this.number = number;
        }
    }

    //最后的任务
    private LastRunnable lastRunnable = new LastRunnable();


    public void sendShopNum(long skuId, String str) {
        //获取商品的数量
        int num = 0;
        try {
            num = Integer.parseInt(str);
        } catch (Exception e) {
            num = 0;
        }
        if (num <= 0) {
            num = 0;
        }
        boolean isAdd = false;
        if (number <= 0 && num > 0) {
            tv_number.setTag(R.id.tag_3, true);
        }
        isAdd = num > number;
        number = num;
        tv_number.setText(String.valueOf(number));
        editShopNumber(skuId, isAdd, isPackage, number, tv_number, BUTTON_TYPE_INPUT);
    }

    //修改商品发送到服务器
    private void editShopNumber(final long id, final boolean isAdd, final boolean isPackage, final int number, final TextView tv_number, String buttonType) {
        if (number <= 0) {
            delProduct(id, isAdd, number, tv_number);
            return;
        }
        //服务器同步
        if (editShopNumberParams == null) {
            editShopNumberParams = new RequestParams();
        }
        JgRequestParams jgRequestParams = new JgRequestParams();
        editShopNumberParams.put("merchantId", HttpManager.getInstance().getMerchant_id());
        try {
            if (JGTrackManager.Companion.getSuperProperty(getContext(),
                    JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
                String searchSortStrategyCode =
                        (String)JGTrackManager.Companion.getSuperProperty(getContext(),
                                JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if (jgTrackBean!=null ){
            if (jgTrackBean.getEntrance() != null && !jgTrackBean.getEntrance().isEmpty()){
//                params.put("entrance",jgTrackBean.getEntrance());
                jgRequestParams.setEntrance(jgTrackBean.getEntrance());
            }
            if (jgTrackBean.getActivityEntrance() != null && !jgTrackBean.getActivityEntrance().isEmpty()){
//                params.put("activityEntrance",jgTrackBean.getActivityEntrance());
                jgRequestParams.setActivity_entrance(jgTrackBean.getActivityEntrance());
            }
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), String.valueOf(id))){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(id))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        jgRequestParams.setProduct_number(number);
        jgRequestParams.setDirect("1");
        jgRequestParams.setSession_id(com.ydmmarket.report.manager.TrackManager.getSessionId(YBMAppLike.getAppContext()));
        editShopNumberParams.put("mddata",new Gson().toJson(jgRequestParams));
        if (jgTrackBean!= null && jgTrackBean.getEntrance()!=null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            editShopNumberParams.getParamsMap().remove("mddata");
        }
        editShopNumberParams.setUrl(AppNetConfig.BUY_COMMODITY);

        editShopNumberParams.put(isPackage ? "packageId" : "skuId", id + "");
        editShopNumberParams.put("amount", String.valueOf(number));
        editShopNumberParams.put("addType", buttonType);
        if (!TextUtils.isEmpty(mSource)) {
            editShopNumberParams.put("source", mSource);
        }
        editShopNumberParams.put("index", mIndex);
        if (rowsBean != null && !TextUtils.isEmpty(rowsBean.searchSortStrategyCode)) {
            //搜索策略编码
            editShopNumberParams.put("searchSortStrategyCode", rowsBean.searchSortStrategyCode);
        }
        if (rowsBean != null && !TextUtils.isEmpty(rowsBean.directModule)) {
            //所属模块 1-搜索列表信息流 2-搜索运营位模块
            editShopNumberParams.put("directModule", rowsBean.directModule);
        }
        // 增加加购埋点
        if (mAddCartListener != null && editShopNumberParams != null){
            mAddCartListener.onPreAddCart(editShopNumberParams);
        }
        //spm加购埋点
        String qtData = ChangeCartUtil.getChangeCartParams(getContext(), rowsBean);
        if (qtData != null) {
            editShopNumberParams.put("qtdata", qtData);
        }
        HttpManager.getInstance().post(editShopNumberParams, new BaseResponse<CartDataBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartDataBean> obj, CartDataBean cartDataBean) {
                lastTime = 0;
                if (obj != null && obj.isSuccess()) {
                    if (isAdd) {
                        startAnim();
                    }
                    int num = 0;
                    if (cartDataBean != null) {
                        num = cartDataBean.qty;
                        if (mAddCartListener != null && cartDataBean.qty != 0){
                            ProductEditLayoutSuccessParams p = new ProductEditLayoutSuccessParams(buttonType, num + "",0);
                            mAddCartListener.onAddCartSuccess(p);
                        }
                    }
                    if (id == proId) {
                        if (tv_number != null && ProductEditLayoutNew.this.number == number) {
                            tv_number.setText(num + "");
                            if (num == 0 && iv_numSub != null && iv_numSub.getVisibility() == View.VISIBLE && needHide) {
                                //需要隐藏减号
                                iv_numSub.setVisibility(GONE);
                                tv_number.setVisibility(GONE);
//                                iv_numSub.setAnimation(hideReduceViewAnim());
                                hideReduceView();

                            }
                        }
                    }
                    //数据库更新
                    HandlerGoodsDao.getInstance().updateItem(id, num, isPackage);
                    boolean getCartNum = false;
                    try {
                        getCartNum = (boolean) tv_number.getTag(R.id.tag_3);
                    } catch (Throwable e) {
                        BugUtil.sendBug(e);
                        e.printStackTrace();
                    }
                    notification(isAdd, getCartNum);
                } else {
                    handleActionFailure(id);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                handleActionFailure(id);
            }
        });
    }

    private void handleActionFailure(long id) {
        if (proId == id && tv_number != null) {
            int num = Math.max(HandlerGoodsDao.getInstance().getNumber(proId), 0);
            tv_number.setText(String.valueOf(num));
            if (num == 0 && needHide) {//如果为0并且需要隐藏掉，那么就只留下添加按钮
//                iv_numSub.setAnimation(hideReduceViewAnim());
                hideReduceView();
                toMin();
            }
        }
    }

    private void delProduct(final long id, final boolean isAdd, final int number, final TextView tv_number) {
        RequestParams params = new RequestParams();
        params.put("merchantId", HttpManager.getInstance().getMerchant_id());
        params.setUrl(AppNetConfig.CART_DELETE);
        params.put("ids", id + "");
        if (fromPage != FROMPAGE_COMMODITY) {
            iv_numSub.setVisibility(GONE);
            tv_number.setVisibility(INVISIBLE);
//            iv_numSub.setAnimation(hideReduceViewAnim());
            hideReduceView();
            // toMin();
        }
        //spm加购埋点
        String qtData = ChangeCartUtil.getChangeCartParams(getContext(), rowsBean);
        if (qtData != null) {
            params.put("qtdata", qtData);
        }
        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean bean) {

                if (obj != null && obj.isSuccess()) {
                    if (id == proId) {
                        if (tv_number != null && ProductEditLayoutNew.this.number == number) {
                            tv_number.setText(0 + "");
                        }
                    }
                    notification(false, true);
                    //数据库更新
                    HandlerGoodsDao.getInstance().deleteItem(id, false);
                    if (delProductListener != null) delProductListener.onDelProduct(number);
                } else {
                    //                    iv_numSub.setVisibility(VISIBLE);
                    //                    tv_number.setVisibility(VISIBLE);
                    toMax();
//                    iv_numSub.setAnimation(showReduceViewAnim());
//                    tv_number.setAnimation(showTranslateAnim());
                    showReduceView();
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                //                iv_numSub.setVisibility(VISIBLE);
                //                tv_number.setVisibility(VISIBLE);
                toMax();
//                iv_numSub.setAnimation(showReduceViewAnim());
//                tv_number.setAnimation(showTranslateAnim());
                showReduceView();
            }
        });
    }

    //数据修改了，通知相关页面，增加，减少都会通知
    private void notification(boolean isAdd, boolean getCartNum) {
        if (getCartNum) {//修改主页购物车数量
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
            if (tv_number != null) {
                tv_number.setTag(R.id.tag_3, false);
            }
        }
        if (fromPage == FROMPAGE_ACTIVITS) {//调用
            //没有处理
            Intent intent = new Intent(IntentCanst.ACTION_ADD_PRODUCT);
            intent.putExtra("isAdd", isAdd);
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
        } else if (fromPage == FROMPAGE_ALL_PRODUCT) {//全部商品修改数据
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_CHANG_CAR_NUMBER));
        } else if (fromPage == FROMPAGE_COMMODITY) {
            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL));//用于同步活动页面商品数量与收藏
            Intent intent = new Intent(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON);
            intent.putExtra("addNum", number);
            intent.putExtra("productId", proId + "");
            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(intent);//用于同步活动页面商品数量与收藏
        } else if (fromPage == FROMPAGE_CART_RECOMMEND) {//购物车页为你推荐加购通知
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_CARTFRAGMENT_ADD_GOOD));
        } else {
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_RELATED_GOODS_ADD_GOODS));
        }
    }

    //只有增加商品都会有动画
    private void startAnim() {
        if (!animationEnable) return;
        if (fromPage == FROMPAGE_ACTIVITS) {
            //向上动画
            startAnim2Top(YBMAppLike.endLocationInCoupon);// 开始执行动画
        } else if (fromPage == FROMPAGE_ALL_PRODUCT) {
            startAnim2Botom(YBMAppLike.endLocation);// 开始执行动画
        } else if (fromPage == FROMPAGE_COMMODITY) {
            startAnim2Botom(YBMAppLike.endLocationInCommodity);// 开始执行动画
        }
    }

    //向上飞入效果
    private void startAnim2Top(int[] endLocation) {
        if (animView == null) {
            return;
        }
        try {
            int[] startLocation = new int[2];// 一个整型数组，用来存储按钮的在屏幕的X、Y坐标
            animView.getLocationInWindow(startLocation);// 这是获取购买按钮的在屏幕的X、Y坐标（这也是动画开始的坐标）
            View view = getAnimView(startLocation, animView);

            // 计算位移
            int fx = 0;// 动画位移的X坐标
            int fy = 0;// 因为有缩放，后面大小基本为零了
            int w = animView.getWidth();
            int h = animView.getHeight();
            if (w > h) {
                w = w / 3;
                h = h / 3;
            } else {
                w = w / 4;
                h = h / 4;
            }
            int tx = endLocation[0] - startLocation[0] - w;// 动画位移的X坐标
            int ty = endLocation[1] - startLocation[1] - h;// 因为有缩放，后面大小基本为零了

            AnimationSet set = getAnimationSet2Top(fx, fy, tx, ty);
            set.setAnimationListener(new MyAnimationListener(view));
            view.startAnimation(set);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    //向下飞入效果
    private void startAnim2Botom(int[] endLocation) {
        if (iv_numAdd == null) {
            return;
        }
        try {
            int[] startLocation = new int[2];
            iv_numAdd.getLocationInWindow(startLocation);
            View view = getAnimView(startLocation, null);

            // 起点
            int fx = startLocation[0];
            int fy = startLocation[1];

            /* 终点 */
            int tx = endLocation[0];// 动画位移的X坐标
            int ty = endLocation[1];// 动画位移的y坐标

            /* 中点 */
            int mx = 0;
            if (UiUtils.getScreenWidth() / 2 <= fx) {
                mx = fx - Math.abs(tx - fx) / 2;
            } else {
                mx = fx + Math.abs(tx - fx) / 2;
            }
            if (height <= 10) {
                height = UiUtils.getScreenHeight();
            }
            int my = fy - (fy < (height / 2) ? fy / 2 : fy / 3);

            AnimationSet set = getAnimationSet2Bottom(fx, fy, mx, my, tx, ty);
            set.setAnimationListener(new MyAnimationListener(view));
            view.startAnimation(set);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    private AnimationSet getAnimationSet2Top(int fx, int fy, int tx, int ty) {
        int startTime = 90;
        int rotate = 50;
        AnimationSet set = new AnimationSet(false);

        RotateAnimation rotateAnimation = new RotateAnimation(0, rotate, Animation.RELATIVE_TO_SELF, 1, Animation.RELATIVE_TO_SELF, 1);
        rotateAnimation.setDuration(startTime);
        rotateAnimation.setFillAfter(false);
        rotateAnimation.setInterpolator(new AccelerateDecelerateInterpolator());
        rotateAnimation.setRepeatCount(0);
        set.addAnimation(rotateAnimation);

        //缩放
        ScaleAnimation scale = new ScaleAnimation(0.8f, 0.08f, 0.8f, 0.08f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        scale.setRepeatCount(0);
        scale.setFillAfter(false);
        scale.setInterpolator(new AccelerateDecelerateInterpolator());
        set.addAnimation(scale);

        //自转
        RotateAnimation rotateAnimation2 = new RotateAnimation(rotate, 180, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation2.setStartTime(startTime + 50);
        rotateAnimation2.setRepeatCount(0);
        set.addAnimation(rotateAnimation2);

        TranslateAnimation translateAnimation = new TranslateAnimation(fx, tx, fy, ty);
        translateAnimation.setStartTime(startTime);
        translateAnimation.setInterpolator(new AccelerateDecelerateInterpolator());
        translateAnimation.setRepeatCount(0);// 动画重复执行的次数
        translateAnimation.setFillAfter(false);
        set.addAnimation(translateAnimation);

        set.setFillAfter(false);
        set.setDuration(450);// 动画的执行时间

        return set;
    }


    private AnimationSet getAnimationSet2Bottom(int fx, int fy, int mx, int my, int tx, int ty) {
        AnimationSet set = new AnimationSet(false);
        TranslateAnimation translateAnimation1 = new TranslateAnimation(0, mx - fx, 0, my - fy);
        translateAnimation1.setInterpolator(new DecelerateInterpolator());
        translateAnimation1.setRepeatCount(0);
        translateAnimation1.setFillAfter(false);
        set.addAnimation(translateAnimation1);

        TranslateAnimation translateAnimation2 = new TranslateAnimation(0, tx - mx, 0, ty - my);
        translateAnimation2.setInterpolator(new AccelerateInterpolator());
        translateAnimation2.setRepeatCount(0);
        translateAnimation2.setFillAfter(false);
        set.addAnimation(translateAnimation2);

        set.setDuration(450);
        set.setFillAfter(false);

        return set;
    }


    /**
     * 显示减号的动画
     */
    private AnimationSet showReduceViewAnim() {
        AnimationSet set = new AnimationSet(true);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0f, 1f);
        set.addAnimation(alphaAnimation);
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 2f,
                TranslateAnimation.RELATIVE_TO_SELF, 0,
                TranslateAnimation.RELATIVE_TO_SELF, 0,
                TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (fromPage != FROMPAGE_COMMODITY && iv_numAdd != null) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        set.addAnimation(translate);
        set.setDuration(400);
        return set;
    }

    private void showReduceView() {

        ValueAnimator animator = ValueAnimator.ofFloat(0f, 100f);
        animator.setDuration(200);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                //百分比对应的值
                float value = (float) animation.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = ProductEditLayoutNew.this.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(25) + (int) ((UiUtils.dp2px(50) * value) / 100f);
                ProductEditLayoutNew.this.setLayoutParams(layoutParams);
                if (value == 0) {
                    iv_numSub.setVisibility(View.VISIBLE);
                    tv_number.setVisibility(View.VISIBLE);
                }
                if (value == 100) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                    if (mFoldingListener != null) {
                        mFoldingListener.onFolding(false);
                    }
                }
            }
        });
        animator.start();
    }

    private void hideReduceView() {

        ValueAnimator animator = ValueAnimator.ofFloat(0f, 100f);
        animator.setDuration(200);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                //百分比对应的值
                float value = (float) animation.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = ProductEditLayoutNew.this.getLayoutParams();
                layoutParams.width = UiUtils.dp2px(75) - (int) ((UiUtils.dp2px(50) * value) / 100f);
                ProductEditLayoutNew.this.setLayoutParams(layoutParams);
                if (value == 100) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                    iv_numSub.setVisibility(View.GONE);
                    tv_number.setVisibility(View.GONE);
                    iv_numAdd.setVisibility(View.VISIBLE);
                    if (mFoldingListener != null) {
                        mFoldingListener.onFolding(true);
                    }
                } else {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_add);
                }
            }
        });
        animator.start();
    }

    /**
     * 隐藏减号的动画
     */
    private AnimationSet hideReduceViewAnim() {
        AnimationSet set = new AnimationSet(true);
        AlphaAnimation alphaAnimation = new AlphaAnimation(1f, 0f);
        set.addAnimation(alphaAnimation);
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 2f
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (iv_numAdd != null) {
                    iv_numAdd.setImageResource(R.drawable.icon_edit_layout_new_only_add);
                }
                toMin();
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        set.addAnimation(translate);
        set.setDuration(400);
        return set;
    }

    /**
     * 控件的水平动画
     */
    private Animation showTranslateAnim() {
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 1f
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setDuration(500);
        return translate;
    }

    private class MyAnimationListener implements Animation.AnimationListener {
        private View view;

        public MyAnimationListener(View view) {
            this.view = view;
        }

        // 动画的开始
        @Override
        public void onAnimationStart(Animation animation) {
            view.setVisibility(View.VISIBLE);
        }

        @Override
        public void onAnimationRepeat(Animation animation) {

        }

        // 动画的结束
        @Override
        public void onAnimationEnd(Animation animation) {
            try {
                view.setVisibility(View.GONE);
                if (view != null) {
                    view.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (view != null) {
                                view.clearAnimation();
                                if (view.getParent() instanceof LinearLayout) {
                                    getWindowRootView().removeView((LinearLayout) view.getParent());
                                } else {
                                    getWindowRootView().removeView(view);
                                }
                                view = null;
                            }
                        }
                    }, 50);
                }
            } catch (Exception e) {
                BugUtil.sendBug(e);
            }
        }
    }

    /**
     * @param
     * @return void
     * @throws
     * @Description: 创建动画层
     */
    private View getAnimView(int[] location, ImageView imageView) {
        LinearLayout animLayout = getAnimLayout();
        ImageView img = new ImageView(getContext());
        LinearLayout.LayoutParams lp = null;
        if (imageView == null) {
            img.setImageDrawable(getResources().getDrawable(R.drawable.sign));
            lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        } else {
            img.setImageDrawable(imageView.getDrawable());
            lp = new LinearLayout.LayoutParams(imageView.getWidth(), imageView.getHeight());
        }
        lp.leftMargin = location[0];
        lp.topMargin = location[1];
        img.setLayoutParams(lp);
        animLayout.addView(img);
        return img;
    }

    private LinearLayout getAnimLayout() {
        ViewGroup rootView = getWindowRootView();
        LinearLayout animLayout = new LinearLayout(getContext());
        LayoutParams lp = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        animLayout.setLayoutParams(lp);
        animLayout.setBackgroundResource(android.R.color.transparent);
        rootView.addView(animLayout);
        return animLayout;
    }

    private ViewGroup getWindowRootView() {
        if (rootView == null) {
            rootView = (ViewGroup) (BaseYBMApp.getApp().getCurrActivity()).getWindow().getDecorView();
        }
        return rootView;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                clean();
            }
        }, DIFF_TIME);
    }

    /**
     * 抛出一个修改icon的接口
     */
    public void modifyIcon(int resAdd, int resSub) {
        if (iv_numSub == null || iv_numAdd == null) {
            return;
        }
        if (resAdd > 0) {
            iv_numAdd.setImageResource(resAdd);
        }
        if (resSub > 0) {
            iv_numSub.setImageResource(resSub);
        }
    }

    /**
     * 抛出修改整个控件样式的接口
     */
    public void modifyViewStyle(int bgImg, int resAdd, int resSub, int colorD) {
        if (iv_numSub == null || iv_numAdd == null || tv_number == null) {
            return;
        }
        modifyViewStyle = true;
        if (bgImg > 0) {
            tv_number.setBackgroundResource(bgImg);
        }
        if (colorD > 0) {
            tv_number.setTextColor(getResources().getColor(colorD));
        }
        if (resAdd > 0) {
            iv_numAdd.setImageResource(resAdd);
        }
        if (resSub > 0) {
            iv_numSub.setImageResource(resSub);
        }
    }

    /**
     * 设置是否开启飞入动画
     *
     * @param enable
     */
    public void setAnimationEnable(boolean enable) {
        animationEnable = enable;
    }

    /**
     * 加购添加监听
     *
     * @param listener
     */
    public void setOnAddCartListener(AddCartListener listener) {
        mAddCartListener = listener;
    }

    public void setOnDelProductListener(DelProductListener delProductListener) {
        this.delProductListener = delProductListener;
    }

    public void setOnFoldingListener(FoldingListener listener) {
        mFoldingListener = listener;
    }

    /**
     * 动画折叠完成 监听接口
     */
    public interface FoldingListener {
        // 是否折叠
        void onFolding(Boolean folding);
    }

    /**
     * 加购监听接口
     */
    public interface AddCartListener {
        //加购前
        RequestParams onPreAddCart(RequestParams params);

        //加购成功
        void onAddCartSuccess(ProductEditLayoutSuccessParams params);
    }

    public interface DelProductListener {
        void onDelProduct(int number);
    }

}
