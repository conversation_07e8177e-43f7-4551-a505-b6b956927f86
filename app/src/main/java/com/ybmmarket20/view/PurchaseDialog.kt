package com.ybmmarket20.view

import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.View
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.viewmodel.PurchaseDate
import jsc.kit.wheel.base.WheelItem
import jsc.kit.wheel.base.WheelItemView
import jsc.kit.wheel.base.WheelView

class PurchaseDialog(val mContext: Context): Dialog(mContext, R.style.dialog_confirm_style) {

    private var wheelYear: WheelItemView
    private var wheelMonth: WheelItemView
    private var mPurchaseDate: PurchaseDate? = null
    private var yearIndex: Int = 0
    private var monthIndex: Int = 0
    private var oldYearIndex = yearIndex
    private var oldMonthIndex = monthIndex
    private var onConfirmListener: ((Int, Int)->Unit)? = null

    init {
        val dialogView = View.inflate(mContext, R.layout.dialog_purchase, null)
        setContentView(dialogView)
        wheelYear = dialogView.findViewById(R.id.wv_year)
        wheelMonth = dialogView.findViewById(R.id.wv_month)

        wheelYear.setOnSelectedListener(PurchaseYearWheelListener())
        wheelMonth.setOnSelectedListener(PurchaseMonthWheelListener())
        window?.let {
            val p = it.attributes
            p.width = UiUtils.getScreenWidth()
            it.attributes = p
            it.setGravity(Gravity.BOTTOM)
            it.setWindowAnimations(R.style.AnimBottom)
        }
        setCancelable(true)
        dialogView.findViewById<TextView>(R.id.tv_cancel).setOnClickListener { dismiss() }
        dialogView.findViewById<TextView>(R.id.tv_finish).setOnClickListener {
            dismiss()
            oldYearIndex = yearIndex
            oldMonthIndex = monthIndex
            onConfirmListener?.invoke(yearIndex, monthIndex)
        }
    }

    fun setOnConfirmListener(listener: (Int, Int)->Unit) {
        onConfirmListener = listener
    }

    /**
     * 设置时间选择器数据
     */
    fun setItemData(purchaseDate: PurchaseDate) {
        mPurchaseDate = purchaseDate
        //设置年数据，并设置选中位置
        val yearArr = purchaseDate.yearList.map { WheelItem("${it}年") }.toTypedArray()
        wheelYear.setItems(yearArr)
        yearIndex = purchaseDate.yearList.indexOf(purchaseDate.year)
        oldYearIndex = yearIndex
        //设置月份数据，并设置选中位置
        val monthList = if (oldYearIndex == purchaseDate.yearList.size - 1) {
            purchaseDate.currentMonthList
        } else {
            purchaseDate.monthList
        }
        val monthArr = monthList.map { WheelItem("${it}月") }.toTypedArray()
        wheelMonth.setItems(monthArr)
        monthIndex = monthList.indexOf(purchaseDate.month)
        oldMonthIndex = monthIndex
    }

    override fun show() {
        try {
            wheelYear.setSelectedIndex(oldYearIndex, false)
            wheelMonth.setSelectedIndex(oldMonthIndex, false)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        super.show()
    }

    inner class PurchaseYearWheelListener: WheelView.OnSelectedListener {
        override fun onSelected(context: Context?, selectedIndex: Int) {
            //切换到当前年，月份可能小于12
            if (selectedIndex == mPurchaseDate?.yearList?.lastIndex) {
                val monthArr = mPurchaseDate?.currentMonthList?.map { WheelItem("${it}月") }?.toTypedArray()
                var tempIndex = monthIndex
                if (tempIndex > (mPurchaseDate?.currentMonthList?.lastIndex ?: 0)) {
                    tempIndex = (mPurchaseDate?.currentMonthList?.lastIndex ?: 0)
                }
                monthArr?.let(wheelMonth::setItems)
                wheelMonth.setSelectedIndex(tempIndex, false)
                yearIndex = tempIndex
            } else if (yearIndex == mPurchaseDate?.yearList?.lastIndex) {
                val monthArr = mPurchaseDate?.monthList?.map { WheelItem("${it}月") }?.toTypedArray()
                monthArr?.let(wheelMonth::setItems)
                wheelMonth.setSelectedIndex(monthIndex, false)
            }
            yearIndex = selectedIndex
        }
    }

    inner class PurchaseMonthWheelListener: WheelView.OnSelectedListener {
        override fun onSelected(context: Context?, selectedIndex: Int) {
            monthIndex = selectedIndex
        }
    }
}