package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.RefundDetailCurrentBean;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarketkotlin.utils.TextViewKt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/*
*   STATE_1(1, "创建退款单","待审核", "您的退货需求已提交，请耐心等待售后客服审核。"),
    STATE_2(2, "取消", "退款取消", "您的退款单已取消，如有问题请咨询客服人员。"),
    STATE_3(3, "客服审核通过", "已审核", "您的退货需求已审核，请在3天之内退回货物至我司仓库并填写退货快递单号，逾期将自动取消退款申请。"),
    STATE_4(4, "客服审核驳回", "客服审核不通过", "您的退款单客服已驳回，关闭原因详见退款详情，如有问题请咨询客服人员。"),
    STATE_5(5, "仓库审核通过", "已入库", "您的退货已收到，请耐心等待财务处理退款，预计2-7个工作日到账。"),
    STATE_6(6, "仓库审核驳回", "入库失败", "您的退款单仓库已驳回，关闭原因详见退款详情，如有问题请咨询客服人员。"),
    STATE_7(7, "财务审核通过", "退款成功", "已为您成功退款，如有问题请咨询客服人员。"),
    STATE_8(8, "财务审核驳回", "退款失败", "您的退款单财务已驳回，关闭原因详见退款详情，如有问题请咨询客服人员。");
* */
public class RefundDetailsOptimizationLayout extends LinearLayout {

    private ImageView iv_01;
    private ImageView iv_02;
    private ImageView iv_03;
    private View line_01;
    private View line_02;
    private TextView tv_01;
    private TextView tv_02;
    private TextView tv_03;
    private TextView tv_content;
    private TextView tv_time;
    private TextView tvRefundDetailTimeTitle;
    private TextView tvRefundDetailTimeCount;
    private LinearLayout tvRefundDetailTimeLayout;
    private LinearLayout ll_refund;
    private TextView rtv_reject_refund, rtv_agree_refund;
    private TextView tvStatusTitle;
    private LinearLayout llStatusIcon, llStatusText;
    private SimpleDateFormat dateFormat;
    private ICountDownListener countDownListener;
    private IRefundButtonClickListener refundButtonClickListener;

    private List<RefundDetailCurrentBean> items = new ArrayList<>();

    public RefundDetailsOptimizationLayout(Context context) {
        this(context, null);
    }

    public RefundDetailsOptimizationLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RefundDetailsOptimizationLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    /*
     * 初始化
     * */
    private void init() {

        View.inflate(getContext(), R.layout.layout_refund_details_optimization, this);

        iv_01 = (ImageView) findViewById(R.id.iv_01);
        iv_02 = (ImageView) findViewById(R.id.iv_02);
        iv_03 = (ImageView) findViewById(R.id.iv_03);
        line_01 = (View) findViewById(R.id.line_01);
        line_02 = (View) findViewById(R.id.line_02);
        tv_01 = (TextView) findViewById(R.id.tv_01);
        tv_02 = (TextView) findViewById(R.id.tv_02);
        tv_03 = (TextView) findViewById(R.id.tv_03);
        tv_content = (TextView) findViewById(R.id.tv_content);
        tv_time = (TextView) findViewById(R.id.tv_time);
        tvRefundDetailTimeTitle = findViewById(R.id.tv_refund_detail_time_title);
        tvRefundDetailTimeCount = findViewById(R.id.tv_refund_detail_time_count);
        tvRefundDetailTimeLayout = findViewById(R.id.tv_refund_detail_time_layout);
        rtv_agree_refund = findViewById(R.id.rtv_agree_refund);
        rtv_reject_refund = findViewById(R.id.rtv_reject_refund);
        ll_refund = findViewById(R.id.ll_refund);
        llStatusIcon = findViewById(R.id.ll_status_icon);
        llStatusText = findViewById(R.id.ll_status_text);
        tvStatusTitle = findViewById(R.id.tv_status_title);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        rtv_reject_refund.setOnClickListener(new RefundButtonClickListener(RefundButtonClickListener.REFUND_BUTTON_TYPE_REJECT));
        rtv_agree_refund.setOnClickListener(new RefundButtonClickListener(RefundButtonClickListener.REFUND_BUTTON_TYPE_AGREE));
    }

    /**
     * 隐藏状态view
     */
    public void showStatusItemsView(boolean isShow) {
        llStatusIcon.setVisibility(isShow? View.VISIBLE: View.GONE);
        llStatusText.setVisibility(isShow? View.VISIBLE: View.GONE);
    }

    /**
     * 设置拒绝按钮文案
     * @param text
     */
    public void setRejectText(String text) {
        rtv_reject_refund.setText(text);
    }

    /**
     * 设置拒绝按钮文案
     * @param text
     */
    public void setAgreeText(String text) {
        rtv_agree_refund.setText(text);
    }

    /**
     * 设置状态标题
     * @param title
     */
    public void setStatusTitle(String title) {
        tvStatusTitle.setText(title);
        tvStatusTitle.setVisibility(View.VISIBLE);
    }

    /**
     * 设置轮播图数据
     *
     * @param
     */
    public void setItemData(List<RefundDetailCurrentBean> list) {
        if (iv_01 == null) {
            return;
        }
        if (list == null || list.isEmpty()) {
            return;
        }
        if (items == null) {
            items = new ArrayList<>();
        }
        items.clear();
        items.addAll(list);

        if (items == null || items.isEmpty()) {
            iv_01.setImageDrawable(null);
            iv_02.setImageDrawable(null);
            iv_03.setImageDrawable(null);
            return;
        }

        try {
            if (items.size() > 0 && !TextUtils.isEmpty(items.get(0).labelTitle)) {

                tv_01.setText(items.get(0).labelTitle);
                tv_01.setTextColor(setTextColor(items.get(0).operateType));

                iv_01.setImageDrawable(setDrawable(items.get(0).operateType));
                line_01.setBackgroundColor(setColor(items.get(0).operateType));

                setImageLayout(iv_01, items.get(0).operateType);
            }
            if (list.size() > 1 && !TextUtils.isEmpty(items.get(1).labelTitle)) {

                tv_02.setText(items.get(1).labelTitle);
                tv_02.setTextColor(setTextColor(items.get(1).operateType));

                iv_02.setImageDrawable(setDrawable(items.get(1).operateType));
                line_02.setBackgroundColor(setColor(items.get(1).operateType));

                setImageLayout(iv_02, items.get(1).operateType);
            }
            if (list.size() > 2 && !TextUtils.isEmpty(items.get(2).labelTitle)) {

                tv_03.setText(items.get(2).labelTitle);
                tv_03.setTextColor(setTextColor(items.get(2).operateType));

                iv_03.setImageDrawable(setDrawable(items.get(2).operateType));

                setImageLayout(iv_03, items.get(2).operateType);
            }
            if (list.size() > 3 && !TextUtils.isEmpty(items.get(3).prompt)) {

                tv_content.setText(items.get(3).prompt);

                String createTime = dateFormat.format(new Date(items.get(3).createTime));
                tv_time.setText(createTime);
                if (items.get(3).createTime == 0 || items.get(3).operateType == 13) {
                    tv_time.setVisibility(View.GONE);
                } else {
                    tv_time.setVisibility(View.VISIBLE);
                }
                RefundDetailCurrentBean bean = items.get(3);
                tvRefundDetailTimeTitle.setText(bean.countDownDesc);
                tvRefundDetailTimeLayout.setVisibility(bean.countDownTime == 0? View.GONE: View.VISIBLE);
                TextViewKt.addCountDownWithUnit(tvRefundDetailTimeCount, bean.countDownTime * 1000, null, null, () -> {
                    tvRefundDetailTimeCount.setText("0分");
                    if (countDownListener != null) countDownListener.onCountDown();
                    return null;
                });
                ll_refund.setVisibility(items.get(3).operateType == 13? View.VISIBLE: View.GONE);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public int setTextColor(int operateType) {

        int color = R.color.text_9494A6;

        switch (operateType) {
            case 0:
                color = R.color.text_9494A6;
                break;
            case 2:
            case 4:
            case 6:
            case 8:
            case 11:
                color = R.color.color_ff2121;
                break;
            default:
                color = R.color.text_292933;
                break;
        }
        return getResources().getColor(color);
    }


    private void setImageLayout(ImageView iv, int operateType) {
        LayoutParams params = null;
        if (operateType == 0) {
            params = new LayoutParams(UiUtils.dp2px(10), UiUtils.dp2px(10));
        } else {
            params = new LayoutParams(UiUtils.dp2px(20), UiUtils.dp2px(20));
        }
        iv.setLayoutParams(params);
    }

    public Drawable setDrawable(int operateType) {

        int drawable = R.drawable.bg_refund_detail_stay_gray;

        switch (operateType) {
            default:
            case 0:
                drawable = R.drawable.bg_refund_detail_stay_gray;
                break;
            case 1:
                drawable = R.drawable.icon_refund_detail_stay;
                break;
            case 3:
                drawable = R.drawable.icon_refund_detail_complete;
                break;

            case 5:
                drawable = R.drawable.icon_refund_detail_complete;
                break;
            case 7:
            case 10:
            case 12:
                drawable = R.drawable.icon_refund_detail_complete;
                break;
            case 2:
            case 4:
            case 6:
            case 8:
            case 11:
                drawable = R.drawable.icon_refund_detail_rejected;
                break;

        }
        return UiUtils.getDrawable(getContext(), drawable);
    }

    public int setColor(int operateType) {

        int color = R.color.text_9494A6;

        switch (operateType) {
            default:
            case 0:
            case 1:
            case 2:
            case 4:
            case 6:
            case 8:
            case 10:
            case 11:
            case 12:
                color = R.color.text_9494A6;
                break;
            case 3:
            case 5:
            case 7:
                color = R.color.base_color;
                break;
        }
        return getResources().getColor(color);
    }

    public interface ICountDownListener {
        void onCountDown();
    }

    public void setCountDownListener(ICountDownListener countDownListener) {
        this.countDownListener = countDownListener;
    }

    public interface IRefundButtonClickListener {
        void onClick(int refundClickType);
    }

    public void setOnRefundClickListener(IRefundButtonClickListener refundButtonClickListener) {
        this.refundButtonClickListener = refundButtonClickListener;
    }

    public class RefundButtonClickListener implements OnClickListener {
        public static final int REFUND_BUTTON_TYPE_REJECT = 0; //拒绝
        public static final int REFUND_BUTTON_TYPE_AGREE = 1; //同意
        private int refundButtonType;

        public RefundButtonClickListener(int refundButtonType) {
            this.refundButtonType = refundButtonType;
        }

        @Override
        public void onClick(View v) {
            if (refundButtonClickListener != null) {
                refundButtonClickListener.onClick(refundButtonType);
            }
        }
    }

}
