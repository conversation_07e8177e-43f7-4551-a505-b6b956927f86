package com.ybmmarket20.view

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFindItemBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.jgTrackSearchResourceClick
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.view_search_find_view.view.fblSearchFind

/**
 * 搜索引导页-搜索发现
 */
class SearchFindView(context: Context, attr: AttributeSet) : ConstraintLayout(context, attr) {

    var itemClickListener: ((SearchFindItemBean, Int) -> Unit)? = null
    var mEntrance:String = ""

    init {
        View.inflate(context, R.layout.view_search_find_view, this)
    }

    fun setData(itemList: List<SearchFindItemBean>?) {
        if (itemList.isNullOrEmpty()) {
            visibility = View.GONE
            return
        } else visibility = View.VISIBLE
        fblSearchFind.removeAllViews()
        itemList.forEachIndexed { index, s ->
            val tagView = createTagView(s.searchKeyword ?: "")
            fblSearchFind.addView(tagView)
            tagView.setOnClickListener { _ ->
                itemClickListener?.invoke(s, index)
                XyyIoUtil.track("action_Search_discover_word", hashMapOf(
                    "discover_word" to s.searchKeyword,
                    "discover_type" to s.type,
                    "sequence" to index + 1
                ))

                context.jgTrackSearchResourceClick(s.searchKeyword?:"",index,JGTrackManager.TrackSearchIntermediateState.SEARCH_MODULE_FIND,JGTrackManager.TrackSearchResult.TRACK_URL)
            }
        }
    }

    private fun createTagView(itemStr: String): TextView {
        val tabView = TextView(context)
        tabView.textSize = 13f
        tabView.setTextColor(UiUtils.getColor(R.color.color_292933))
        val shapeDrawable = GradientDrawable()
        shapeDrawable.setColor(UiUtils.getColor(R.color.color_f7f7f8))
        shapeDrawable.cornerRadius = UiUtils.dp2px(2).toFloat()
        tabView.background = shapeDrawable
        tabView.maxLines = 1
        tabView.maxEms = 13
        tabView.ellipsize = TextUtils.TruncateAt.END
        tabView.gravity = Gravity.CENTER
        tabView.setPadding(UiUtils.dp2px(8), 0, UiUtils.dp2px(8), 0)
        tabView.text = if (itemStr.length > 13) {
            "${itemStr.substring(0, 13)}..."
        } else itemStr
        val layoutParams =
            LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, UiUtils.dp2px(30))
        layoutParams.leftMargin = UiUtils.dp2px(10)
        layoutParams.bottomMargin = UiUtils.dp2px(10)
        tabView.layoutParams = layoutParams
        return tabView
    }
}