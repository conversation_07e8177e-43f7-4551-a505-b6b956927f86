package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaCodecList;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;

/**
 * 购物车 - 温馨提示
 */
public class ShowCertainPopWindow {

    private TextView tvTitle;
    private ImageView ivClose;
    private TextView tvContent;
    private TextView btnLeft;
    private TextView btnRight;
    private OnButtonClickListener mListener;

    private PopupWindow popwindow;
    private View contentView;

    public ShowCertainPopWindow() {
        LayoutInflater inflater = (LayoutInflater) BaseYBMApp.getApp().getCurrActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        contentView = inflater.inflate(R.layout.popwindow_show_certain, null, false);
        popwindow = new PopupWindow(contentView, RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT, true);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#00000000")));
        popwindow.setOutsideTouchable(false);
        initView();
    }

    protected void initView() {
        tvTitle = getView(R.id.tv_title);
        ivClose = getView(R.id.iv_close);
        btnLeft = getView(R.id.btn_left);
        btnRight = getView(R.id.btn_right);



    }
    public ShowCertainPopWindow setOutsideTouchable(boolean outsideTouchable) {
        popwindow.setOutsideTouchable(outsideTouchable);
        popwindow.setFocusable(outsideTouchable);
        return this;
    }

    public ShowCertainPopWindow setTitle(String title) {
        tvTitle.setText(title);
        return this;
    }

    public ShowCertainPopWindow setContent(String content) {
        tvContent.setText(content);
        return this;
    }

    public ShowCertainPopWindow setBtnLeft(String strLeftBtn) {
        btnLeft.setText(strLeftBtn);
        return this;
    }

    public ShowCertainPopWindow setBtnRight(String strRight) {
        btnRight.setText(strRight);
        return this;
    }

    public <T extends View> T getView(int viewId) {
        if (contentView == null) {
            return null;
        }
        try {
            return (T) contentView.findViewById(viewId);
        } catch (Throwable e) {
            return null;
        }
    }

    public void show(View token) {
        if (popwindow == null) {
            return;
        }
        try {
            if (isShow()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        try {
            popwindow.showAtLocation(token, Gravity.BOTTOM, 0, 0);
            // 设置popWindow的显示和消失动画
            popwindow.setAnimationStyle(R.style.mypopwindow_anim_style);
        } catch (Exception e) {
            return;
        }
        backgroundAlpha(0.5f);
        popwindow.update();
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                backgroundAlpha(1f);
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = ((BaseActivity) contentView.getContext()).getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        lp.dimAmount = 1 - bgAlpha;
        ((BaseActivity) contentView.getContext()).getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        ((BaseActivity) contentView.getContext()).getWindow().setAttributes(lp);
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }

    public void setOnButtonClickListener(OnButtonClickListener listener) {
        this.mListener = listener;
    }




    public interface OnButtonClickListener {
        void onLeftClick();

        void onRightClick();

        void onCloseClick();
    }

}
