package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.common.util.ConvertUtils;

import java.util.List;

/**
 * 二级标签view
 */

public class TagView extends LinearLayout {
    public TagView(Context context) {
        this(context, null);
    }

    public TagView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }


    private void initView() {
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER_VERTICAL | Gravity.LEFT);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     *             默认三个，
     */
    public void bindData(List<LabelIconBean> list) {
        bindData(list, 3);
    }

    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<LabelIconBean> list, int max, boolean gone) {
        removeAllViews();
        if (list == null || list.size() <= 0) {//添加一个空view,填充高度问题
            setVisibility(gone ? View.GONE : View.INVISIBLE);
            addView(createView(null));
            return;
        } else {
            setVisibility(View.VISIBLE);
        }
        if (max <= 0) {
            max = 3;
        }
        if (getMeasuredWidth() == 0) {
            postDelayed(() -> addTagChildView(list), 50);
        } else {
            addTagChildView(list);
        }
    }

    /**
     * 添加标签子View
     *
     * @param list
     */
    private void addTagChildView(List<LabelIconBean> list) {
        //防止延迟添加childView导致标签重复
        if (getChildCount() != 0) return;
        int totalChildWidth = 0;
        for (int i = 0; i < list.size(); i++) {
            if (i >= 3) return;
            View childView = createView(list.get(i));
            addView(childView);
            int tagViewWidth = getMeasuredWidth();
            LinearLayout.LayoutParams lp = (LayoutParams) childView.getLayoutParams();
            childView.measure(lp.width, lp.height);
            int childWidth = childView.getMeasuredWidth() + lp.leftMargin + lp.rightMargin;
            totalChildWidth += childWidth;
            if (totalChildWidth > tagViewWidth && i != 0) {
                removeViewAt(getChildCount() - 1);
                return;
            }
        }
    }


    /**
     * 绑定数据
     *
     * @param list 标签组
     * @param max  最多显示多少个，<=0 或者3 都用默认三个，
     */
    public void bindData(List<LabelIconBean> list, int max) {
        bindData(list, max, false);
    }

    private TextView createView(LabelIconBean bean) {
        TextView textView = new TextView(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, 0, ConvertUtils.dp2px(4), 0);
        textView.setGravity(Gravity.CENTER);
        textView.setSingleLine(true);
        textView.setEllipsize(TextUtils.TruncateAt.END);
        textView.setLayoutParams(params);
        textView.setPadding(ConvertUtils.dp2px(3), 0, ConvertUtils.dp2px(3), 0);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10);
        int drawRes = R.drawable.bg_brand_item_type4;
        int colorRes = R.color.brand_icon_type4;
        if (bean != null) {
            textView.setText(bean.name);
            switch (bean.uiType) {
                //标签显示类型 1:临期 ,2:券, 3:自定义1, 4:自定义2, 5医保
                case 1:
                    drawRes = R.drawable.bg_brand_item_type1;
                    colorRes = R.color.white;
                    break;
                case 2:
                    drawRes = R.drawable.shape_tag_bg_red;
                    colorRes = R.color.color_ff2121;
                    break;
                case 3:
                    drawRes = R.drawable.bg_brand_item_type3;
                    colorRes = R.color.brand_icon_type3;
                    break;
                case 5://医保
                    // 使用自定义GradientDrawable而不是预定义的背景资源
                    colorRes = R.color.light_blue;

                    // 创建一个新的GradientDrawable
                    GradientDrawable gradientDrawable = new GradientDrawable();
                    gradientDrawable.setColor(Color.parseColor("#0D00B3B3")); // 背景色，透明度0.05
                    gradientDrawable.setStroke(ConvertUtils.dp2px(0.5f), Color.parseColor("#8000B3B3")); // 边框颜色和宽度
                    gradientDrawable.setCornerRadius(ConvertUtils.dp2px(1.5f)); // 圆角半径

                    // 设置自定义背景
                    textView.setBackground(gradientDrawable);
                    textView.setTextColor(getResources().getColor(colorRes));
                    return textView; // 直接返回，跳过后面的通用设置

                case 4:
                    drawRes = R.drawable.bg_brand_item_type4;
                    colorRes = R.color.brand_icon_type4;
                    break;

                case 7://标签类型 7, "60天最低价"
                case 8://8, "区域毛利榜"
                case 10://10, "比上次购买时降xx元"
                case 11: //11, "比加入时降xx元"
//                    drawRes = R.drawable.bg_brand_item_type11;
//                    colorRes = R.color.brand_icon_type11;
//                    break;
                case 12://12, "品类点击榜"
                default:
                    drawRes = R.drawable.bg_item_data_tag_list_view;
                    colorRes = R.color.color_F19824;
                    break;
            }
        } else {
            textView.setText("");
            textView.setVisibility(View.INVISIBLE);
        }
        textView.setBackgroundResource(drawRes);
        textView.setTextColor(getResources().getColor(colorRes));
        return textView;
    }
}
