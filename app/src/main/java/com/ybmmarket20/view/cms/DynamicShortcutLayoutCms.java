package com.ybmmarket20.view.cms;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.bean.cms.ModuleItemFastEntryBean;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 快捷入口
 */
public class DynamicShortcutLayoutCms extends BaseDynamicLayoutCms<ModuleItemFastEntryBean> {

    private RecyclerView rvShortCut;
    private List<ModuleItemFastEntryBean> beanlist;
    // 显示行数,默认是两行
    private int spanCount;
    // 入口个数
    private int listCount;
    private ShortCutItemAdapter itemAdapter;
    private View viewbg;
    private View viewbefore;
    // 是否显示指示条
    private boolean showIndicator = true;
    // 一行显示最大个数
    private int maxCountOneLine = 0;
    // 模块可显示宽度
    private int moudleWidth;

    public DynamicShortcutLayoutCms(Context context) {
        this(context, null);
    }

    public DynamicShortcutLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void initViews() {
        rvShortCut = findViewById(R.id.rv_shortcurt);

        viewbg = findViewById(R.id.view_indicator_bg);
        viewbefore = findViewById(R.id.view_indicator_before);

        rvShortCut.setOverScrollMode(View.OVER_SCROLL_NEVER);
        rvShortCut.setNestedScrollingEnabled(false);

        beanlist = new ArrayList<>();
        itemAdapter = new ShortCutItemAdapter(R.layout.list_item_shortcut_cms, beanlist);
        spanCount = 2;
        rvShortCut.setLayoutManager(new GridLayoutManager(getContext(), spanCount, RecyclerView.HORIZONTAL, false));
        rvShortCut.setAdapter(itemAdapter);

        rvShortCut.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                if (showIndicator) {
                    moveIndicator(recyclerView, viewbefore);
                }
                super.onScrolled(recyclerView, dx, dy);
            }
        });
    }

    private void initViewAfaterDatas() {
        // 设置indicator宽度
        if (showIndicator) {
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) viewbg.getLayoutParams();
            layoutParams.width = computeIndicatorBgWith(maxCountOneLine);
            viewbg.setLayoutParams(layoutParams);
        }
        // 设置指示条颜色
        viewbg.setBackgroundColor(getColor(content.style.color1));
        viewbg.setAlpha(content.style.getOpacity1() / 100.0f);
        viewbefore.setBackgroundColor(getColor(content.style.color2));
        viewbg.setAlpha(content.style.getOpacity2() / 100.0f);


        moudleWidth = UiUtils.getScreenWidth();
        //margin[上，右，下，左]取值应为上右下左
        if (styles != null && styles.margin != null && styles.margin.size() >= 4) {
            moudleWidth = moudleWidth - dp2px(styles.margin.get(1)) - dp2px(styles.margin.get(3));
        }
        if (styles != null && styles.padding != null && styles.padding.size() >= 4) {
            moudleWidth = moudleWidth - dp2px(styles.padding.get(1)) - dp2px(styles.padding.get(3));
        }
        itemAdapter.setModuleWidth(moudleWidth);
        itemAdapter.notifyDataSetChanged();
    }

    @Override
    public void updateModuleItemStyle() {
        initViewAfaterDatas();
    }

    // 设置指示条
    private void moveIndicator(RecyclerView recyclerView, View viewbefore) {
        // 1. 手指滑动距离
        int scrollOffset = recyclerView.computeHorizontalScrollOffset();
        // 2. indicator 滑动距离
        // indicator滑动距离/indicator整体可滑动距离 = 手指滑动距离/recyclerview整体可移动宽度
        // reycyclerview 整体可移动宽度
        int rvWidth = moudleWidth / 5 * maxCountOneLine - moudleWidth;
        // indicator 整体可移动宽度
        int indicatorWith = computeIndicatorBgWith(maxCountOneLine) - dp2px(20);
        // indicator 滑动距离
        int indicatorTranslation = 0;
        if (rvWidth != 0) {
            indicatorTranslation = (int) (scrollOffset * indicatorWith / (rvWidth * 1.0f) + 0.5);
        }
        //Log.e("xyd", "indicator 滑动距离= " + indicatorTranslation + "indicator整体宽度：" + indicatorWith + "手指滑动距离：" + scrollOffset + "recyclerview full width = " + rvWidth);
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) viewbefore.getLayoutParams();
        layoutParams.leftMargin = indicatorTranslation;
        viewbefore.setLayoutParams(layoutParams);
    }


    private int getColumnCount(int listCount) {
        if (listCount <= 5 || listCount == 10) {
            return 5;
        } else if (5 < listCount && listCount < 10) {
            return listCount;
        } else {
            return (listCount - 10 + 1) / 2 + 5;
        }
    }

    /**
     * @param maxCount 一行最大显示个数
     * @return
     */
    private int computeIndicatorBgWith(int maxCount) {
        // 每多一个加 2dp
        return dp2px(20 + (maxCount - 5) * 4);
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }


    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_shortcut_cms;
    }

    /**
     * @param data 数据
     */
    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<ModuleItemFastEntryBean> data, boolean isUpdate) {
        if (data == null || data.size() <= 0) {
            setVisibility(View.GONE);
            return;
        }

        if (beanlist.size() > 0) {
            beanlist.clear();
        }
        beanlist.addAll(data);
        listCount = beanlist.size();
        int newSpanCount = 0;
        try {
            newSpanCount = Integer.parseInt(content.count);
        } catch (NumberFormatException e) {
            BugUtil.sendBug(new Throwable("快捷入口行数解析错误"));
        }
        if (spanCount != newSpanCount) {
            spanCount = newSpanCount;
            rvShortCut.setLayoutManager(new GridLayoutManager(getContext(), spanCount, RecyclerView.HORIZONTAL, false));
        }
        if (spanCount == 1) {
            maxCountOneLine = listCount;
        } else {
            maxCountOneLine = (listCount + 1) / 2;
        }
        if (maxCountOneLine <= 5) {
            showIndicator = false;
            viewbefore.setVisibility(View.GONE);
            viewbg.setVisibility(View.GONE);
        }
        initViewAfaterDatas();
    }

    @Override
    public void setStyle(int style) {

    }

    @Override
    public void setImageView(ImageView view, ModuleItemFastEntryBean bean) {
    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<ModuleItemFastEntryBean> moduleBean, List<ModuleItemFastEntryBean> data) {
        return true;
    }

    private class ShortCutItemAdapter extends YBMBaseAdapter<ModuleItemFastEntryBean> {

        int moduleWidth = UiUtils.getScreenWidth();

        public ShortCutItemAdapter(int layoutResId, List<ModuleItemFastEntryBean> data) {
            super(layoutResId, data);
        }

        public void setModuleWidth(int width) {
            moduleWidth = width;
        }

        @Override
        protected void bindItemView(YBMBaseHolder ybmBaseHolder, ModuleItemFastEntryBean itemBean) {
            ViewGroup.LayoutParams layoutParams = ybmBaseHolder.itemView.getLayoutParams();
            layoutParams.width = moduleWidth / 5;
            ybmBaseHolder.itemView.setLayoutParams(layoutParams);
            // 首页快接入口埋点 cms 埋点
            ybmBaseHolder.itemView.setTag(R.id.tag_action, itemBean.action);
            ybmBaseHolder.itemView.setTag(R.id.tag_2, ybmBaseHolder.getAdapterPosition());
            ybmBaseHolder.itemView.setTag(R.id.tag_title, itemBean.entry);
            ybmBaseHolder.itemView.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_SHORTCUT);

            ybmBaseHolder.itemView.setOnClickListener(itemClick);

            ImageView iv = (ImageView) ybmBaseHolder.getView(R.id.iv);
            ybmBaseHolder.setImageUrl(R.id.iv, getImgUrl(itemBean.title));

            TextView tvTittle = (TextView) ybmBaseHolder.getView(R.id.tv);
            if (TextUtils.isEmpty(itemBean.entry)) {
                tvTittle.setVisibility(View.GONE);
            } else {
                tvTittle.setText(itemBean.entry);
            }
            if (!TextUtils.isEmpty(itemBean.frontColor)) {//设置方案的颜色
                if (getColor(itemBean.frontColor) != 0) {
                    tvTittle.setTextColor(getColor(itemBean.frontColor));
                }
            }
        }

    }
}
