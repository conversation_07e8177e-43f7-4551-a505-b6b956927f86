package com.ybmmarket20.view.cms;

import android.content.Context;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.List;

/**
 * <AUTHOR> Brin
 * @date : 2019/7/19 - 13:58
 * @Description : 图片标题
 */
public class DynamicWonderActiveLayoutCms extends BaseDynamicLayoutCms {

    private ConstraintLayout clMoreActive;
    private TextView tvTitle01;
    private TextView tvTitle02;

    public DynamicWonderActiveLayoutCms(Context context) {
        super(context);
    }

    public DynamicWonderActiveLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicWonderActiveLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        clMoreActive = findViewById(R.id.cl_more_active);
        tvTitle01 = findViewById(R.id.tv_title01);
        tvTitle02 = findViewById(R.id.tv_title02);

    }

    @Override
    public boolean supportSetHei() {
        return true;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_wonder_active;
    }

    @Override
    public void setItemData(ModuleBeanCms moduleBean, List items, boolean isUpdate) {
        tvTitle01.setText(content.title1);
        tvTitle01.setTextColor(getColor(content.color1));
        tvTitle02.setText(content.title2);
        tvTitle02.setTextColor(getColor(content.color2));
        clMoreActive.setTag(R.id.tag_action, content.action);
        clMoreActive.setTag(R.id.tag_click_type, XyyIoUtil.ACTION_HOME_IMAGETITLE);
        clMoreActive.setOnClickListener(itemClick);
    }

    @Override
    public void setStyle(int style) {
        //默认 32 是什么鬼

    }

    @Override
    public void setImageView(ImageView view, Object bean) {
    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms moduleBean, List items) {
        return true;
    }
}
