package com.ybmmarket20.view.couponrelatedgoods

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R

/**
 * 凑单页筛选栏
 */

//选中状态-未选中
const val FILTER_ITEM_UNSELECTED = 0
//选中状态-选中
const val FILTER_ITEM_SELECTED = 1

//筛选类型-排序
const val FILTER_ITEM_TYPE_SORT = 0
//筛选类型-分类
const val FILTER_ITEM_TYPE_CATEGORY = 1
//筛选类型-商家
const val FILTER_ITEM_TYPE_BUSINESS = 2
//筛选类型-筛选
const val FILTER_ITEM_TYPE_FILTER = 3

abstract class BaseCouponRelatedGoodsFilterView(context: Context, attr: AttributeSet?) :
    ConstraintLayout(context, attr) {

    private var mClickListener: ((filterItem: FilterItem, v: View) -> Unit)? = null
    private var llItemFilter: LinearLayout

    init {
        View.inflate(context, R.layout.view_coupon_related_goods_filter, this)
        llItemFilter = findViewById(R.id.llItemFilter)
    }

    /**
     * 设置item
     */
    open fun setItemList(itemList: List<FilterItem>?, voucherId: String?, isDesignateShop: Boolean) {
        itemList?.forEach {
            val view = View.inflate(context, R.layout.view_coupon_related_filter_item, null)
            val lp = LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1.0F)
            it.view = view
            llItemFilter.addView(view, lp)
            val tvFilter = view.findViewById<TextView>(R.id.tvFilter)
            tvFilter.text = it.text
            view.setOnClickListener(ItemClickListener(it))
        }
    }

    /**
     * 设置按钮状态
     */
    fun setItemStatus(selectedStatus: Int, item: View, filterItem: FilterItem?) {
        filterItem?.let { it.status = selectedStatus }
        val tvFilter = item.findViewById<TextView>(R.id.tvFilter)
        val ivFilter = item.findViewById<ImageView>(R.id.ivFilter)
        tvFilter.setTextColor(Color.parseColor(if (selectedStatus == FILTER_ITEM_UNSELECTED) "#111111" else "#00B955"))
        ivFilter.setImageResource(if (selectedStatus == FILTER_ITEM_UNSELECTED) R.drawable.icon_coupon_related_goods_filter_down else R.drawable.icon_coupon_related_goods_filter_up)
    }

    /**
     * 设置每项的点击监听
     */
    fun setOnItemClickListener(clickListener: ((filterItem: FilterItem, v: View) -> Unit)?) {
        mClickListener = clickListener
    }

    /**
     * 设置有选中项
     */
    fun setChildSelected(item: View) {
        val tvFilter = item.findViewById<TextView>(R.id.tvFilter)
        val ivFilter = item.findViewById<ImageView>(R.id.ivFilter)
        tvFilter.setTextColor(Color.parseColor("#00B955"))
        ivFilter.setImageResource(R.drawable.icon_coupon_related_goods_filter_down)
    }

    inner class ItemClickListener(private val filterItem: FilterItem): OnClickListener {
        override fun onClick(v: View?) {
            v?.let {
                filterItem.status = FILTER_ITEM_SELECTED
                setItemStatus(filterItem.status, it, filterItem)
                mClickListener?.invoke(filterItem, it)
                handleFilterItems(filterItem, it)
            }
        }

    }

    data class FilterItem(
        val text: String,
        val id: Int,
        var status: Int = FILTER_ITEM_UNSELECTED,
        var view: View? = null
    )

    fun getFilterItemListWithoutBusiness():List<FilterItem> {
        return listOf(
            FilterItem("综合排序", FILTER_ITEM_TYPE_SORT),
            FilterItem("全部分类", FILTER_ITEM_TYPE_CATEGORY),
            FilterItem("筛选", FILTER_ITEM_TYPE_FILTER)
        )
    }

    fun getFilterItemList():List<FilterItem> {
        return listOf(
            FilterItem("综合排序", FILTER_ITEM_TYPE_SORT),
            FilterItem("全部分类", FILTER_ITEM_TYPE_CATEGORY),
            FilterItem("商家", FILTER_ITEM_TYPE_BUSINESS),
            FilterItem("筛选", FILTER_ITEM_TYPE_FILTER)
        )
    }

    abstract fun handleFilterItems(filterItem: FilterItem, v: View)

}