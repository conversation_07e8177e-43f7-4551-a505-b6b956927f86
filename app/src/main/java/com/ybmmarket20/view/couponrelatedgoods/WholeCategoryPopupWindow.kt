package com.ybmmarket20.view.couponrelatedgoods

import android.text.TextUtils
import android.view.View
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.ProductCategoryViewV2

/**
 * 全部分类
 */
class WholeCategoryPopupWindow: BaseFilterPopWindow() {

    private var onSelectListener: OnSelectListener? = null
    private var onResultListener: OnResultListener? = null
    private var mCategoryIds: String? = null

    fun setOnSelectListener(onSelectListener: OnSelectListener?) {
        this.onSelectListener = onSelectListener
    }

    fun setOnResultListener(onResultListener: OnResultListener?) {
        this.onResultListener = onResultListener
    }

    interface OnSelectListener {
        fun getValue(categoryIds: Set<String?>?)

        fun OnDismiss(multiSelectStr: String?)
    }

    interface OnResultListener {
        fun onConfirm(categoryIds: String?)

        fun onDismiss(categoryIds: String?)
    }

    private var mPcvList: ProductCategoryViewV2? = null

    private val selectedIds: MutableSet<Int> = HashSet()

    override fun getLayoutId(): Int {
        return R.layout.pop_layout_all_product_v2
    }

    override fun initView() {
        mPcvList = getView<ProductCategoryViewV2>(R.id.pcv_list)

        mPcvList?.setOnSelectListener(ProductCategoryViewV2.OnSelectListener { categoryIds ->
            if (mOnSelectListener != null && categoryIds != null) {
                if (categoryIds.isEmpty()) {
                    mOnSelectListener.getValue(SearchFilterBean("", ""))
                    return@OnSelectListener
                }
                val categoryBuilder = StringBuilder()
                for (categoryId in categoryIds) {
                    categoryBuilder.append(categoryId).append(",")
                }
                val categoryStr = categoryBuilder.substring(0, categoryBuilder.length - 1)
                mOnSelectListener.getValue(SearchFilterBean("", categoryStr))
            }
        })
        getView<View>(R.id.btn_affirm).setOnClickListener { v: View? ->
            val categoryIds = mPcvList?.getSelectedCategoryIds()
            if (onSelectListener != null) onSelectListener!!.getValue(categoryIds)
            dismiss()
            if (onSelectListener != null) onSelectListener!!.OnDismiss("")
            mCategoryIds = categoryIds?.joinToString(",")
            onResultListener?.onConfirm(mCategoryIds)
        }

        super.setOnSelectListener(object: BaseFilterPopWindow.OnSelectListener{
            override fun getValue(show: SearchFilterBean?) {}

            override fun OnDismiss(multiSelectStr: String?) {
                onResultListener?.onDismiss(mCategoryIds)
            }
        })

        getView<View>(R.id.btn_reset).setOnClickListener { v: View? ->
            resetPosition()
        }
    }

    //重置商品分类
    private fun resetPosition() {
        mPcvList?.resetAllPosition()
        if (onSelectListener != null) onSelectListener!!.getValue(HashSet())
    }

    fun setSelectedIdsStr(selectedIdsStr: String) {
        if (TextUtils.isEmpty(selectedIdsStr)) return
        val idArr = selectedIdsStr.split(",".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        selectedIds.clear()
        for (s in idArr) {
            try {
                selectedIds.add(s.toInt())
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun show(token: View?, selectedIdsStr: String?) {
        mPcvList?.doGetNewsContent(selectedIdsStr)
        super.show(token)
    }

    override fun show(token: View?) {
        mPcvList?.doGetNewsContent(null)
        super.show(token)
    }

    fun show2(token: View?, id: String?) {
        mPcvList?.doGetNewsContent(1, "planningScheduleId", id, null)
        super.show(token)
    }

    fun showWithHandleIds(token: View?) {
        mPcvList?.doGetNewsContent(mCategoryIds?: "")
        super.show(token)
    }

    fun showShopCategory(token: View?, orgId: String?) {
        val requestParams = RequestParams()
        requestParams.url = AppNetConfig.SHOP_GOODS_CATEGORY
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("orgId", orgId)
        mPcvList?.getContentByUrl(requestParams)
        super.show(token)
    }

    fun showShopCategory(orgId: String?) {
        val requestParams = RequestParams()
        requestParams.url = AppNetConfig.SHOP_GOODS_CATEGORY
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("orgId", orgId)
        mPcvList?.getContentByUrl(requestParams)
    }

    fun setOnLevelItemClickListener(levelItemClickListener: ProductCategoryViewV2.LevelItemClickListener?) {
        if (levelItemClickListener == null) return
        mPcvList?.setOnLevelItemClickListener(levelItemClickListener)
    }
}