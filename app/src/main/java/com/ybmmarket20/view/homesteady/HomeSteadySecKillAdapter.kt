package com.ybmmarket20.view.homesteady

import android.graphics.Paint
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.StyleSpan
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.SeckillProduct
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.openUrl
import com.ybmmarket20.view.homesteady.callback.ISeckillAnalysisCallback

/**
 * 首页秒杀模块
 */

// 普通样式
const val SECKILL_NORMAL = 0
// 更多
const val SECKILL_MORE = 1

class HomeSteadySecKillAdapter(
        list: MutableList<SeckillProduct>
        , private var analysisCallback: ISeckillAnalysisCallback?
        , private var showType: Int
        , private val jumUrl: String?
): YBMBaseMultiItemAdapter<SeckillProduct>(list) {

    private var moreUrl: String? = ""

    private var licenseStatus = -1
    private val traceProductData = SparseArray<String>()

    init {
        addItemType(SECKILL_NORMAL, R.layout.item_seckill_goods)
        addItemType(SECKILL_MORE, R.layout.item_seckill_more)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SeckillProduct?) {
        t?.also {
            when (t.itemType) {
                SECKILL_NORMAL -> {
                    val iv = baseViewHolder.getView<ImageView>(R.id.iv)
                    val tag = baseViewHolder.getView<TextView>(R.id.tv_tag)
                    val name = baseViewHolder.getView<TextView>(R.id.tv_goods_name)
                    val spec = baseViewHolder.getView<TextView>(R.id.tv_goods_spec)
                    val price = baseViewHolder.getView<TextView>(R.id.tv_goods_price)
                    val discount = baseViewHolder.getView<TextView>(R.id.tv_goods_discount)
                    name.text = it.showName
                    spec.text = it.spec
                    val priceStr = setGoodsPriceStatus(price, it, licenseStatus, mContext)
                    if (priceStr.isEmpty()) {
                        setFont(price, "￥${it.skuPrice}")
                        price.setTextColor(ContextCompat.getColor(mContext, R.color.color_ff2121))
                        discount.visibility = View.VISIBLE
                    } else {
                        price.setTextColor(ContextCompat.getColor(mContext, R.color.color_ff982c))
                        discount.visibility = View.GONE
                    }
                    discount.text = "￥${it.fob}"
                    discount.paintFlags = discount.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                    ImageHelper.with(mContext).load(it.imageUrl).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().into(iv)
                    when (convertActSkStatus(it.actSkStatus)) {
                        SECKILL_STATUS_WAIT -> {
                            //即将开始
                            tag.text = "限量${it.totalQty}${it.productUnit}"
                        }
                        SECKILL_STATUS_END -> {
                            //已结束
                            tag.visibility = View.GONE
                        }
                        SECKILL_STATUS_HAVE_IN_HAND -> {
                            //进行中
                            tag.text = "已售出${it.inProgressPayRadio}"
                        }
                    }
                    baseViewHolder.itemView.setOnClickListener {_ ->
                        //跳转到商品详情
                        val url = "ybmpage://productdetail/" + it.id
                        openUrl(url, BaseFlowData(it.sptype, it.spid, it.sid, "", ""))
//                        val action = t.jumpUrl
                        val action = ""
//                        analysisCallback?.onHomeSteadyAnalysisSeckillGoodsClick(url, baseViewHolder.layoutPosition, "$showType", it.id)

                        //跳转到更多秒杀
//                        openUrl(jumUrl, BaseFlowData(it.sptype, it.spid, it.sid, "", ""))
//                        RoutersUtils.open(t.jumpUrl)
                        analysisCallback?.onHomeSteadyAnalysisSeckillGoodsClick(t.jumpUrl, baseViewHolder.layoutPosition, "$showType", it.id, action)
                    }
//                    handleAnalysisExposure(baseViewHolder, it, "ybmpage://productdetail/${t.id}")
                    handleAnalysisExposure(baseViewHolder, it, t.jumpUrl?: "")
                }
                SECKILL_MORE -> {
                    baseViewHolder.itemView.setOnClickListener {
                        RoutersUtils.open(moreUrl)
                        analysisCallback?.onHomeSteadyAnalysisSeckillMoreClick(moreUrl, "$showType")
                    }
                }
            }

        }
    }

    fun setMoreUrl(url: String?) {
        this.moreUrl = url
    }

    fun setLicenseStatus(licenseStatus: Int) {
        this.licenseStatus = licenseStatus
    }

    private fun setFont(tv: TextView?, price: String?) {
        val spannableStringBuilder = SpannableStringBuilder(price)
        if (price?.length ?:0 >= 2) {
            spannableStringBuilder.setSpan(AbsoluteSizeSpan(ScreenUtils.dip2px(mContext, 10f)), 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            spannableStringBuilder.setSpan(AbsoluteSizeSpan(ScreenUtils.dip2px(mContext, 13f)), 1, price?.length?:2, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            spannableStringBuilder.setSpan(StyleSpan(Typeface.BOLD), 1, price?.length?:2, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            tv?.text = spannableStringBuilder
        }
    }

    /**
     * 处理商品曝光
     */
    private fun handleAnalysisExposure(baseViewHolder: YBMBaseHolder, t: SeckillProduct, jumpUrl: String) {
        if (traceProductData[baseViewHolder.adapterPosition] == null) {
            analysisCallback?.onHomeSteadyAnalysisSeckillGoodsExposure(jumpUrl, baseViewHolder.adapterPosition, "$showType", t.id)
            traceProductData.put(baseViewHolder.adapterPosition, t.id)
        }
    }

    private fun convertActSkStatus(actSkStatus: Int): Int {
        return when (actSkStatus) {
            0 -> SECKILL_STATUS_WAIT
            1 -> SECKILL_STATUS_HAVE_IN_HAND
            else -> SECKILL_STATUS_END
        }
    }

}