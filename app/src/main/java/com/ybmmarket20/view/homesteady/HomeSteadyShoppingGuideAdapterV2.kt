package com.ybmmarket20.view.homesteady

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.text.Spannable
import androidx.core.content.ContextCompat
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.*
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.android.synthetic.main.layout_home_steady_seckill.view.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2020-05-11
 * @description 首页导购适配器
 */

const val SECKILL_STATUS_WAIT = 1           //未开始
const val SECKILL_STATUS_HAVE_IN_HAND = 2   //进行中
const val SECKILL_STATUS_END = 3            //已结束
const val SECKILL_CONVERSE = 4            // 将秒杀数据结构转化成其他结构

class HomeSteadyShoppingGuideAdapterV2(
    var shoppingGuideContext: Context,
    var list: MutableList<ShoppingGuideAllItem>,
    var resId: Int
) : YBMBaseAdapter<ShoppingGuideAllItem>(resId, list) {

    private var licenseStatus = -1
    private var analysisCallback: ((String, Int, String, String) -> Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShoppingGuideAllItem?) {
        t?.also {
            val minorTitle = baseViewHolder?.getView<TextView>(R.id.tv_minor_title)
            val ivGoods = baseViewHolder?.getView<ImageView>(R.id.iv_minor_title)
            val majorTitle = baseViewHolder?.getView<TextView>(R.id.tv_major_title)
            majorTitle?.text = if (t.moduleTitle?.length ?:0 > 4) "${t.moduleTitle?.substring(0, 4)}" else t.moduleTitle
            if (isSeckill(t)) {
                // 秒杀
                baseViewHolder?.getView<LinearLayout>(R.id.llTime)?.visibility = View.VISIBLE
                minorTitle?.visibility = View.GONE
                ivGoods?.visibility = View.GONE
            } else {
                // 非秒杀
                baseViewHolder?.getView<LinearLayout>(R.id.llTime)?.visibility = View.GONE
                minorTitle?.visibility = View.GONE
                ivGoods?.visibility = View.GONE
                if (t.describeType == 1) {
                    //图片
                    ivGoods?.visibility = View.VISIBLE
                    setImage(baseViewHolder, t.describeImageUrl, R.id.iv_minor_title)
                } else if (t.describeType == 0) {
                    //文字
                    minorTitle?.visibility = View.VISIBLE
                    minorTitle?.text = t.describe
                    if (baseViewHolder?.adapterPosition?.rem(2) ?: 0 == 0) {
                        minorTitle?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_5272e7))
                    } else {
                        minorTitle?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_FF3635))
                    }
                }
            }

            if (t.itemInfo?.size ?: 0 >= 1) {
                val statusStr = t.itemInfo?.get(0)?.let { it1 ->
                    setGoodsPriceStatus(baseViewHolder?.getView(R.id.tv_first_price), it1, licenseStatus, mContext)
                }?: ""
                if (statusStr.isEmpty()) {
//                    baseViewHolder?.setText(R.id.tv_first_price, if(isSeckill(t)) "￥${t.itemInfo?.get(0)?.skuPrice}" else "￥${t.itemInfo?.get(0)?.fob}")
                    setFont(baseViewHolder?.getView(R.id.tv_first_price), if(isSeckill(t)) "￥${t.itemInfo?.get(0)?.skuPrice}" else "￥${t.itemInfo?.get(0)?.fob}")
                    baseViewHolder?.getView<TextView>(R.id.tv_first_price)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                } else {
                    baseViewHolder?.getView<TextView>(R.id.tv_first_price)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff982c))
                }
                baseViewHolder?.setText(R.id.tv_first_name, t.itemInfo?.get(0)?.showName)
                setImage(baseViewHolder, t.itemInfo?.get(0)?.imageUrl, R.id.iv_product1)
            }
            if (t.itemInfo?.size ?: 0 >= 2) {
                val statusStr = t.itemInfo?.get(1)?.let { it1 ->
                    setGoodsPriceStatus(baseViewHolder?.getView(R.id.tv_second_price), it1, licenseStatus, mContext)
                }?: ""
                if (statusStr.isEmpty()) {
//                    baseViewHolder?.setText(R.id.tv_second_price, if (isSeckill(t)) "￥${t.itemInfo?.get(1)?.skuPrice}" else "￥${t.itemInfo?.get(1)?.fob}")
                    setFont(baseViewHolder?.getView(R.id.tv_second_price), if (isSeckill(t)) "￥${t.itemInfo?.get(1)?.skuPrice}" else "￥${t.itemInfo?.get(1)?.fob}")
                    baseViewHolder?.getView<TextView>(R.id.tv_second_price)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                } else {
                    baseViewHolder?.getView<TextView>(R.id.tv_second_price)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff982c))
                }
                setImage(baseViewHolder, t.itemInfo?.get(1)?.imageUrl, R.id.iv_product2)
                baseViewHolder?.setText(R.id.tv_second_name, t.itemInfo?.get(1)?.showName)
            }
            val llTime = baseViewHolder?.getView<LinearLayout>(R.id.llTime)
            val tvTimeTitle = baseViewHolder?.getView<TextView>(R.id.tvTimeTitle)
            val tvTimeRemains = baseViewHolder?.getView<TextView>(R.id.tvTimeRemains)
            val resources = mContext.resources
            if (isSeckill(t)) {
                tvTimeTitle?.text = getSeckillName(t.seckillInfo)
            }
            when (t.seckillInfo?.status) {
                SECKILL_STATUS_WAIT -> {
                    //即将开始
                    llTime?.background = resources.getDrawable(R.drawable.shape_home_time_green_parent, null)
                    tvTimeTitle?.background = resources.getDrawable(R.drawable.shape_home_time_green, null)
                    tvTimeRemains?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_00b377))
                    tvTimeRemains?.text = t.seckillInfo?.time
                }
                SECKILL_STATUS_END -> {
                    //已结束
                    llTime?.background = resources.getDrawable(R.drawable.shape_home_time_gray_parent, null)
                    tvTimeTitle?.background = resources.getDrawable(R.drawable.shape_home_time_gray, null)
                    tvTimeRemains?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_c2c2ca))
                    tvTimeRemains?.text = "已结束"
                }
                SECKILL_STATUS_HAVE_IN_HAND -> {
                    //进行中
                    llTime?.background = resources.getDrawable(R.drawable.shape_home_time_red_parent, null)
                    tvTimeTitle?.background = resources.getDrawable(R.drawable.shape_home_time_red, null)
                    tvTimeRemains?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                    tvTimeRemains?.text = t.seckillInfo?.time
                }
            }

            baseViewHolder?.getConvertView()?.setOnClickListener {
                val action = t.jumpUrl ?: ""
                RoutersUtils.open(t.jumpUrl)
                var skuId = ""
                t.itemInfo?.forEach {
                    skuId += "_${it.id ?: ""}"
                }
                if (skuId.isNotEmpty()) skuId = skuId.substring(1, skuId.length)
                clickEvent(action, baseViewHolder.adapterPosition, t.moduleTitle ?: "", skuId)
            }
        }
    }

    private fun setFont(tv: TextView?, price: String?) {
        val spannableStringBuilder = SpannableStringBuilder(price)
        if (price?.length ?:0 >= 2) {
            spannableStringBuilder.setSpan(AbsoluteSizeSpan(ScreenUtils.dip2px(mContext, 10f)), 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            spannableStringBuilder.setSpan(AbsoluteSizeSpan(ScreenUtils.dip2px(mContext, 13f)), 1, price?.length?:2, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            spannableStringBuilder.setSpan(StyleSpan(Typeface.BOLD), 1, price?.length?:2, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
            tv?.text = spannableStringBuilder
        }
    }

    /**
     * 是否是秒杀
     */
    private fun isSeckill(t: ShoppingGuideAllItem?): Boolean = t?.seckillInfo?.status == com.ybmmarketkotlin.adapter.SECKILL_STATUS_WAIT
            || t?.seckillInfo?.status == SECKILL_STATUS_HAVE_IN_HAND
            || t?.seckillInfo?.status == SECKILL_STATUS_END

    /**
     * 点击事件埋点
     */
    private fun clickEvent(action: String, offset: Int, text: String, skuId: String) {
        try {
//            val obj = JSONObject()
//            obj.apply {
//                put("action", action)
//                put("offset", offset + 1)
//                put("text", text)
//                put("sku_id", skuId)
//            }
//            XyyIoUtil.track("action_Home_Card", obj)
            analysisCallback?.invoke(action, offset, text, skuId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置价钱
     */
    private fun setPrice(tv: TextView?, productItem: ProductAllItem?, licenseStatus: Int) {
        try {
            val price = productItem?.skuPrice ?: ""
            val fob = productItem?.fob ?: ""
            if ((TextUtils.isEmpty(price)&& TextUtils.isEmpty(fob))
                ||licenseStatus==1||licenseStatus==5 //【价格认证资质可见 licenseStatus 1资质未提交 5首营资质审核中 】
                ||(productItem?.isControl == 1 && !productItem.isPurchase) // 【暂无购买权限 productItem?.isControl == 1 && !productItem.isPurchase】
                ||(productItem?.isOEM == true&&productItem.signStatus != 1)//【价格签署协议可见productItem?.isOEM == true&&productItem.signStatus != 1是否签署协议】
                ||(productItem?.showAgree == 0)){ //【价格签署协议可见//是否符合协议标准展示价格,1:符合0:不符合seckillProduct?.showAgree == 0】
                tv?.visibility=View.GONE
                return
            }
            tv?.visibility=View.VISIBLE
            tv?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
            tv?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
            val builder = SpannableStringBuilder("¥$price")
            if (price.isNotEmpty()) {
                val styleSpan = StyleSpan(Typeface.BOLD)
                builder.setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(9f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                if (price.contains(".")){
                    builder.setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(9f)),price.indexOf(".")+1,price.length+1,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    builder.setSpan(styleSpan, 1, price.indexOf(".")+1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }else{
                    builder.setSpan(styleSpan, 0, price.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
            if (fob.isNotEmpty()) {
                val fobBuilder = SpannableStringBuilder("¥$fob")
                val fobSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(9f))
                val colorSpan = ForegroundColorSpan(ContextCompat.getColor(shoppingGuideContext, R.color.color_4d222222))
                val strikeSpan = StrikethroughSpan()
                fobBuilder.setSpan(fobSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(strikeSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(colorSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.append(fobBuilder)
            }
            tv?.text = builder
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置商品图片
     */
    private fun setImage(baseViewHolder: YBMBaseHolder?, img: String?, imageViewId: Int) {
        val imageUrl = img?.let {
            if (img.startsWith("http")) {
                img
            } else {
                "${AppNetConfig.LORD_IMAGE}${img}"
            }
        } ?: ""
        ImageHelper.with(shoppingGuideContext).load(imageUrl).diskCacheStrategy(DiskCacheStrategy.SOURCE)
            .dontAnimate().into(baseViewHolder?.getView(imageViewId))
    }

    /**
     * 获取xx点场次
     */
    @SuppressLint("SimpleDateFormat")
    fun getSeckillName(seckill: SeckillAllInfo?): String? = seckill?.let {
        val cal = Calendar.getInstance()
        cal.time = Date(it.startDate)
        val hours = SimpleDateFormat("HH").format(Date(it.startDate))
        return "${hours}点场"
    }

    /**
     * 设置一审状态
     */
    fun setLicenseStatus(licenseStatus: Int) {
        this.licenseStatus = licenseStatus
    }

    fun setAnalysisCallback(callback: (action: String, offset: Int, text: String, sku_id: String) -> Unit) {
        analysisCallback = callback
    }

}