package com.ybmmarket20.view.homesteady.newpage

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.xyy.canary.utils.LogUtil
import com.ybmmarket20.R
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.jgTrackHomeBtnClick
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.ROUTER_CAPTURE
import com.ybmmarket20.constant.ROUTER_VOICE_SEARCH_PRODUCT
import com.ybmmarket20.databinding.LayoutHomeSteadySearchNewBinding
import com.ybmmarket20.home.newpage.adapter.SearchContentBannerAdapter
import com.ybmmarket20.home.newpage.bean.HomeSearchContent
import com.ybmmarket20.home.newpage.bean.HomeSearchContentResponse
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.IHomeSteady
import com.youth.banner.Banner
import com.youth.banner.listener.OnBannerListener
import com.youth.banner.listener.OnPageChangeListener
import kotlinx.android.synthetic.main.layout_home_steady_search_new.view.banner_content


/**
 * <AUTHOR>
 * @date 2024-04-02
 * @description 首页搜索框头部 首页改版最新的搜索框
 */
class HomeSteadySearchView3(mContext: Context, attr: AttributeSet) : HomeSteadySearchAnalysisView3(mContext, attr), IHomeSteady {

    private lateinit var mBinding: LayoutHomeSteadySearchNewBinding

    var jgspid = ""
    var mItemClickListener: ((content:HomeSearchContentResponse?,position:Int,isSearchIcon:Boolean)->Unit)? = null
    //极光埋点 点击时 资源埋点的回调
    var mJgResourceClickListener: (()->Unit)? = null
    var isHomePage:Boolean = false

    var mJgExposureCallBack: ((HomeSearchContentResponse?,Int)->Unit)? = null
    //key 热词  value:当时埋点的时间戳
    private val hotKeyTrackMap = hashMapOf<String, Long>()

    companion object {
        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
        private const val TAG = "HomeSteadySearchView3"
    }

    init {
        var view:View? = null
        if (getLayoutId() != 0) {
            mBinding = DataBindingUtil.inflate(LayoutInflater.from(mContext),getLayoutId(),this,true)
        }
        initialize(view)
    }

    private fun getPageId() = if (isHomePage) TrackManager.TrackHome.TRACK_HOME_PAGE_ID else TrackManager.TrackHome.TRACK_HOME_TAB_PAGE_ID

    @SuppressLint("ClickableViewAccessibility")
    fun initialize(root: View?) {

        mBinding.apply {
            ivScan.setOnClickListener {
                val map = HashMap<String, Int>().apply {
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_SCAN_CLICK, map)
                mContext.jgTrackHomeBtnClick(JGTrackManager.TrackHomePage.TRACK_HOME_OUT_URL,"顶部搜索栏","搜索框-扫一扫")
                mJgResourceClickListener?.invoke()
                RoutersUtils.open("${ROUTER_CAPTURE}?${IntentCanst.JG_JGSPID}=$jgspid")
                trackSearchScanClick()
            }

            ivVoice.setOnClickListener { // xyyio 埋点
                val content = HashMap<String, String>();
                content["entry"] = "search_main"
                content["merchantId"] = SpUtil.getMerchantid()
                XyyIoUtil.track(XyyIoUtil.ACTION_VOICE_CLICK, content)

                val map = HashMap<String, Int>().apply { put(TrackManager.FIELD_PAGE_ID, getPageId()) }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_VOICE_INPUT_CLICK, map)
                mContext.jgTrackHomeBtnClick(JGTrackManager.TrackHomePage.TRACK_HOME_OUT_URL,"顶部搜索栏","搜索框-语音")
                mJgResourceClickListener?.invoke()

                RoutersUtils.open("${ROUTER_VOICE_SEARCH_PRODUCT}?${IntentCanst.JG_JGSPID}=$jgspid")
                trackSearchVoiceClick()
            }

            ivMessage.setOnClickListener {
                Message.openMessagePage()
                val map = HashMap<String, Int>().apply { put(TrackManager.FIELD_PAGE_ID,getPageId()) }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_MESSAGE_CLICK, map)
                mContext.jgTrackHomeBtnClick(JGTrackManager.TrackHomePage.TRACK_HOME_OUT_URL,"顶部搜索栏","消息")
                mJgResourceClickListener?.invoke()
                trackMessageClick()
            }

            ivSearchIcon.setOnClickListener {
                mItemClickListener?.invoke(null,0,true)
                mJgResourceClickListener?.invoke()
                trackSearchIconClick()
            }
        }

    }

    fun setSearchContentList(
            list: ArrayList<HomeSearchContentResponse>,
            scanColor: String? = "",
            scanTransparency: String? = ""
    ) {
        mBinding.apply {
            bannerContent.setAdapter(SearchContentBannerAdapter((list.map { HomeSearchContent(content = it, scanColor = scanColor, scanTransparency = scanTransparency, trackData = it.trackData) } as? ArrayList<HomeSearchContent>)
                    ?: arrayListOf()).apply {
            }, true).setLoopTime(3000L).setOrientation(Banner.VERTICAL).setOnBannerListener(object : OnBannerListener<HomeSearchContent> {
                override fun OnBannerClick(
                        data: HomeSearchContent,
                        position: Int
                ) {
                    data.content?.let {
                        mItemClickListener?.invoke(it, position, false)
                        mJgResourceClickListener?.invoke()
                        trackSearchViewClick(context, data.trackData, position, data.content.hotWord, trackData)
                    }
                }
            }).setUserInputEnabled(false)

            bannerContent.addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                }

                override fun onPageSelected(position: Int) {
                    (banner_content.adapter.getData(position) as HomeSearchContent).apply {

                        hotKeyTrackMap[this.content?.hotWord?:""]?.let {
                            if (System.currentTimeMillis() - it > TRACK_DURATION){
                                mJgExposureCallBack?.invoke(this.content,position)
                                hotKeyTrackMap[this.content?.hotWord?:""] = System.currentTimeMillis()
                            }
                        }?: kotlin.run {
                            mJgExposureCallBack?.invoke(this.content,position)
                            hotKeyTrackMap[this.content?.hotWord?:""] = System.currentTimeMillis()
                        }
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {
                }
            })

            //没有数据就隐藏 触发空数据时外层的点击事件
            bannerContent.isVisible = list.size > 0
            if (list.size > 0){ //第一次需要手动曝光事件
                mJgExposureCallBack?.invoke(list[0],0)
            }
        }
    }

    //设置搜索框的颜色和粗细
    fun setSearchStroke(
            width: Int,
            @ColorInt color: Int
    ) {
        (mBinding.clSearch.background as? GradientDrawable)?.setStroke(width, color)
        invalidate()
    }

    fun setSearchStrokeAlpha(mAlpha: Float) {
        (mBinding.clSearch.background as? GradientDrawable)?.alpha = (mAlpha * 255).toInt()
    }

    fun setDefaultSearchStroke() {
        (mBinding.clSearch.background as? GradientDrawable)?.setStroke(1.dp, getColorById(R.color.color_00b377))
        setSearchStrokeAlpha(1f)
        invalidate()
    }


    //设置搜索框的颜色和粗细
    fun setSearchBg(
            @ColorInt color: Int
    ) {
        (mBinding.clSearch.background as? GradientDrawable)?.setColor(color)
        invalidate()
    }

    fun setSearchBgAlpha(mAlpha: Float) {
        (mBinding.clSearch.background as? GradientDrawable)?.alpha = (mAlpha * 255).toInt()
    }

    fun setDefaultSearchBg() {
        (mBinding.clSearch.background as? GradientDrawable)?.setColor(Color.WHITE)
        setSearchBgAlpha(1f)
        invalidate()
    }

    //设置消息右上角背景颜色
    fun setSearchMessageBubbleBg(@ColorInt color: Int) {
        (mBinding.tvHomeSteadyMessageBubble.background as? GradientDrawable)?.setColor(color)
        invalidate()
    }

    fun setSearchMessageBubbleBgAlpha(mAlpha: Float) {
        mBinding.tvHomeSteadyMessageBubble.alpha = mAlpha
        invalidate()
    }


    fun setDefaultSearchMessageBubbleBg() {
        (mBinding.tvHomeSteadyMessageBubble.background as? GradientDrawable)?.setColor(getColorById(R.color.color_ff2121))
        setSearchMessageBubbleBgAlpha(1f)
        invalidate()
    }

    //设置消息图片
    fun setSearchMessageBg(url: String) {
        mContext.glideLoadWithPlaceHolder(url,mBinding.ivMessage,R.drawable.transparent,R.drawable.icon_home_steady_message)
        invalidate()
    }

    fun setDefaultSearchMessageBg() {
        mContext.glideLoadWithPlaceHolder("",mBinding.ivMessage,R.drawable.transparent,R.drawable.icon_home_steady_message)
        setSearchMessageAlpha(1f)
        invalidate()
    }

    fun setSearchMessageAlpha(mAlpha:Float){
        mBinding.ivMessage.alpha = mAlpha
        invalidate()
    }

    //修改搜索框图片
    fun setSearchIcon(url: String) {
        mContext.glideLoadWithPlaceHolder(url,mBinding.ivSearchIcon,R.drawable.transparent,R.drawable.home_ic_search)
        invalidate()
    }

    //默认搜索框图片
    fun setDefaultSearchIcon() {
        mContext.glideLoadWithPlaceHolder("",mBinding.ivSearchIcon,R.drawable.transparent,R.drawable.home_ic_search)
        invalidate()
    }

    fun hasSearchContent(): Boolean = (mBinding.bannerContent.adapter?.itemCount ?: 0) > 0


    fun getLayoutId(): Int = R.layout.layout_home_steady_search_new

    /**
     * 获取消息气泡view
     */
    fun getBubbleView(): TextView? = mBinding.tvHomeSteadyMessageBubble

    override fun initPlaceHold() {
    }

}