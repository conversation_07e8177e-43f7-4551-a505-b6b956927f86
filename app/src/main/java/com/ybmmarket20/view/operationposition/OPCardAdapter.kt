package com.ybmmarket20.view.operationposition

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.common.JgOperationPositionInfo
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 运营位
 */
class OPCardAdapter(data: MutableList<SearchRowsBean>) :
    YBMBaseListAdapter<SearchRowsBean>(R.layout.item_operation_position, data) {

    //运营位列表 商品曝光事件的回调
    var mTrackViewListener:( (searchBean: SearchRowsBean,productPosition:Int)->Unit)? = null
    //商品点击事件的回调
    var mTrackClickListener:( (searchBean:SearchRowsBean,productPosition:Int,isBtnClick:Boolean,btnContent:String,number:Int?)->Unit)? = null

    var jgTrackBean:JgTrackBean? = null
    var jGPageListCommonBean: JGPageListCommonBean? = null

    fun bindItemViewWithBackground(baseViewHolder: YBMBaseHolder?, t: SearchRowsBean?, bgResId: Int = -1) {
        t?.outPosition = baseViewHolder?.bindingAdapterPosition?:0
        bindItemView(baseViewHolder, t)
        if (bgResId == -1) return
        whenAllNotNull(baseViewHolder, t) { holder, _ ->
            val sopView = holder.getView<OPCardView>(R.id.sopView)
            sopView.setCardViewBg(bgResId)
        }
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SearchRowsBean?){
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            bean.operationInfo?:return@whenAllNotNull

            val sopView = holder.getView<OPCardView>(R.id.sopView)
            sopView.flowData = flowData
            sopView.setData(bean.operationInfo, holder.bindingAdapterPosition, jgTrackBean?.apply {
                rank = t?.outPosition?.let { it+1 }?:1
            },jGPageListCommonBean)
            sopView.mTrackViewListener = { rowsBean, productPosition->
                this.mTrackViewListener?.invoke(SearchRowsBean(2,rowsBean,t?.operationInfo,t?.keyword?:"", t?.outPosition?:0),productPosition)
            }

            sopView.mTrackClickListener = { rowsBean, productPosition,isBtnClick:Boolean,btnContent:String,number:Int?->
                this.mTrackClickListener?.invoke(SearchRowsBean(2,rowsBean,t?.operationInfo,t?.keyword?:"", t?.outPosition?:0),productPosition,isBtnClick, btnContent, number)
            }
        }
    }
}