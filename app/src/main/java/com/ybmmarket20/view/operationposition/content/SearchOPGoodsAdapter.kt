package com.ybmmarket20.view.operationposition.content

import android.content.Context
import android.util.SparseArray
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.JgOperationPositionInfo
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.utils.analysis.flowDataPageOPListExposureWithCode
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 运营位商品适配器
 */
class SearchOPGoodsAdapter(data: MutableList<RowsBean>, private val isShowShop: Boolean, private val parentPosition: Int) :
    YBMBaseListAdapter<RowsBean>(R.layout.item_search_operation_position_goods_wrapper ,data) {

    val traceProductData = SparseArray<String>()
    //商品曝光事件的回调
    var trackViewListener:( (rowsBean:RowsBean,productPosition:Int)->Unit)? = null
    //商品点击事件的回调
    var trackClickListener:( (rowsBean:RowsBean,productPosition:Int,isBtnClick:Boolean,btnContent:String,number:Int?)->Unit)? = null

    var jGPageListCommonBean: JGPageListCommonBean? = null
    var mJgTrackBean: JgTrackBean? = null
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val goodsView = holder.getView<OPGoodsView>(R.id.goodsView)
            when (itemCount) {
                3 -> ItemSize.FixedItemCountSize(
                        mContext,
                        holder.itemView)

                else -> ItemSize.DynamicItemCountSize(
                        mContext,
                        holder.itemView)
            }.apply {
                setItemWidth()
                setIconHeight()
            }
            goodsView.setData(
                    bean.apply {
//                        isOPSingleGoods = true
                    },
                    holder.bindingAdapterPosition,
                    parentPosition,
                    isShowShop,
                    flowData,
                    mJgTrackBean?.copy()?.apply {
                        mJgOperationPositionInfo = JgOperationPositionInfo(
                                t?.productId,
                                t?.operationId,
                                rank,
                                holder.bindingAdapterPosition+1)
                        productId= t?.productId?:""
                        productType= t?.jgProductType?:""
                    },jGPageListCommonBean)
            traceGoodsExposure(
                    bean,
                    holder)
            goodsView.productClickListener = { rowsBean, productPosition,isBtnClick:Boolean,btnContent:String,number:Int? ->
                trackClickListener?.invoke(
                        rowsBean,
                        baseViewHolder?.bindingAdapterPosition ?: 0,isBtnClick, btnContent, number)
            }
            t?.let {
                trackViewListener?.invoke(
                        it,
                        baseViewHolder?.bindingAdapterPosition ?: 0)
            }
        }
    }

    /**
     * 商品曝光埋点
     */
    private fun traceGoodsExposure(rowsBean: RowsBean, holder: YBMBaseHolder) {
        if (flowData != null && traceProductData[holder.bindingAdapterPosition] == null) {
            flowDataPageOPListExposureWithCode(
                flowData,
                rowsBean.productId,
                rowsBean.showName,
                "${holder.bindingAdapterPosition}",
                rowsBean.searchSortStrategyCode,
                "0",
                rowsBean.operationExhibitionId,
                rowsBean.operationId,
                "$parentPosition"
            )
            traceProductData.put(holder.bindingAdapterPosition, rowsBean.productId)
        }
    }

    /**
     * itemView宽度
     */
    sealed class ItemSize(val context: Context, val itemView: View) {

        open fun setItemWidth() {

        }

        open fun setIconHeight(){

        }

        /**
         * 固定数量Item， itemCount=3
         */
        class FixedItemCountSize(context: Context, itemView: View) : ItemSize(context, itemView) {
            override fun setItemWidth() {
                super.setItemWidth()
                val lp = itemView.layoutParams as RecyclerView.LayoutParams
                lp.width = RecyclerView.LayoutParams.MATCH_PARENT
//                lp.height = ScreenUtils.getScreenWidth(context) - 4 * ScreenUtils.dip2px(context, 10F)
                itemView.layoutParams = lp
            }

            override fun setIconHeight() {
                super.setIconHeight()
                val clTop = itemView.findViewById<ConstraintLayout>(R.id.clTop)
                val lp = clTop.layoutParams as ConstraintLayout.LayoutParams
                lp.height = (ScreenUtils.getScreenWidth(context) - 4 * ScreenUtils.dip2px(context, 10F)) / 3
                clTop.layoutParams = lp
            }
        }

        /**
         * 动态数量Item
         */
        class DynamicItemCountSize(context: Context, itemView: View) : ItemSize(context, itemView) {
            override fun setItemWidth() {
                super.setItemWidth()
                val itemWidth = ScreenUtils.getScreenWidth(context) / 4
                val lp = itemView.layoutParams as RecyclerView.LayoutParams
                lp.width = itemWidth
                itemView.layoutParams = lp
            }

            override fun setIconHeight() {
                super.setIconHeight()
                val clTop = itemView.findViewById<ConstraintLayout>(R.id.clTop)
                val itemWidth = ScreenUtils.getScreenWidth(context) / 4
                val lp = clTop.layoutParams as ConstraintLayout.LayoutParams
                lp.height = itemWidth
                clTop.layoutParams = lp
            }
        }
    }
}