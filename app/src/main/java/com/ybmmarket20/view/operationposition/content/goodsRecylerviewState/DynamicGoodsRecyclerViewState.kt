package com.ybmmarket20.view.operationposition.content.goodsRecylerviewState

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.common.util.ConvertUtils

/**
 * 动态数量item
 */
class DynamicGoodsRecyclerViewState(rv: RecyclerView, info: OperationPositionInfo) :
    GoodsRecyclerViewState(rv, info) {

    override fun initRecyclerView() {
        super.initRecyclerView()
        rv.layoutManager = LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false)
    }

    override fun getNestedScrollingEnabled(): Boolean = true

    override fun addItemDivider() {
        super.addItemDivider()
        if (rv.itemDecorationCount > 0) {
            rv.removeItemDecorationAt(0)
        }
        rv.addItemDecoration(DynamicCountDecoration())
    }

    inner class DynamicCountDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val edgeDividerWidth = ConvertUtils.dp2px(13F)
            val dividerWidth = ConvertUtils.dp2px(5F)
            val position = parent.getChildLayoutPosition(view)
            if (position == 0) {
                edgeDividerWidth to dividerWidth
            } else {
                dividerWidth to dividerWidth
            }.apply {
                outRect.left = first
                outRect.right = second
            }
        }
    }
}