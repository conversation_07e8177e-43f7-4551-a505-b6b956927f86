package com.ybmmarket20.view.operationposition.content.goodsState

import android.view.View
import android.widget.ImageView
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.bean.JgRequestParams
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.reportAddToCart
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.reportBean.AddToCart
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.flowDataPageCommodityDetails
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ydmmarket.report.manager.TrackManager

/**
 * 秒杀
 */
class SecKillGoodsViewState(itemView: View) : GoodsViewTimeState(itemView) {

    override fun handleData(
            rowsBean: RowsBean,
            parentPosition: Int,
            isShowShop: Boolean,
            flowData: BaseFlowData?,
            position: Int
    ) {
        super.handleData(rowsBean, parentPosition, isShowShop, flowData,position)
        if (rowsBean.actSk.status == 1) {
            //进行中
            getOPGoodsTime().visibility = View.VISIBLE
            getPigouBtn().visibility = View.VISIBLE
            setTitleText("距结束")
            setTitleBg(R.drawable.shape_op_time_title_seckill)
            setClTimeBg(R.drawable.shape_op_time_bg_seckill)
            setTimeSolidAll(R.drawable.shape_op_time_item_seckill)
            startCountDown(rowsBean)
            getPigouBtn().setOnClickListener {
                productClickListener?.invoke(true,"抢购",null)
                getPigouBtn().visibility = View.GONE
                getGoodsPel().visibility = View.VISIBLE
                getGoodsPel().findViewById<ImageView>(R.id.iv_numAdd).callOnClick()
            }
            val number = HandlerGoodsDao.getInstance().getNumber(rowsBean.id, false)
            if (number > 0) {
                getPigouBtn().visibility = View.GONE
                getGoodsPel().visibility = View.VISIBLE
            }
            getGoodsPel().bindData(
                rowsBean.id,
                rowsBean.status,
                true,
                0,
                null,
                true,
                rowsBean.stepNum,
                rowsBean.isSplit == 1
            )
            rowsBean.jgTrackBean = jgTrackBean
            getGoodsPel().rowsBean = rowsBean
            getGoodsPel().jgTrackBean = jgTrackBean
            getGoodsPel().jgPageListCommonBean = jgPageListCommonBean
            getGoodsPel().setOnAddCartListener(object : ProductEditLayoutNew.AddCartListener {
                override fun onPreAddCart(params: RequestParams?): RequestParams {
                    val number:Int? = params?.paramsMap?.get("amount")?.toIntOrNull()
                    productClickListener?.invoke(true,"加购",number)
                    trackClick()
                    addAnalysisRequestParams(
                        params,
                        flowData,
                        AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_LIST
                    )
                    val jgRequestParams = JgRequestParams()
                    jgRequestParams.entrance = jgTrackBean?.entrance ?: ""
                    jgRequestParams.activity_entrance = jgTrackBean?.activityEntrance ?: ""
                    jgRequestParams.search_sort_strategy_id = JGTrackManager.getSuperProperty(
                            itemView.context,
                            JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) as String?

                    JGTrackManager.GlobalVariable.mJgOperationInfo?.let {
                        if ((it.productId ?: "") == rowsBean.id.toString()){
                            jgRequestParams.operation_id = jgTrackBean?.mJgOperationPositionInfo?.operationId ?: ""
                            jgRequestParams.operation_rank = jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1
                            jgRequestParams.rank = jgTrackBean?.mJgOperationPositionInfo?.rank?:1
                        }
                    }

                    JGTrackManager.GlobalVariable.mJgSearchRowsBean?.let {
                        if ((it.productId ?: "") == rowsBean.id.toString()) {
                            jgRequestParams.key_word = it.searchKeyword
                            jgRequestParams.list_position_type = it.positionType.toString()
                            jgRequestParams.list_position_typename = it.positionTypeName
                            jgRequestParams.product_id = it.productId
                            jgRequestParams.product_name = it.productName
                            jgRequestParams.product_first = it.categoryFirstId
                            jgRequestParams.product_number = it.productNumber
                            jgRequestParams.product_price = it.jgProductPrice
                            jgRequestParams.product_type = it.productType.toString()
                            jgRequestParams.product_activity_type = it.productActivityType
                            jgRequestParams.product_shop_code = it.shopCode
                            jgRequestParams.product_shop_name = it.shopName
                            JGTrackManager.GlobalVariable.mJgSearchSomeField?.let {field->
                                field.mJgPageListCommonBean?.let {
                                    jgRequestParams.sptype = it.sptype?:""
                                    jgRequestParams.jgspid = it.jgspid?:""
                                    jgRequestParams.sid = it.sid?:""
                                    jgRequestParams.total_page = it.total_page
                                    jgRequestParams.direct = "1"
                                    jgRequestParams.page_no = it.page_no?:1
                                    jgRequestParams.result_cnt = it.result_cnt?:0
                                    jgRequestParams.page_size = it.page_size?:1
                                }
                                jgRequestParams.rank = field.rank?:1
                            }
                        }
                    }
                    jgRequestParams.session_id = TrackManager.getSessionId(YBMAppLike.getAppContext())
                    params?.put("mddata",
                                Gson().toJson(jgRequestParams))

                    if (jgTrackBean?.entrance?.contains(JGTrackManager.TrackShoppingCart.TITLE) == true){ //购物车只传个direct = "3"
                        params?.paramsMap?.remove("mddata")
                    }

                    return params?: RequestParams()
                }

                override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams?) {
                    reportAddToCart(
                            AddToCart(
                                    url = jgTrackBean?.url?:"",
                                    title = jgTrackBean?.title?:"",
                                    referrer = jgTrackBean?.jgReferrer?:"",
                                    jGPageListCommonBean = jgPageListCommonBean,
                                    search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                                    rank = jgTrackBean?.mJgOperationPositionInfo?.rank,
                                    operation_id = jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                                    operation_rank = jgTrackBean?.mJgOperationPositionInfo?.operationRank,
                                    list_position_type = rowsBean.positionType.toString(),
                                    list_position_typename = rowsBean.positionTypeName?:"",
                                    product_id = rowsBean.id,
                                    product_name = rowsBean.productName?:"",
                                    product_first = rowsBean.categoryFirstId,
                                    product_price = rowsBean.jgProductPrice,
                                    product_type = rowsBean.productType.toString(),
                                    direct = "1",
                                    product_number = params?.amount?.toIntOrNull(),
                                    product_activity_type = rowsBean.productActivityType,
                                    product_shop_code = rowsBean.shopCode,
                                    product_shop_name = rowsBean.shopName,
                            )
                    )
                    flowDataPageCommodityDetails(
                        flowData,
                        rowsBean.id.toString() + "",
                        AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_NOREAL,
                        rowsBean.sourceType,
                        "$parentPosition"
                    )
                }
            })
            getGoodsPel().setOnDelProductListener {
                getPigouBtn().visibility = View.VISIBLE
                getGoodsPel().visibility = View.GONE
            }
        }
        getPriceView().visibility = View.VISIBLE
        getPriceView().text = getNormalPrice("${rowsBean.actSk?.skPrice?: 0}")
    }

    override fun getClickItemType(): String = OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_SECKILL

    override fun getLeftTime(rowsBean: RowsBean): Long {
        val localDiff: Long = System.currentTimeMillis() - (rowsBean.actSk?.responseLocalTime ?: 0L)
        return (rowsBean.actSk?.surplusTime ?: 0) - localDiff
    }
}