package com.ybmmarket20.view.operationposition.header

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.operationposition.track.OPTrackManager
import com.ybmmarket20.view.operationposition.OPCardViewManager

/**
 * 多店铺头部
 */
class MultiShopHeader(info: OperationPositionInfo, opManager: OPCardViewManager) :
    ShopHeader(info, opManager) {
    override fun handleVisibility() {
        opManager.showMultiShopHeader()
    }

    override fun handleData() {
        val title = opManager.getView<TextView>(R.id.tvOPMultiShopTitle)
        val subTitle = opManager.getView<TextView>(R.id.tvOPMultiShopSubTitle)
        val tvOPMoreEntry = opManager.getView<TextView>(R.id.tvOPMoreEntry)
        title.text = info.title
        subTitle.text = info.subTile
        tvOPMoreEntry.visibility = if (TextUtils.isEmpty(info.jumpUrl)) View.GONE else View.VISIBLE
        tvOPMoreEntry.setOnClickListener {
            OPTrackManager.opActivityEntryClickTrack(info)
            val mUrl = splicingUrlWithParams(info.jumpUrl, hashMapOf(Pair("entrance","搜索-运营位入口")))
            RoutersUtils.open(mUrl)
        }

    }
}