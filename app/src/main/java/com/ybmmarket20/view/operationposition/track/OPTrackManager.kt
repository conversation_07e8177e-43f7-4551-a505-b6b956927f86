package com.ybmmarket20.view.operationposition.track

import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.XyyIoUtil

/**
 * 运营位埋点
 */
object OPTrackManager {

    /**
     * 运营位-活动入口点击
     */
    fun opActivityEntryClickTrack(info: OperationPositionInfo) {
        XyyIoUtil.track("search_Active_Click", hashMapOf(
            "active_url" to info.jumpUrl,
            "active_title" to info.title
        ))
    }

    /**
     * 运营位-进店点击
     */
    fun opShopClickTrack(info: OperationPositionInfo) {
        XyyIoUtil.track("search_Store_Click", hashMapOf(
            "shop_name" to info.shopName,
            "shop_code" to info.shopCode
        ))
    }

    /**
     * 商品按钮点击
     */
    fun opGoodsBtnClickTrack(opTrackGoodsItem: OPTrackGoodsItem) {
        opTrackGoodsItem.apply {
            val params = hashMapOf(
                "commodityId" to "${rowsBean.id}",
                "commodityName" to rowsBean.showName,
                "sptype" to (flowData?.spType?: ""),
                "spid" to (flowData?.spId?: ""),
                "sid" to (flowData?.sId?: ""),
                "direct" to AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_LIST,
                "index" to "$position",
                "search_sort_strategy_id" to rowsBean.searchSortStrategyCode,
                "active_type" to "0",
                "goods_groupid" to rowsBean.operationExhibitionId,
                "active_id" to rowsBean.operationId,
                "active_index" to parentPosition,
                "click_item" to clickItemType,
            )
            XyyIoUtil.track("action_Product_Click", params)
        }
    }

}