package com.ybmmarket20.view.searchFilter.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean

/**
 * 搜索过滤条件，服务adapter
 */
class SearchFilterServiceAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_service) {
    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_SERVICE

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_SERVICE

    override fun bindItemViewData(holder: <PERSON><PERSON>BaseHolder, bean: FullSearchFilterBean) {

    }

}