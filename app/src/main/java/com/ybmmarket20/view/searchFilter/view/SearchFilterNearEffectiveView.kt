package com.ybmmarket20.view.searchFilter.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 搜索筛选框有效期
 */

//6月个以下
const val NEAR_EFFECTIVE_LEVE_1 = "1"
//6-12个月
const val NEAR_EFFECTIVE_LEVE_2 = "2"
//12个月以上
const val NEAR_EFFECTIVE_LEVE_3 = "3"
//15个月以上
const val NEAR_EFFECTIVE_LEVE_4 = "4"

class SearchFilterNearEffectiveView(context: Context, attrs: AttributeSet) :
    LinearLayout(context, attrs) {

    var mAdapter: SearchFilterNearEffectiveAdapter? = null
    var mSelectedItemCallback: ((nearEffectiveItem: NearEffectiveItem) ->Unit)? = null

    init {
        View.inflate(context, R.layout.view_search_filter_near_effective, this)
        initData()
    }

    /**
     * 初始化数据
     */
    fun initData() {
        val rv = findViewById<RecyclerView>(R.id.rvNearEffective)
        rv.layoutManager = GridLayoutManager(context, 4)
        mAdapter = SearchFilterNearEffectiveAdapter(getData())
        rv.adapter = mAdapter
    }

    /**
     * 设置数据
     */
    fun getData(): MutableList<NearEffectiveItem> {
        return mutableListOf(
            NearEffectiveItem("6月个以下", NEAR_EFFECTIVE_LEVE_1),
            NearEffectiveItem("6-12个月", NEAR_EFFECTIVE_LEVE_2),
            NearEffectiveItem("12个月以上", NEAR_EFFECTIVE_LEVE_3),
            NearEffectiveItem("15个月以上", NEAR_EFFECTIVE_LEVE_4)
        )
    }

    /**
     * 更新选中项
     */
    @SuppressLint("NotifyDataSetChanged")
    fun updateItemSelected(selectedId: String?) {
        mAdapter?.data?.forEach {
            val item = it as NearEffectiveItem
            it.isSelected = false
            if (item.nearEffectiveId == selectedId) {
                it.isSelected = true
            }
        }
        mAdapter?.notifyDataSetChanged()
    }

    fun setOnSelectedItemCallback(callback: ((nearEffectiveItem: NearEffectiveItem) ->Unit)?) {
        mSelectedItemCallback = callback
    }

    @SuppressLint("NotifyDataSetChanged")
    fun reset() {
        mAdapter?.let {
            val data = it.data
            data.forEach { item ->
                (item as NearEffectiveItem).isSelected = false
            }
            it.notifyDataSetChanged()
        }
    }

    data class NearEffectiveItem(
        var text: String,
        var nearEffectiveId: String,
        var isSelected: Boolean = false
    )


    inner class SearchFilterNearEffectiveAdapter(
        data: MutableList<NearEffectiveItem>
    ) : YBMBaseListAdapter<NearEffectiveItem>(R.layout.item_search_filter_near_effective, data) {

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: NearEffectiveItem?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->
                val cb = holder.getView<CheckBox>(R.id.cb_item)
                cb.isChecked = bean.isSelected
                cb.text = bean.text
                holder.itemView.setOnClickListener {
                    data.forEach {
                        val nei = it as NearEffectiveItem
                        if (nei.nearEffectiveId == bean.nearEffectiveId) {
                            nei.isSelected = !nei.isSelected
                        } else nei.isSelected = false
                    }
                    mSelectedItemCallback?.invoke(bean)
                    notifyDataSetChanged()
                }
            }
        }

    }
}