package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.AssociateShopRequest
import com.ybmmarket20.network.request.AssociatedShopsRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 查询已关联的店铺
 */
class AssociateShopsViewModel : ViewModel() {
    //查询已关联的店铺
    private val _queryAssociateShopsLiveData = MutableLiveData<BaseBean<SelectLoginShopInfoWithPage>>()
    val queryAssociateShopsLiveData: LiveData<BaseBean<SelectLoginShopInfoWithPage>> = _queryAssociateShopsLiveData
    //状态切换
    private val _switchStatusLiveData = MutableLiveData<BaseBean<SelectLoginShopInfoWithPage>>()
    val switchStatusLiveData: LiveData<BaseBean<SelectLoginShopInfoWithPage>> = _switchStatusLiveData
    //取消关联店铺
    private val _cancelAssociatedShopLiveData = MutableLiveData<BaseBean<CancelAssociatedPositionBean>>()
    val cancelAssociatedShopLiveData: LiveData<BaseBean<CancelAssociatedPositionBean>> = _cancelAssociatedShopLiveData
    var pageNo: Int = 1
    var list: MutableList<SelectLoginShopInfo> = mutableListOf()

    /**
     * 查询已关联的店铺
     */
    fun queryAssociatedShops() {
        viewModelScope.launch {
            val queryAssociatedShops = AssociatedShopsRequest().queryAssociatedShops("$pageNo", "20")
            if (queryAssociatedShops.isSuccess) {
                queryAssociatedShops.data.pageNo = pageNo
                if (!queryAssociatedShops.data.isEnd()) pageNo++
                val shopList = queryAssociatedShops.data.list
                shopList?.forEach {
                    val shopState = SelectLoginShopState.getLoginShopState(
                        it.merchantStatus,
                        it.associateStatus,
                        it.merchantId?: "",
                        it.merchantAvailableStatus
                    )
                    it.tagBean = shopState?.getTagBean()
                    it.routerUrl = shopState?.getRouterUrl()
                    if (it.merchantId == SpUtil.getMerchantid()) it.editStatus = 1
                }
                shopList?.let { list.addAll(it) }
            }
            _queryAssociateShopsLiveData.postValue(queryAssociatedShops)
        }
    }

    /**
     * 切换到编辑状态
     */
    fun switchStatusToEdit() {
        switchStatus(2)
    }

    /**
     * 切换到普通状态
     */
    fun switchStatusToNormal() {
        switchStatus(0)
    }

    /**
     * 切换到选中状态
     */
    fun switchStatusToSelected(position: Int) {
        switchStatus(1, position)
    }

    /**
     * 切换状态
     * @param status 0:普通状态 1：选中状态 2：编辑状态
     * @param position 更新位置 -1：全部
     */
    private fun switchStatus(status: Int, position: Int = -1) {
        val shopInfo = _queryAssociateShopsLiveData.value
        list.forEachIndexed {index, item ->
            if (status == 0) {
                //普通状态
                if (item.merchantId == SpUtil.getMerchantid()) {
                    item.editStatus = 1
                } else item.editStatus = 0
            } else if (status == 1) {
                //选中状态
                if (index == position){
                    item.editStatus = 1
                } else item.editStatus = 0
            } else if (status == 2) {
                //编辑状态
                item.editStatus = 2
            }
        }
        shopInfo?.let(_switchStatusLiveData::postValue)
    }

    /**
     * 选择登录店铺状态
     */
    sealed class SelectLoginShopState {

        /**
         * 获取标签样式
         */
        open fun getTagBean(): TagBean? = null

        /**
         * 获取路由
         */
        open fun getRouterUrl(): String? = null

        companion object {

            /**
             * 获取当前状态
             */
            fun getLoginShopState(merchantStatus: Int, associateStatus: Int, merchantId: String, merchantAvailableStatus: Int = 0): SelectLoginShopState? =
                if (merchantAvailableStatus == 3) {
                    //冻结状态
                    FreezeState
                } else if (associateStatus == 1) {
                    //待提审
                    PendingProcess(merchantId)
                } else if (associateStatus == 4 || associateStatus == 5 || merchantStatus == 1) {
                    //审核中
                    ProcessingState(merchantStatus, associateStatus, merchantId)
                } else if (associateStatus == 3 || merchantStatus == 3) {
                    //审核未通过
                    UnPassedState(merchantStatus, associateStatus, merchantId)
                } else if (associateStatus == 2 && merchantStatus == 2) {
                    //审核通过
                    PassedState
                } else null
        }

        object FreezeState : SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "已冻结"
                bgColor = "#0DFE2021"
                textColor = "#FFFE2021"
                borderColor = "#73FE2021"
            }
        }

        //审核通过
        object PassedState: SelectLoginShopState() {
            override fun getRouterUrl(): String {
                return "ybmpage://main"
            }
        }

        //审核未通过
        class UnPassedState(private val merchantStatus: Int, private val associateStatus: Int, val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "审核未通过"
                bgColor = "#0DFE2021"
                textColor = "#FFFE2021"
                borderColor = "#73FE2021"
            }

            override fun getRouterUrl(): String? = if (merchantStatus == 3) {
                //店铺审核不通过
                "ybmpage://linkshop"
            } else if (associateStatus == 3) {
                //凭据审核不通过
                "ybmpage://clerkaptitudeauthenticationactivity?merchant_id=$merchantId&status=1"
            } else null
        }

        //审核中
        class ProcessingState(private val merchantStatus: Int, private val associateStatus: Int, val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = if (merchantStatus == 1) "店铺信息审核中" else "资质审核中"
                bgColor = "#0D00B377"
                textColor = "#FF00B377"
                borderColor = "#8000B377"
            }

            override fun getRouterUrl(): String? = if (merchantStatus == 1) {
                //店铺审核中
                "ybmpage://shopinfoauthenticationprocessing?merchantId=$merchantId&status=2"
            } else if (associateStatus == 4 || associateStatus == 5) {
                "ybmpage://associatedshopauthenticationprocessing?merchantId=$merchantId&status=2"
            } else null
        }

        //待提审
        class PendingProcess(val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "待提审"
                bgColor = "#0DFF8C1A"
                textColor = "#FFFF8C19"
                borderColor = "#80FF8C1A"
            }

            override fun getRouterUrl(): String = "ybmpage://clerkaptitudeauthenticationactivity?merchant_id=$merchantId&status=0"
        }
    }

    /**
     * 取消关联店铺
     */
    fun cancelAssociateMerchant(merchantId: String, position: Int) {
        viewModelScope.launch {
            val cancelAssociateMerchant = AssociatedShopsRequest().cancelAssociateMerchant(merchantId)
            cancelAssociateMerchant.data = CancelAssociatedPositionBean(position)
            _cancelAssociatedShopLiveData.postValue(cancelAssociateMerchant)
        }
    }
}