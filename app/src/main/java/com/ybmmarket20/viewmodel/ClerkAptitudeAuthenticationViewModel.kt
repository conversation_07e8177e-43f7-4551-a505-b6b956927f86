package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ybmmarket20.adapter.LicensePicListAdapter
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.ClerkAptitudeAuthenticationRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 店员资质认证
 */
class ClerkAptitudeAuthenticationViewModel: ViewModel() {

    //提交凭据
    private val _confirmLiveData = MutableLiveData<BaseBean<EmptyBean>>()
    val confirmLiveData: LiveData<BaseBean<EmptyBean>> = _confirmLiveData
    //布局
    private val _layoutLiveData = MutableLiveData<MutableList<LicenceBean>>()
    val layoutLiveData: LiveData<MutableList<LicenceBean>> = _layoutLiveData

    /**
     * 提交凭据
     */
    fun confirmClerkAptitudeAuthentication(mNecessaryList: MutableList<LicenceBean>, merchantId: String) {
        viewModelScope.launch {
            val gson = Gson()
            val licenseAuditImgListStr = mNecessaryList.map {
                val imageUrlList: List<LicensePicListAdapter.ImageInfo> = it.adapter.data as List<LicensePicListAdapter.ImageInfo>
                it.listImgUrls = imageUrlList.map { imageInfo ->
                    imageInfo.newPath
                }
                LicenseUpload(it)
            }.let(gson::toJson)

            val confirmClerkAptitudeAuthentication = ClerkAptitudeAuthenticationRequest().confirmClerkAptitudeAuthentication(licenseAuditImgListStr, merchantId)
            _confirmLiveData.postValue(confirmClerkAptitudeAuthentication)
        }
    }

    /**
     * 获取布局数据
     */
//    fun getLayoutData1() {
//        mutableListOf(
//            ClerkAptitudeAuthenticationLayoutData("授权委托书", "查看示例图片", "", mutableListOf()),
//            ClerkAptitudeAuthenticationLayoutData("被委托人身份证复印件(正反两面)", null, null, mutableListOf())
//        ).let(_layoutLiveData::postValue)
//    }

    /**
     * 获取布局数据
     */
    fun getLayoutData(isFetch: Boolean, merchantId: String) {
        viewModelScope.launch {
            val list = mutableListOf<LicenceBean>()
            val aptitudeAuthenticationLayoutData =
                ClerkAptitudeAuthenticationRequest().getAptitudeAuthenticationLayoutData(merchantId)
            if (isFetch) {
                if (aptitudeAuthenticationLayoutData.isSuccess) {
                    val itemList = aptitudeAuthenticationLayoutData.data.list
                    list.apply {
                        add(getLicenceBean("FRSQ", itemList, aptitudeAuthenticationLayoutData.data.sqwtsSampleImageUrl?: ""))
                        add(getLicenceBean("WTRZ", itemList))
                    }
                }
            } else {
                val sampleImageUrl = aptitudeAuthenticationLayoutData.data?.sqwtsSampleImageUrl ?: ""
                list.apply {
                    add(LicenceBean("FRSQ", "授权委托书", 1, sampleImageUrl, 0, mutableListOf()))
                    add(LicenceBean("WTRZ", "被委托人身份证复印件(正反两面)", 1, "", 0, mutableListOf()))
                }
            }
            _layoutLiveData.postValue(list)
        }
    }


    private fun getLicenceBean(licenseCode: String, list: List<AptitudeAuthenticationLayoutDataItem>?, tempUrl: String = ""): LicenceBean {
        if (list?.isNotEmpty() == true) {
            list.forEach {
                if (it.licenseCode == licenseCode) {
                    return LicenceBean(licenseCode, "${it.name} ", 1, tempUrl, 0, it.listImgUrls)
                }
            }
        }
        return if (licenseCode == "FRSQ") {
            LicenceBean("FRSQ", "授权委托书 ", 1, "", 0, mutableListOf())
        } else {
            LicenceBean("WTRZ", "被委托人身份证复印件(正反两面)", 1, "", 0, mutableListOf())
        }
    }
}