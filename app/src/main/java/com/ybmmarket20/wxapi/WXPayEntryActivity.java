package com.ybmmarket20.wxapi;

import android.content.Intent;
import android.os.Bundle;

import com.apkfuns.logutils.LogUtils;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.PaywayActivity;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.utils.YBMPayUtil;


public class WXPayEntryActivity extends BaseActivity implements IWXAPIEventHandler {

    private IWXAPI api;

    public static String APP_ID = "";

    // 支付回调
    private static YBMPayUtil.PaySDKCallBack paySDKCallBack;
    private static int payChannel;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        api = WXAPIFactory.createWXAPI(this, APP_ID);

        if (api != null)
            try {
                api.handleIntent(getIntent(), this);
            } catch (Exception ignore) {

            }
    }

    @Override
    protected void initData() {

    }

    @Override
    public int getContentViewId() {
        return -1;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        try {
            api.handleIntent(intent, this);
        } catch (Exception ignore) {

        }
    }

    @Override
    public void onReq(BaseReq req) {
    }

    @Override
    public void onResp(BaseResp resp) {
        if (resp.getType() == ConstantsAPI.COMMAND_PAY_BY_WX) {
            LogUtils.e("wxpay errorCode = " + resp.errCode);
            if (resp.errCode == BaseResp.ErrCode.ERR_OK) {//成功
                if (paySDKCallBack != null) {
                    paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), "");
                }
            } else if (resp.errCode == BaseResp.ErrCode.ERR_USER_CANCEL) {//取消
                if (paySDKCallBack != null) {
                    paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_USER_CANCEL, getStr(R.string.payway_result_cancel), "");
                }
            } else {//失败
                if (paySDKCallBack != null) {
                    paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_sdk), "");
                }
            }
        }
        finish();
    }


    public static void setPaySDKCallBack(YBMPayUtil.PaySDKCallBack callBack, String app_id) {
        paySDKCallBack = callBack;
        APP_ID = app_id;
    }


    private String getStr(int id) {
        return YBMAppLike.getAppContext().getString(id);
    }

    @Override
    protected void onStop() {
        super.onStop();
        paySDKCallBack = null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        paySDKCallBack = null;
    }
}