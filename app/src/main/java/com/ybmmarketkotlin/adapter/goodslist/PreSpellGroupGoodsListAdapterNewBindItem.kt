package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.openUrl
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import java.text.SimpleDateFormat

/**
 * 预热
 */
open class PreSpellGroupGoodsListAdapterNewBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>
) : AbstractGoodsListAdapterNewBindItem(mContext, baseViewHolder, rowsBean, countDownTimerMap) {

    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean) {
        super.onGoodsPrice(showUnderlinePrice, showPgbyUnderLineProce)
        rowsBean.actPt?.let {
            val price = baseViewHolder.getView<TextView>(R.id.shop_price_spell_group)
            price.text = rowsBean.getShowPriceStrNew(showUnderlinePrice)
        }
    }

    override fun onGoodsSpec() {
        super.onGoodsSpec()
        baseViewHolder.getView<TextView?>(R.id.tv_goods_spec)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_divider_of_spec_name)?.visibility = View.GONE
    }

    override fun onManufacturerTag() {
        super.onManufacturerTag()
        baseViewHolder.getView<TextView?>(R.id.tv_chang_name)?.visibility = View.GONE
    }

    override fun onDataTags() {
        super.onDataTags()
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
    }

    override fun onMarketingTags() {
        super.onMarketingTags()
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
        val tvGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking)
        tvGroupbooking.visibility = View.GONE

        // 注意：单价显示已在 onFinal() 中统一处理，这里不再重复调用
        // handleUnitPrice()

//        tvGroupbooking?.text =
//            "已拼${rowsBean.actPt?.orderNum}${rowsBean.productUnit}/${rowsBean.actPt?.skuStartNum}${rowsBean.productUnit}起拼"
//        tvGroupbooking?.setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))
//        val marketTag: ShopNameWithTagView = baseViewHolder.getView(R.id.snwtg_spell_group_market_tag)
//        val tags = getAllTags()
//        marketTag.visibility = if (tags.isNotEmpty()) View.VISIBLE else View.GONE
//        marketTag.bindData(tags, maxTagCount = 100)
    }

    override fun onEffectTags() {
        super.onEffectTags()
        baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.GONE
    }

    override fun onGoodsDiscountPrice() {
        super.onGoodsDiscountPrice()
        baseViewHolder.getView<TextView?>(R.id.tv_original_price)?.visibility = View.GONE
        baseViewHolder.getView<RelativeLayout>(R.id.ll_product_price)?.visibility = View.GONE
        // 折后价
        val tvOriginalPrice = baseViewHolder.getView<TextView>(R.id.tv_original_price_spell_group)

        if ((!rowsBean.isControlTitle || rowsBean.controlType == 5) && rowsBean.availableQty > 0 && !rowsBean.showPriceAfterDiscount.isNullOrEmpty()) {
            tvOriginalPrice.visibility = View.VISIBLE
            tvOriginalPrice.text = rowsBean.showPriceAfterDiscount
        } else {
            tvOriginalPrice.visibility = View.GONE
        }
    }

    override fun onCoupon() {
        super.onCoupon()
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
    }

    override fun onMediumPackage() {
        super.onMediumPackage()
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
    }

    override fun onArrivalOfGoodsNotify(adapter: RecyclerView.Adapter<*>) {
        super.onArrivalOfGoodsNotify(adapter)
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE
    }

    override fun onSpellGroupPreHot() {
        super.onSpellGroupPreHot()
        val clSpellGroup = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
        clSpellGroup?.visibility = View.VISIBLE
        //加购按钮
        baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.visibility = View.GONE
        handlePreSpellGroup()
    }

    fun setFlowData(flowData: BaseFlowData?) {
        baseViewHolder.setOnClickListener(R.id.tv_join_groupbooking) {
            SearchProductReport.trackSearchGoodsClick(mContext, baseViewHolder.bindingAdapterPosition, rowsBean)
            openUrl(
                "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}",
                flowData
            )
            //点击拼团也认为是点击商品的埋点
            productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,true,"立即参团",null)
        }
    }

    override fun onLoadGoodsIcon() {
        super.onLoadGoodsIcon()
    }


    open fun handlePreSpellGroup() {
        //按钮
        val btn = baseViewHolder.getView<RoundTextView>(R.id.tv_join_groupbooking)
        btn.setBackgroundColor(ContextCompat.getColor(mContext, R.color.color_00b955))
        btn.text = "即将开团"
    }


}