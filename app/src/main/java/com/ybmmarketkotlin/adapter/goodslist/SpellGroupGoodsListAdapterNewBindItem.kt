package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.graphics.Color
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.common.widget.RoundConstraintLayout
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.common.widget.RoundedImageView
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.ControlGoodsDialogUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.SpellGroupPopWindow

/**
 * 拼团
 */
open class SpellGroupGoodsListAdapterNewBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    private val countDownTimerMap: SparseArray<CountDownTimer>,
    val adapter: RecyclerView.Adapter<*>
): AbstractGoodsListAdapterNewBindItem(mContext, baseViewHolder, rowsBean, countDownTimerMap) {


    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSpellGroupOrSeckill() {
        super.onSpellGroupOrSeckill()
        processCountDown(baseViewHolder, rowsBean)
    }
    /**
     * 营销标签
     */
    override fun onMarketingTags() {
        super.onMarketingTags()

    }

    /**
     * @param baseViewHolder
     * @param rowsBean
     *
     *   目前拼团没倒计时了
     *
     *  1. 拼团倒计时
     *  2. 隐藏规格、厂家
     *  3. 替换拼团价、无折后价，增加划线价
     *  4. 增加拼购比例文字显示、进度显示、参团按钮
     *  5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装
     *  处理 拼团相关 的逻辑
     */
    private fun processCountDown(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {

        // 倒计时组件
//        val tvCountdown = baseViewHolder.getView<TextView?>(R.id.tv_countdown)
        val clGroupbooking = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
        val tvGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking)
        val tvJoinGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_join_groupbooking)
        val time = baseViewHolder.getView<RoundTextView>(R.id.tv_spell_time)
        val conSpell = baseViewHolder.getView<RoundConstraintLayout>(R.id.con_spell)


        val localDiff: Long = System.currentTimeMillis() - (rowsBean.actPt?.responseLocalTime ?: 0L)
        val leftTime = (rowsBean.actPt?.surplusTime ?: 0L) * 1000 - localDiff
        val localDiff1: Long = System.currentTimeMillis() - (rowsBean.limitFullDiscountActInfo?.responseLocalTime ?: 0L)
        val leftTime1 = (rowsBean.limitFullDiscountActInfo?.endTime ?: 0) - localDiff1
        // 如果遇到刷新数据，要先取消原来的倒计时
        countDownTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            countDownTimerMap.remove(baseViewHolder.hashCode())
        }
        if (leftTime1 >= 0) {
            conSpell.visibility=View.VISIBLE
            time.visibility=View.VISIBLE
            // 补价倒计时
            val countDownTimer = object : CountDownTimer(leftTime1, 100L) {
                override fun onTick(millisUntilFinished: Long) {
                    // 计算剩余的小时数
                    val hours: Long = millisUntilFinished / (1000 * 60 * 60)
                    // 计算剩余的分钟数（去掉小时部分后的毫秒数）
                    val minutes: Long = millisUntilFinished % (1000 * 60 * 60) / (1000 * 60)
                    // 计算剩余的秒数（去掉分钟部分后的毫秒数）
                    val seconds: Long = millisUntilFinished % (1000 * 60) / 1000
                    // 计算剩余的百毫秒数
                    val hundredMilliseconds: Long = millisUntilFinished % 1000 / 100
                   val tvMsStr = hundredMilliseconds.toString()
                    val tvSecondStr = if (seconds < 10 ) "0$seconds" else seconds.toString()
                    val tvMinuteStr = if (minutes < 10 ) "0$minutes" else minutes.toString()
                    var hour = if (hours < 10 ) "0$hours" else hours.toString()
                    time.text = "$hour:$tvMinuteStr:$tvSecondStr.$tvMsStr"
                }

                override fun onFinish() {
                    time.text="0:00:00.00"
                }
            }
            countDownTimerMap.put(baseViewHolder.hashCode(), countDownTimer)
            countDownTimer.start()
        }else{
            conSpell.visibility = View.GONE
            time.visibility = View.GONE
        }

        // 处理单价显示（在拼团逻辑处理完成后）
        handleSpellGroupUnitPrice()

        if (leftTime > 0) {
            clGroupbooking?.visibility = View.VISIBLE
            tvGroupbooking?.text =
                "已拼${rowsBean.actPt?.orderNumStr ?:""}${rowsBean.productUnit}"
            if(time.visibility == View.VISIBLE) {
                tvGroupbooking.visibility = View.GONE
            } else {
                tvGroupbooking.visibility = View.VISIBLE
            }

            tvGroupbooking?.setTextColor(Color.parseColor("#676773"))
            tvJoinGroupbooking?.setOnClickListener {
                SearchProductReport.trackSearchItemBtnClickSpellGroup(mContext, baseViewHolder.bindingAdapterPosition, rowsBean)
                //点击参团也认为是点击商品的埋点
                productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,true,"立即参团",null)
                if (rowsBean.isControlGoods) {
                    //控销品
                    ControlGoodsDialogUtil.showControlGoodsDialog(mContext, rowsBean)
                    return@setOnClickListener
                }
                if (rowsBean.actPt != null && rowsBean.actPt.isApplyListShowType) {
                    rowsBean.jgTrackBean = jgTrackBean
                    val mPopWindowSpellGroup = SpellGroupPopWindow(
                        baseViewHolder.itemView.context,
                        rowsBean,
                        rowsBean.actPt,
                        false,
                        mIsList = true,
                        <EMAIL>,
                        jgTrackBean,
                            jgPageListCommonBean,
                            hideSpellGroupRightNow = <EMAIL>
                    )
                    mPopWindowSpellGroup.show(baseViewHolder.itemView)
                } else {
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                            Pair<String,Any>(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:""),
                            Pair<String,Any>(IntentCanst.JG_REFERRER,jgTrackBean?.jgReferrer?:""),
                            Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,jgTrackBean?.jgReferrerTitle?:""),
                            Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,jgTrackBean?.jgReferrerModule?:""),
                    ))
                    RoutersUtils.open(mUrl)
//                    openUrl(
//                        "ybmpage://productdetail/" + rowsBean.id, mFlowData
//                    )
                }
            }

            // 1. 拼团倒计时
            // 2. 隐藏规格、厂家
            baseViewHolder.getView<TextView?>(R.id.tv_goods_spec)?.visibility = View.GONE
            baseViewHolder.getView<ImageView?>(R.id.iv_divider_of_spec_name)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.tv_chang_name)?.visibility = View.GONE
            // 3. 替换拼团价、无折后价，增加划线价
            // 4. 增加拼购比例文字显示、进度显示、参团按钮
            // 5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装、有效期（后加）、售罄图标（如果在显示的话）
//            baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
//            baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
            baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
            baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
//            baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.GONE
            baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE

            handleSpellGroupStyle()
        }else {
            //拼团已结束
            clGroupbooking?.visibility = View.GONE
        }
    }

    override fun onFinal() {
        super.onFinal()
        setControl()
    }

    //设置控销
    fun setControl() {
        if (!TextUtils.isEmpty(rowsBean.controlTitle)) {
            //控销商品隐藏拼团加购按钮
            val spellGroupBtn = baseViewHolder.getView<ConstraintLayout>(R.id.cl_groupbooking)
            spellGroupBtn.visibility = View.GONE
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 控销显示商品价格样式
        if (rowsBean.controlType == 5) {
            val spellGroupBtn = baseViewHolder.getView<ConstraintLayout>(R.id.cl_groupbooking)
            spellGroupBtn.visibility = View.VISIBLE
        }
    }

    override fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean) {
        baseViewHolder.setText(R.id.shop_price_spell_group, rowsBean.getShowPriceStrNew(showUnderlinePrice))
        baseViewHolder.setVisible(R.id.ll_product_price, false)
    }

    override fun onGoodsDiscountPrice() {
//        super.onGoodsDiscountPrice()
        // 折后价
        val tvOriginalPrice = baseViewHolder.getView<TextView>(R.id.tv_original_price_spell_group)

        if ((!rowsBean.isControlTitle || rowsBean.controlType == 5) && rowsBean.availableQty > 0 && !rowsBean.showPriceAfterDiscount.isNullOrEmpty()) {
            tvOriginalPrice.visibility = View.VISIBLE
            tvOriginalPrice.text = rowsBean.showPriceAfterDiscount
        } else {
            tvOriginalPrice.visibility = View.GONE
        }
    }

    /**
     * 处理拼团样式
     */
    open fun handleSpellGroupStyle() {
        //按钮
        val btn = baseViewHolder.getView<RoundTextView>(R.id.tv_join_groupbooking)
        btn.setBackgroundColor(ContextCompat.getColor(mContext,R.color.color_FF6204))
        btn.text = "参团"
    }

    /**
     * 处理拼团商品的单价显示
     * 特殊逻辑：tv_spell_time → tv_unit_price → tv_groupbooking
     */
    private fun handleSpellGroupUnitPrice() {
        val tvUnitPrice = baseViewHolder.getView<TextView>(R.id.tv_unit_price)

        // 检查 unitPrice 字段是否有值
        if (!TextUtils.isEmpty(rowsBean.unitPrice)) {
            tvUnitPrice.visibility = View.VISIBLE
            tvUnitPrice.text = rowsBean.unitPrice
            // 设置背景色为透明
            tvUnitPrice.setBackgroundColor(mContext.resources.getColor(android.R.color.transparent))

            // 拼团商品的特殊位置调整逻辑
            adjustSpellGroupUnitPricePosition(tvUnitPrice)
        } else {
            tvUnitPrice.visibility = View.GONE
        }
    }

    /**
     * 调整拼团商品单价位置
     */
    private fun adjustSpellGroupUnitPricePosition(tvUnitPrice: TextView) {
        val tvSpellTime = baseViewHolder.getView<TextView>(R.id.tv_spell_time)
        val tvGroupbooking = baseViewHolder.getView<TextView>(R.id.tv_groupbooking)

        // 获取当前的约束布局参数
        val layoutParams = tvUnitPrice.layoutParams as? androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
        layoutParams?.let { params ->
            // 清除现有的水平约束
            params.startToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET

            // 根据 tv_spell_time 的可见性设置约束
            if (tvSpellTime.visibility == View.VISIBLE) {
                // tv_spell_time 可见时，单价放在其右边
                params.startToEnd = R.id.tv_spell_time
                params.marginStart = mContext.resources.getDimensionPixelSize(R.dimen.dimen_dp_8)
            } else {
                // tv_spell_time 不可见时，单价放在父容器左边
                params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
                params.marginStart = 0
            }

            tvUnitPrice.layoutParams = params

            // 同时调整 tv_groupbooking 的位置，确保它在单价右边
            adjustGroupbookingPosition(tvGroupbooking, tvUnitPrice)
        }
    }

    /**
     * 调整拼购信息的位置，确保它在单价右边
     */
    private fun adjustGroupbookingPosition(tvGroupbooking: TextView, tvUnitPrice: TextView) {
        val layoutParams = tvGroupbooking.layoutParams as? androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
        layoutParams?.let { params ->
            // 清除现有的水平约束
            params.startToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET

            // 设置 tv_groupbooking 在 tv_unit_price 右边
            if (tvUnitPrice.visibility == View.VISIBLE) {
                params.startToEnd = R.id.tv_unit_price
                params.marginStart = mContext.resources.getDimensionPixelSize(R.dimen.dimen_dp_8)
            } else {
                // 如果单价不显示，则根据 tv_spell_time 的状态决定位置
                val tvSpellTime = baseViewHolder.getView<TextView>(R.id.tv_spell_time)
                if (tvSpellTime.visibility == View.VISIBLE) {
                    params.startToEnd = R.id.tv_spell_time
                    params.marginStart = mContext.resources.getDimensionPixelSize(R.dimen.dimen_dp_8)
                } else {
                    params.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
                    params.marginStart = 0
                }
            }

            tvGroupbooking.layoutParams = params
        }
    }


}