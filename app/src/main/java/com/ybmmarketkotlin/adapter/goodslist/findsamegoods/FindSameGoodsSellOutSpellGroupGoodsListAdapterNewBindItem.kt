package com.ybmmarketkotlin.adapter.goodslist.findsamegoods

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarketkotlin.adapter.goodslist.SellOutSpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.bigpic.SellOutSpellGroupGoodsListAdapterNewBindItemBigPic

/**
 * 找相似拼团售罄
 */
class FindSameGoodsSellOutSpellGroupGoodsListAdapterNewBindItem(
    mContext: Context,
    val holder: YBMBaseHolder,
    rowsBean: Rows<PERSON>ean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    findSameGoodsAdapter: RecyclerView.Adapter<*>
): SellOutSpellGroupGoodsListAdapterNewBindItemBigPic(mContext, holder, rowsBean, countDownTimerMap, findSameGoodsAdapter) {

    override fun onShop(isShowShopInfo: Boolean) {
        super.onShop(isShowShopInfo)
        setFindSameGoodsShop(isShowShopInfo)
    }

    override fun onGoodsSpec() {
        baseViewHolder.setGone(R.id.iv_divider_of_spec_name, false)
        // 规格
        if (TextUtils.isEmpty(rowsBean.spec)) {
            baseViewHolder.setGone(R.id.tv_goods_spec, false)
        } else {
            baseViewHolder.setGone(R.id.tv_goods_spec, true)
            baseViewHolder.setText(R.id.tv_goods_spec, "${rowsBean.spec} / ")
        }
    }

    override fun onFinal() {
        super.onFinal()
        setFindSameGoodsCoupon(false)
        baseViewHolder.getView<ShopNameWithTagView>(R.id.data_tag_list_view)?.visibility = View.GONE
        baseViewHolder.setGone(R.id.tv_validity_period, !TextUtils.isEmpty(rowsBean.nearEffect))
    }

    override fun onBuyCount() {
        //空实现
        super.onBuyCount()
    }
}