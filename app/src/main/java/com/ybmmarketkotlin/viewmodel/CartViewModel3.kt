package com.ybmmarketkotlin.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import android.text.SpannableStringBuilder
import android.text.TextUtils
import androidx.activity.ComponentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.google.gson.Gson
import com.ybmmarket20.activity.PaymentActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.RefreshWrapperPagerBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SettleBean
import com.ybmmarket20.bean.cart.CartBeanWraper
import com.ybmmarket20.bean.cart.CollectionInvalidGoodsInfo
import com.ybmmarket20.bean.cart.INVALID_GOODS_ALL
import com.ybmmarket20.bean.cart.INVALID_GOODS_NORMAL
import com.ybmmarket20.bean.cart.INVALID_GOODS_PACKAGE
import com.ybmmarket20.bean.cart.Level0ItemShopFooterBean
import com.ybmmarket20.bean.cart.Level0ItemShopHeaderBean
import com.ybmmarket20.bean.cart.Level1InvalidGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodEndBean
import com.ybmmarket20.bean.cart.Level1ItemCommonGoodsBean
import com.ybmmarket20.bean.cart.Level1ItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemGroupFooterBean
import com.ybmmarket20.bean.cart.Level1ItemGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemSubShopHeaderBean
import com.ybmmarket20.bean.cart.MarketingTipsBean
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.eventTrack
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.network.request.CartRequest
import com.ybmmarket20.network.request.SearchDataRequest
import com.ybmmarket20.report.coupon.CouponUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_ADD_TO_CART
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore.Companion.get
import com.ybmmarketkotlin.viewmodel.changeCart.ChangeCartManager
import com.ybmmarketkotlin.viewmodel.changeCart.UiData
import com.ybmmarketkotlin.viewmodel.changeCart.WHAT_CHANGE_CART_ADD
import com.ybmmarketkotlin.viewmodel.changeCart.WHAT_CHANGE_CART_SELECT_ALL
import com.ybmmarketkotlin.viewmodel.changeCart.WHAT_CHANGE_CART_SELECT_GROUP
import com.ybmmarketkotlin.viewmodel.changeCart.WHAT_CHANGE_CART_SELECT_ITEM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.collections.set

/**
 *
 *   购物车数据显示结构：
 *   - 自营公司
 *      - 自营活动
 *      - 自营活动商品
 *      - ...
 *      - 自营活动商品
 *      - 自营套餐
 *      - 自营套餐商品
 *      - ...
 *      - 自营套餐商品
 *      - 自营店铺
 *      - 自营店铺商品
 *      - ...
 *      - 自营店铺商品
 *   - pop公司
 *      - pop商品
 *   - 失效头部
 *   - 失效商品
 *   - ....
 *   - 失效套餐
 *   - 失效套餐商品
 *   - ...
 *   - 失效套餐商品
 */
class CartViewModel3(val appLike: Application) : BaseViewModel(appLike) {
    val settleBean = MutableLiveData<SettleBean>()
    val onlyStatusResultBean = MutableLiveData<Boolean>()
    val onlyGoodStatusResultBean = MutableLiveData<Boolean>()
    val batchcollectResultBean = MutableLiveData<BaseBean<String>>()
    val batchCollectWithDataInfoLiveData = MutableLiveData<BaseBean<CollectionInvalidGoodsInfo>>()
    val recommendData = MutableLiveData<RefreshWrapperPagerBean<RowsBean>>()
    val showToastLiveData = MutableLiveData<String>()
    val changeCartManager = ChangeCartManager.getInstance()
    // 更新商品合计、总计等的loading状态，去结算按钮禁止点击
    val uiLiveData: LiveData<CartBeanWraper?> = changeCartManager.uiLoadingLiveData.map {
        return@map handleUiData(it, cartBean.value)
    }
    // 更新购物车数据
    val cartBean: LiveData<CartBeanWraper> = changeCartManager.uiCartDataLiveData.map {
        return@map it
    }

    //tranNo
    private val _tranNoLiveData = MutableLiveData<String>()
    val tranNoLiveData: LiveData<String> = _tranNoLiveData

    /**
     * 通知Ui进行loading
     */
    private fun handleUiData(uiData: UiData, cartBean: CartBeanWraper?): CartBeanWraper? {

        return when (uiData.msgType) {
            WHAT_CHANGE_CART_SELECT_ITEM -> {
                handleUiDataLoadingSelectItem(cartBean, uiData)
            }
            WHAT_CHANGE_CART_SELECT_GROUP -> {
                if (uiData.isSubShop) {
                    handleUiDataLoadingSelectSubShop(cartBean, uiData)
                } else {
                    handleUiDataLoadingSelectShop(cartBean, uiData)
                }
            }
            WHAT_CHANGE_CART_ADD -> handleUiDataLoading(uiData, cartBean)
            WHAT_CHANGE_CART_SELECT_ALL -> handleUiDataLoadingAll(cartBean, uiData)
            else -> null
        }
    }

    /**
     * 找到当前商品并找到商品对应店铺的footer更新footer状态
     */
    private fun handleUiDataLoading(uiData: UiData, cartBean: CartBeanWraper?): CartBeanWraper? {
        //加购时选择的商品是否是选中状态，默认是选中状态
        var selectGoodsCheckedStatus = true
        val pairMap = getPairMap(cartBean)
        pairMap.forEach {
            var isFound = false
            val header = it.key
            if (header.subItems != null && !isFound) {
                header.subItems.forEach subItemsFor@ {item ->
                    //商品
                    if ((item is Level1ItemGoodsBeanAbs && item.skuid == uiData.token)) {
                        isFound = true
                        try {
                            if (!item.rangePriceBean.isStep()) {
                                //普通品阶梯价使用后端返回的小计
                                val subTotal = item.unitPrice * (item.amount?.toInt()?: 0)
                                item.subtotal = SpannableStringBuilder("小计 ¥${UiUtils.transform(subTotal.toString() ?: "")}")
                            }
                            //获取选中商品的选中状态
                            selectGoodsCheckedStatus = item.selected
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        return@subItemsFor
                    }
                    //套餐
                    if (item is Level1ItemGroupFooterBean && item.packageId == uiData.token) {
                        isFound = true
                        try {
                            if (!item.rangePriceBean.isStep()) {
                                //普通品阶梯价使用后端返回的小计
                                val subTotal = item.realPrice * (item.amount?.toInt()?: 0)
                                item.subtotal = SpannableStringBuilder("小计 ¥${UiUtils.transform(subTotal.toString() ?: "")}").toString()
                            }
                            //获取选中商品的选中状态
                            selectGoodsCheckedStatus = item.selected
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        return@subItemsFor
                    }
                }
            }
            if (isFound) {
                it.value.isLoading = true
                return@forEach
            }
        }
        cartBean?.isLoading = true
        if (!selectGoodsCheckedStatus) {
            //选择的商品非选中状态下处理点击状态
            uiData.status = true
            handleUiDataLoadingSelectItem(cartBean, uiData)
        }
        return cartBean
    }

    /**
     * 获取每组的header和footer的映射
     */
    private fun getPairMap(cartBean: CartBeanWraper?): MutableMap<Level0ItemShopHeaderBean, Level0ItemShopFooterBean> {
        var keyHeader: Level0ItemShopHeaderBean? = null
        val pairMap = mutableMapOf<Level0ItemShopHeaderBean, Level0ItemShopFooterBean>()
        //成对获取header和footer
        cartBean?.cartEntityList?.forEach {
            if (it is Level0ItemShopHeaderBean) keyHeader = it
            if (it is Level0ItemShopFooterBean) {
                pairMap[keyHeader!!] = it
            }
        }
        return pairMap
    }

    /**
     * 更新全选状态
     */
    private fun handleUiDataLoadingAll(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        val checked = uiData.status
        val pairMap = getPairMap(cartBean)
        //筛选与选中状态相反的成对header和footer
        pairMap.filterKeys {
            it.selected != checked
        }.map {
            //设置店铺为指定状态
            it.key.selected = checked
            it.value.isLoading = true
            //设置商品为指定状态
            it.key.subItems.forEach {goods ->
                goods.selected = checked
            }
        }
        // 设置全选状态
        cartBean?.isLoading = true
        cartBean?.isSelected = checked
        return cartBean
    }


    /**
     * 更新指定店铺选中
     */
    private fun handleUiDataLoadingSelectShop(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        val checked = uiData.status
        val pairMap = getPairMap(cartBean)
        val checkStatusMap = pairMap.filterKeys {
            it.selected != checked
        }
        if (checked && checkStatusMap.size == 1) {
            cartBean?.isSelected = true
        } else if (!checked && cartBean?.isSelected == true) {
            cartBean.isSelected = false
        }
        checkStatusMap.filter {
            val shopCode = it.key.shopCode
            val orgId = it.key.orgId
            if (shopCode == null || orgId == null) return@filter false
            uiData.token == shopCode || uiData.token == orgId
        }.forEach {
            it.key.selected = checked
            it.value.isLoading = true
            it.key.subItems.forEach{ goods ->
                goods.selected = checked
            }
        }
        cartBean?.isLoading = true
        return cartBean
    }

    /**
     * 更新指定子店铺选中
     */
    private fun handleUiDataLoadingSelectSubShop(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        val checked = uiData.status
        val pairMap = getPairMap(cartBean)
        pairMap.filterKeys {
            it.selected != checked
        }.forEach { entry ->
            var isFound = false
            var currentGoodsCountInSubShop = 0
            entry.key.subItems.forEach subItemsFor@ {
                if (isFound && it is Level1ItemCommonGoodsBean) {
                    it.selected = checked
                    currentGoodsCountInSubShop++
                } else if (isFound) {
                    return@subItemsFor
                }
                if (it is Level1ItemSubShopHeaderBean && it.shopCode == uiData.token) {
                    it.selected = checked
                    entry.value.isLoading = true
                    isFound = true
                }
            }
            if (checked) {
                val currentAllGoodsCountInSubShopSelected = entry.key.subItems.count { it is Level1ItemCommonGoodsBean && !it.selected}
                if (currentAllGoodsCountInSubShopSelected == 0) entry.key.selected = true
            } else {
                entry.key.selected = false
                cartBean?.isSelected = false
            }
        }
        if (checked) {
            val unSelectGoodsCount = pairMap.map { it.key }
                .flatMap { it.subItems }
                .filter { !it.selected && (it is Level1ItemCommonGoodsBean || it is Level1ItemGroupHeaderBean) }.size
            cartBean?.isSelected = unSelectGoodsCount == 0
        }

        cartBean?.isLoading = true
        return cartBean
    }

    /**
     * 更新指定商品
     */
    private fun handleUiDataLoadingSelectItem(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        var isSubShop = false
        cartBean?.cartEntityList?.filterIsInstance<Level0ItemShopHeaderBean>()
            ?.forEach { header ->
                var isFound = false
                var currentSubShopHeader: Level1ItemSubShopHeaderBean? = null
                header.subItems.forEach {
                    if (it is Level1ItemSubShopHeaderBean) {
                        currentSubShopHeader = it
                    } else if ((it is Level1ItemCommonGoodsBean && it.skuid == uiData.token)
                        || (it is Level1ItemActivityGoodBean && it.skuid == uiData.token)
                        || (it is Level1ItemActivityGoodEndBean && it.skuid == uiData.token)
                        || (it is Level1ItemGroupHeaderBean && it.packageId == uiData.token)) {
                        isFound = true
                        return@forEach
                    }
                }
                if (isFound && currentSubShopHeader!=null) isSubShop = true
                else if (isFound) isSubShop = false
            }
        return if (isSubShop) {
            handleUiDataLoadingSelectSubShopItem(cartBean, uiData)
        } else {
            handleUiDataLoadingSelectNoSubShopItem(cartBean, uiData)
        }
    }

    /**
     * 更新指定商品
     */
    private fun handleUiDataLoadingSelectNoSubShopItem(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        val checked = uiData.status
        val pairMap = getPairMap(cartBean)
        //选中组
        var isCheckedGroup = false
        val groupMaps = pairMap.filter {
            if (!checked) {
                return@filter true
            }
            return@filter it.key.selected != checked
        }
        var clickCounter: Int
        groupMaps.forEach {
            clickCounter = 0
            var isFound = false
            it.key.subItems.forEach { goods ->
                if (goods.selected != checked
                    && (goods is Level1ItemCommonGoodsBean
                            || goods is Level1ItemGroupHeaderBean
                            || goods is Level1ItemActivityGoodBean
                            || goods is Level1ItemActivityGoodEndBean)) {
                    clickCounter++
                }
                if ((goods is Level1ItemCommonGoodsBean && goods.skuid == uiData.token)
                    || (goods is Level1ItemGroupHeaderBean && goods.packageId == uiData.token)
                    || (goods is Level1ItemActivityGoodBean && goods.skuid == uiData.token)
                    || (goods is Level1ItemActivityGoodEndBean && goods.skuid == uiData.token)){
                    isFound = true
                    goods.selected = checked
                }
            }
            //查找到选择项
            if (isFound) {
                //下面两种情况：
                //1、选中状态并且当前组未选中的选项只有一个则设置组状态选中
                //2、反选状态并且当前组全部为选中状态则取消选中组状态
                if (checked && clickCounter == 1) {
                    //选中
                    it.key.selected = true
                    isCheckedGroup = true
                } else if (!checked && clickCounter == it.key.subItems.count{ goods -> goods is Level1ItemCommonGoodsBean
                            || goods is Level1ItemGroupHeaderBean || goods is Level1ItemActivityGoodBean || goods is Level1ItemActivityGoodEndBean}){
                    //反选
                    it.key.selected = false
                    isCheckedGroup = true
                }
                it.value.isLoading = true
            }
        }

        //下面两种情况
        //1、选中状态、只有一组未选中、未选中组被选中设置全选
        //2、非选中状态、当前是全选状态设置非全选
        if (checked && groupMaps.size == 1 && isCheckedGroup) {
            cartBean?.isSelected = true
        } else if (!checked && cartBean?.isSelected == true) {
            cartBean.isSelected = false
        }
        cartBean?.isLoading = true
        return cartBean
    }

    /**
     * 更新指定子店铺商品
     */
    private fun handleUiDataLoadingSelectSubShopItem(cartBean: CartBeanWraper?, uiData: UiData): CartBeanWraper? {
        val checked = uiData.status
        val pairMap = getPairMap(cartBean)
        pairMap.filterKeys {
            it.selected != checked
        }.forEach { entry ->
            var subShopHeader: Level1ItemSubShopHeaderBean? = null
            var isFound = false
            var unCheckedCount = 0
            entry.key.subItems.forEach subItemsFor@ {
                if (it is Level1ItemSubShopHeaderBean && !isFound){
                    subShopHeader = it
                    unCheckedCount = 0
                }
                if (isFound && !(it is Level1ItemCommonGoodsBean || it is Level1ItemGroupHeaderBean)) {
                    return@subItemsFor
                }
                if (isFound && !it.selected && (it is Level1ItemCommonGoodsBean || it is Level1ItemGroupHeaderBean)) {
                    unCheckedCount++
                }
                if ((it is Level1ItemCommonGoodsBean && it.skuid == uiData.token)
                    || (it is Level1ItemGroupHeaderBean && it.packageId == uiData.token)) {
                    isFound = true
                    it.selected = checked
                    entry.value.isLoading = true
                    if (!checked) unCheckedCount++
                }
            }
            if (isFound){
                val header = entry.key
                //设置子店铺选中状态
                subShopHeader?.selected = unCheckedCount == 0
                //设置店铺选中状态
                header.selected = header.subItems.count { (it is Level1ItemCommonGoodsBean || it is Level1ItemGroupHeaderBean) && !it.selected } == 0
                entry.value.isLoading = true
                //设置全选状态
                if (!checked) cartBean?.isSelected = false
            }
        }
        if (checked) {
            val unSelectGoodsCount = pairMap.map { it.key }
                .flatMap { it.subItems }
                .filter { !it.selected && (it is Level1ItemCommonGoodsBean || it is Level1ItemGroupHeaderBean) }.size
            cartBean?.isSelected = unSelectGoodsCount == 0
        }
        cartBean?.isLoading = true
        return cartBean
    }

    /**
     * 获取购物车数据
     */
    fun getCartData(merchantId: String) {
        changeCartManager.getCartData()
    }

    /**
     * 修改商品的选中状态
     */
    fun changeGoodsSelectStatus(check: Boolean, itemId: String, isGroup: Boolean = false) {
        val paramMap = mapOf(
            "merchantId" to SpUtil.getMerchantid(),
            if (isGroup) {
                "packageId" to itemId
            } else {
                "skuId" to itemId
            }
        )
        changeCartManager.changeGoodsSelectStatus(paramMap, check, isGroup)
    }

    /**
     * 修改店铺的选中状态
     */
    fun changeShopSelectStatus(check: Boolean, itemId: String, isThirdCompany: Boolean, isSubShop: Boolean) {
        val paramMap = mapOf(
            "merchantId" to SpUtil.getMerchantid(),
            "orgId" to itemId,
            "isThirdCompany" to if (isThirdCompany) "1" else "0"
        )
        changeCartManager.changeSelectStatusAll(paramMap, check, true, isSubShop)
    }


    /**
     * 修改购物车所有商品的选中状态
     */
    fun changeAllSelectStatus(check: Boolean) {
        val paramMap = mapOf("merchantId" to SpUtil.getMerchantid())
        changeCartManager.changeSelectStatusAll(paramMap, check, false, isSubShop = false)
    }


    /**
     *  修改购物车商品的数量
     */
    fun changeCart(paramsMap: Map<String, String>) {
        changeCartManager.changeCartData(paramsMap)
    }

    /**
     * 从购物车中删除商品
     */
    fun removeProductFromCart(merchantId: String, packageIds: String, ids: String, isInvalidPackage: Boolean = false) {
        viewModelScope.launch {
            val result: BaseBean<EmptyBean>? = CartRequest().removeProductFromCart(merchantId, packageIds, ids)
            if (result?.isSuccess == true) {
                HandlerGoodsDao.getInstance().deleteItems(packageIds?.split(","))
                HandlerGoodsDao.getInstance().deleteItems(ids?.split(","))
                if (isInvalidPackage) showToastLiveData.postValue("收藏成功")
            }
            onlyStatusResultBean.postValue(result?.isSuccess)
        }
    }

    /**
     * 收藏商品
     */
    fun batchcollect(ids: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
            result?.let {
                val idsBaseBean = BaseBean(it, ids)
                idsBaseBean.let(batchcollectResultBean::postValue)
            }
        }
    }

    /**
     * 收藏并删除失效套餐
     */
    fun batchCollectAndRemoveInvalidPackage(collectionInvalidGoodsInfo: CollectionInvalidGoodsInfo) {
        viewModelScope.launch(Dispatchers.IO) {
            val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(collectionInvalidGoodsInfo.ids?: "")
            result?.let {
                if (result.isSuccess.not()) {
                    val idsBaseBean = BaseBean(it, collectionInvalidGoodsInfo.ids?: "")
                    idsBaseBean.let(batchcollectResultBean::postValue)
                } else {
                    removeProductFromCart(SpUtil.getMerchantid(), collectionInvalidGoodsInfo.packageId, "", true)
                }
            }
        }
    }

    /**
     * 失效商品收藏后从购物车移除(失效套餐单独处理)
     */
    fun batchCollectWithGoodsInfo(ids: String, goodList: MutableList<MultiItemEntity>?) {
        viewModelScope.launch(Dispatchers.IO) {
            val idArray = ids.split(",")
            if (idArray.size > 1) {
                // 所有无效商品移入收藏
                val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
                val goodsIds = mutableListOf<String>()
                val packageIds = mutableListOf<String>()
                goodList?.forEach {
                    if (it is Level1InvalidGoodBean) {
                        goodsIds.add(it.skuid ?: "")
                    }
                    if (it is Level1InvalidGroupHeaderBean) {
                        packageIds.add(it.packageId ?: "")
                    }
                }
                if (result?.isSuccess?.not() == true) {
                    batchCollectWithDataInfoLiveData.postValue(BaseBean<CollectionInvalidGoodsInfo>(result, null))
                } else {
                    batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(
                        CollectionInvalidGoodsInfo(INVALID_GOODS_ALL, packageIds.joinToString(","), goodsIds.joinToString(","))
                    ))
                }
                return@launch
            } else {
                // 单个无效商品
                val findResult = goodList?.filterIsInstance<Level1InvalidGoodBean>()
                    ?.find { it.skuid === ids }
                if (findResult != null) {
                    //失效商品（非套餐商品）
                    val result: BaseBean<EmptyBean>? = CartRequest().batchcollect(ids)
                    if (result?.isSuccess?.not() == true) {
                        batchCollectWithDataInfoLiveData.postValue(BaseBean<CollectionInvalidGoodsInfo>(result, null))
                    } else {
                        batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(CollectionInvalidGoodsInfo(
                            INVALID_GOODS_NORMAL, "", ids)))
                    }
                    return@launch
                }
                // 失效商品套餐 不收藏直接返回，接收数据后处理数据
                val groupGoodsList = goodList?.filterIsInstance<Level1InvalidGroupGoodBean>()
                val packageId = groupGoodsList?.find { it.skuid == ids }?.packageId
                val goodsInvalidPackageList = groupGoodsList?.filter { it.packageId == packageId && it.skuid != null }
                val idsList = goodsInvalidPackageList?.map { it.skuid?: "" }
                val idsListMutable = idsList?.toMutableList()
                val packageGroupGoodsIds = idsListMutable?.joinToString(",")
                batchCollectWithDataInfoLiveData.postValue(BaseBean.newSuccessBaseBean(CollectionInvalidGoodsInfo(
                    INVALID_GOODS_PACKAGE, packageId?: "", packageGroupGoodsIds)))
            }
        }
    }


    /**
     * 推荐商品
     */
    fun getRecommendGoodlist(params: Map<String, String>) {
        viewModelScope.launch {
            val recommendData = SearchDataRequest().getRecommendSearchDataRequest(params)
            <EMAIL>(recommendData?.data)
        }
    }


    /**
     * 领取优惠券跳转倒凑单页
     */
    fun getVoucher(voucherTemplateId: String?, chooseUrl: String?, spId: String?) {
        viewModelScope.launch {
            if (!TextUtils.isEmpty(voucherTemplateId)) {
                val paramsMap = mutableMapOf<String, String>()
                paramsMap["merchantId"] = SpUtil.getMerchantid()
                paramsMap["voucherTemplateId"] = voucherTemplateId!!
                var result: BaseBean<EmptyBean>? = CartRequest().getVoucher(paramsMap)
                // todo 这里的跳转路由可能有问题
                if (result?.isSuccess == true) {
                    chooseUrl?.let { RoutersUtils.open(CouponUtil.wrapperRouterUrlWithParam(chooseUrl, spId?: "")) }
                }
                onlyStatusResultBean.postValue(result?.isSuccess)
            } else {
                chooseUrl?.let { RoutersUtils.open(CouponUtil.wrapperRouterUrlWithParam(chooseUrl, spId?: "")) }
            }
        }
    }

    /**
     * 领取优惠券跳转倒凑单页
     */
    fun getVoucher2(voucherTemplateId: String?, appUrl: String?, shopDiscounts: MarketingTipsBean?) {
        viewModelScope.launch {
            if (!TextUtils.isEmpty(voucherTemplateId)) {
                val paramsMap = mutableMapOf<String, String>()
                paramsMap["merchantId"] = SpUtil.getMerchantid()
                paramsMap["voucherTemplateId"] = voucherTemplateId!!
                var result: BaseBean<EmptyBean>? = CartRequest().getVoucher(paramsMap)
                if (result?.isSuccess == true) {
                    RoutersUtils.open(appUrl)
                    shopDiscounts?.haseReceived = true
                    val properties = java.util.HashMap<String, Any>()
                    properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
                    properties[JGTrackManager.FIELD.FIELD_BTN_NAME] = "购物车店铺券凑单"
                } else {
                    ToastUtils.showShort("优惠券领取失败")
                }
            }
        }
    }

    /**
     * 生成预订单
     */
    fun preSettle(notSubmitOrderOrgIds: String?, context: Context?) {
        viewModelScope.launch {
            val paramsMap = mutableMapOf<String, String>()
            paramsMap["merchantId"] = SpUtil.getMerchantid()
            notSubmitOrderOrgIds?.let {
                paramsMap["notSubmitOrderOrgIds"] = it
                ToastUtils.showLong("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化")
            }
            val result: BaseBean<SettleBean>? = CartRequest().preSettle(paramsMap)
            if (result?.isSuccess == true) {
                result.data?.let {
                    val checkOutIntent =
                        Intent(context, PaymentActivity::class.java).apply {
                            putExtra("tranNo", it.tranNo)
                            putExtra(IntentCanst.JG_ENTRANCE, "购物车")
                            putExtra("isFromCart", "1")
                            notSubmitOrderOrgIds?.let {
                                putExtra("notSubmitOrderOrgIds",it)
                            }
                            cartBean.value?.shopInfoSxpList?.let { shopInfoSxpList ->
                                val shopInfoJson = Gson().toJson(shopInfoSxpList)
                                putExtra("shopInfoSxpList",shopInfoJson)
                            }
                        }
                    if (context is ComponentActivity) {
                        val viewModel: SpellGroupRecommendGoodsViewModel = ViewModelProvider(
                            get().getGlobalViewModelStore(), SavedStateViewModelFactory(
                                context.application,
                                context
                            )
                        ).get(SpellGroupRecommendGoodsViewModel::class.java)
                        viewModel.registerJumpType(SPELL_GROUP_RECOMMEND_ADD_TO_CART)
                    }
                    if (it.isShowDialog == 1 && context != null) {
                        AlertDialogEx(context)
                            .setMessage("您的资质已过期，请及时更新，以免影响发货")
                            .setCanceledOnTouchOutside(false)
                            .setConfirmButton("我知道了") {_, _-> context.startActivity(checkOutIntent)}
                            .show()
                    } else context?.startActivity(checkOutIntent)

                }
            }
            dismissLoading()
//            onlyStatusResultBean.postValue(result?.isSuccess)
        }
    }

    /**
     * 获取tranNo
     */
    fun preSettleTranNo(notSubmitOrderOrgIds: String?) {
        viewModelScope.launch {
            val paramsMap = mutableMapOf<String, String>()
            paramsMap["merchantId"] = SpUtil.getMerchantid()
            notSubmitOrderOrgIds?.let {
                paramsMap["notSubmitOrderOrgIds"] = it
                ToastUtils.showLong("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化")
            }
            val result: BaseBean<SettleBean>? = CartRequest().preSettle(paramsMap)
            result?.data?.let {
                _tranNoLiveData.postValue(it.tranNo)
            }
        }
    }





}