<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />
<!--去掉顶部tab标签-->
<!--    <com.flyco.tablayout.SlidingTabLayout-->
<!--        android:id="@+id/stl"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="44dp"-->
<!--        android:background="@color/white"-->
<!--        android:orientation="horizontal"-->
<!--        app:tl_indicator_color="@color/base_colors_new"-->
<!--        app:tl_indicator_corner_radius="2dp"-->
<!--        app:tl_indicator_height="4dp"-->
<!--        app:tl_indicator_width_equal_title="true"-->
<!--        app:tl_tab_space_equal="true"-->
<!--        app:tl_textAllCaps="true"-->
<!--        app:tl_textBold="BOTH"-->
<!--        app:tl_textSelectColor="@color/text_292933"-->
<!--        app:tl_textSelectSize="17sp"-->
<!--        app:tl_textUnselectColor="@color/text_898999"-->
<!--        app:tl_textsize="15sp" />-->

<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="1dp"-->
<!--        android:background="@color/color_f7f7f8" />-->

<!--    <com.ybmmarket20.view.NoScrollViewPager-->
<!--        android:id="@+id/nvp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_weight="1" />-->
    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
       />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dimen_dp_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E2E2E2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>
        <TextView
            android:id="@+id/tv_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_6"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_16"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@+id/divider"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="小计：¥213.00" />

        <TextView
            android:id="@+id/tv_amount_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:textColor="@color/text_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_amount"
            tools:text="再买¥65.00可使用优惠券" />

        <TextView
            android:id="@+id/tv_to_cart"
            android:layout_width="@dimen/dimen_dp_100"
            android:layout_height="@dimen/dimen_dp_50"
            android:background="@color/color_00B377"
            android:gravity="center"
            android:text="@string/go_to_cart"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
