<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:id="@+id/rl_protect_price"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@color/colors_fff7ef"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="自客户下单之日起30天内可享受保价护航"
            android:textColor="@color/colors_99664D"
            android:textSize="14sp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawableRight="@drawable/icon_protect_price_right"
            android:text="查看规则"
            android:textColor="@color/colors_99664D"
            android:textSize="14sp" />

    </RelativeLayout>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/color_F5F5F5" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_protect_price"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="申请保价护航"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:rv_backgroundColor="@color/base_colors"
        app:rv_cornerRadius="2dp" />

</LinearLayout>