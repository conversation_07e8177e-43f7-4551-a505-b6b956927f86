<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="@drawable/shape_select_service_bg"
        app:layout_constraintTop_toBottomOf="@+id/header">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_15"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:text="选择服务类型"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_14" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/color_F5F5F5"
            android:layout_marginTop="@dimen/dimen_dp_10" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tv_service_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_service_type"
            tools:itemCount="2" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>