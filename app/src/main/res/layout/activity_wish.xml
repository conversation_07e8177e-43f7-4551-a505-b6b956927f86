<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include
        layout="@layout/common_header_items"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:layout_marginBottom="-2dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/ps_tab"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/white"
            android:orientation="horizontal"
            app:tabTextAppearance="@style/MyWishTabTextAppearance"
            app:tl_indicator_color="@color/base_colors_new"
            app:tl_indicator_corner_radius="2dp"
            app:tl_indicator_height="4dp"
            app:tl_indicator_width="26dp"
            app:tl_indicator_width_equal_title="false"
            app:tl_tab_padding="11dp"
            app:tl_tab_space_equal="true"
            app:tl_textAllCaps="true"
            app:tl_textBold="BOTH"
            app:tl_textSelectColor="@color/color_292933"
            app:tl_textSelectSize="17sp"
            app:tl_textUnselectColor="@color/text_676773"
            app:tl_textsize="15sp" />

        <View
            android:id="@+id/view_bg"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_below="@+id/ps_tab"
            android:background="@color/color_F5F5F5" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_client"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/view_bg"
            android:background="@color/white" />

    </RelativeLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_wish"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="@string/wish_btn_add_text"
        android:textColor="@color/white"
        android:textSize="@dimen/wish_btn_size"
        app:rv_backgroundColor="@color/base_colors_new"
        app:rv_cornerRadius="2dp" />

</LinearLayout>