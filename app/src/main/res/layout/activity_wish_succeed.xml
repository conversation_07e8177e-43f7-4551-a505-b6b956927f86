<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <include layout="@layout/common_header_items"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal">

            <ImageView
                android:id="@+id/wish_succeed_iv"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerVertical="true"
                android:src="@drawable/wish02"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:layout_toRightOf="@+id/wish_succeed_iv"
                android:text="@string/wish_succeed_tv01"
                android:textColor="@color/home_back_selected"
                android:textSize="25sp"/>
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/wish_succeed_tv02"/>
        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:orientation="vertical">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/wish_succeed_commit_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:paddingBottom="12dp"
            android:paddingTop="12dp"
            android:text="返回首页"
            android:textColor="@color/back_gray"
            android:textSize="18sp"
            app:rv_backgroundColor="@color/base_color"
            app:rv_cornerRadius="7dp"/>
    </LinearLayout>
</LinearLayout>