<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:rv_backgroundColor="@android:color/white"
        app:rv_cornerRadius="2dp">

        <LinearLayout
            android:id="@+id/lyCompany"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_58"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent">

            <!--公司名称-->
            <TextView
                android:id="@+id/tvCompanyName"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/text_292933"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="武汉小药药"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/lyPop"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivPop"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dimen_dp_15"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:src="@drawable/icon_payment_pop" />

                <LinearLayout
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dimen_dp_6"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCompanyPopName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_292933"
                        android:textSize="@dimen/dimen_dp_14"
                        tools:text="以岭药业工业旗舰店" />

                    <TextView
                        android:id="@+id/tvOriginalName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_2"
                        android:textColor="@color/color_676773"
                        android:textSize="@dimen/dimen_dp_10"
                        tools:text="企业名称：中核医药（湖北）有限公司" />
                </LinearLayout>

                <ImageView
                    android:layout_width="@dimen/dimen_dp_15"
                    android:layout_height="@dimen/dimen_dp_15"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_dp_5"
                    android:src="@drawable/right_new" />
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_F5F5F5"
            app:layout_constraintTop_toBottomOf="@id/lyCompany" />

        <FrameLayout
            android:id="@+id/fyProduct"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10.5dp"
            app:layout_constraintTop_toBottomOf="@id/lyCompany">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lyMultiProduct"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_goods"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toStartOf="@id/tvProductTotalNum"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvProductTotalNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableEnd="@drawable/icon_right_gray"
                    android:gravity="center_vertical|end"
                    app:layout_constraintBottom_toBottomOf="@id/rv_goods"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/rv_goods"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/lySingleProduct"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_10"
                android:layout_marginEnd="15dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="83dp"
                    android:layout_height="83dp"
                    android:layout_centerVertical="true">

                    <ImageView
                        android:id="@+id/iv_order"
                        android:layout_width="73dp"
                        android:layout_height="73dp"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/product_image_bg"
                        android:padding="3dp" />

                    <com.ybmmarket20.view.PromotionTagView
                        android:id="@+id/view_ptv"
                        android:layout_width="73dp"
                        android:layout_height="73dp"
                        android:layout_centerInParent="true"
                        android:padding="3dp"
                        app:contentTextSize="7dp"
                        app:subTitleTextSize="4dp" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="12dp"
                    android:orientation="vertical"
                    android:paddingTop="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/tv_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:includeFontPadding="false"
                            android:maxLines="1"
                            android:text=""
                            android:textColor="@color/text_292933"
                            android:textSize="14dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_price"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:includeFontPadding="false"
                            android:textColor="@color/text_color_666666"
                            android:textSize="12dp"
                            tools:text="¥100" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6.5dp"
                        android:layout_marginBottom="@dimen/dimen_dp_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_guige"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:includeFontPadding="false"
                            android:textColor="@color/text_color_666666"
                            android:textSize="12dp"
                            tools:text="6g*20袋" />

                        <TextView
                            android:id="@+id/tv_num"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:includeFontPadding="false"
                            android:textColor="@color/text_color_666666"
                            android:textSize="12dp"
                            tools:text="x10" />
                    </LinearLayout>

                    <com.ybmmarket20.view.TagView
                        android:id="@+id/tg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6.5dp" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_find_same_goods"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dimen_dp_21"
                            android:layout_gravity="end"
                            android:background="@drawable/shape_find_same_goods"
                            android:gravity="center"
                            android:paddingStart="@dimen/dimen_dp_9"
                            android:paddingEnd="@dimen/dimen_dp_9"
                            android:text="找相似"
                            android:textColor="#121415"
                            android:textSize="@dimen/dimen_dp_12"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <com.ybmmarket20.common.widget.RoundTextView
                            android:id="@+id/tv_bug_again"
                            android:layout_width="80dp"
                            android:layout_height="31dp"
                            android:layout_gravity="end"
                            android:gravity="center"
                            android:text="再次购买"
                            android:textColor="@color/color_00b955"
                            android:textSize="14dp"
                            android:visibility="gone"
                            app:rv_backgroundColor="@color/color_EAF9F1"
                            app:rv_cornerRadius="4dp" />

                    </FrameLayout>
                </LinearLayout>
            </LinearLayout>
        </FrameLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/pabr_dimen10dp"
            android:background="@color/color_F5F5F5"
            app:layout_constraintTop_toBottomOf="@id/fyProduct" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10.5dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/fyProduct">

            <FrameLayout
                android:id="@+id/fySubAmount"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp">

                <TextView
                    android:id="@+id/tvSubAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:text="商品合计"
                    android:textColor="@color/color_676773"
                    android:textSize="@dimen/pax_core_sp_13" />

                <TextView
                    android:id="@+id/tvSubAmountPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:textColor="@color/color_333"
                    android:textStyle="bold" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fyCarriageFee"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp">

                <TextView
                    android:id="@+id/tvCarriageFeeLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:text="运费"
                    android:textColor="@color/color_676773"
                    android:textSize="@dimen/pax_core_sp_13" />

                <TextView
                    android:id="@+id/tvCarriageFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:textColor="@color/color_333"
                    android:textStyle="bold" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fyDisAmount"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp">

                <TextView
                    android:id="@+id/tvDisAmountLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:text="优惠金额"
                    android:textColor="@color/color_676773"
                    android:textSize="@dimen/pax_core_sp_13" />

                <TextView
                    android:id="@+id/tvDisAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:textColor="@color/color_red_FF0000"
                    android:textStyle="bold" />
            </FrameLayout>

            <FrameLayout
                android:id="@+id/fyRealPay"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp">

                <TextView
                    android:id="@+id/tvRealPayLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:drawableEnd="@drawable/icon_down_arrow"
                    android:text="实付款"
                    android:textColor="@color/color_676773"
                    android:textSize="@dimen/pax_core_sp_13" />

                <TextView
                    android:id="@+id/tvRealPay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:textColor="@color/color_333"
                    android:textStyle="bold" />
            </FrameLayout>

            <!--        <LinearLayout-->
            <!--            android:id="@+id/lyExpand"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:orientation="vertical">-->

            <LinearLayout
                android:id="@+id/ll_order_trading_snapshot"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_order_trading_snapshot"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_676773"
                    android:textSize="13sp" />

                <TextView
                    style="@style/payment_item_layout_text_left"
                    android:text="发生交易争议时，可作为判断依据"
                    android:textColor="@color/text_292933"
                    android:textSize="12sp" />
            </LinearLayout>
            <!--随货资质需求-->
            <LinearLayout
                android:id="@+id/llLicense"
                android:layout_width="match_parent"
                android:layout_height="@dimen/pabr_dimen35dp"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvLicenseTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="资质随货"
                    android:textColor="@color/text_676773"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tvLicenseArrow"
                    style="@style/payment_item_layout_text_left"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:textColor="@color/text_292933"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/lyOrderRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/pabr_dimen10dp"
                android:background="@color/white"
                android:paddingVertical="@dimen/pabr_dimen10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="订单备注"
                    android:textColor="@color/text_676773"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_order_remark"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginEnd="@dimen/dimen_dp_15"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:maxLines="2"
                    android:text=""
                    android:textColor="@color/color_292933"
                    android:textSize="12sp" />
            </LinearLayout>

            <com.ybmmarket20.view.OrderServiceView
                android:id="@+id/orderServiceView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_F5F5F5" />

            <LinearLayout
                android:id="@+id/ll_kufu"
                style="@style/order_detail_item_layout"
                android:layout_height="@dimen/dimen_dp_45"
                android:divider="@drawable/divider_line_h_1px_f5f5f5"
                android:gravity="center"
                android:orientation="horizontal"
                android:showDividers="middle">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_kefu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_kefu"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/icon_third_company_kefu"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tv_online_kefu"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_online_kefu"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="联系商家在线客服"
                        android:textColor="@color/text_color_333333"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_kefu"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_call_service"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tv_call_service"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableStart="@drawable/icon_order_detail_im"
                        android:drawablePadding="10dp"
                        android:text="@string/kefu_im_order_detail"
                        android:textColor="@color/text_676773"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_call_service_bubble"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_dp_60"
                        android:background="@drawable/shape_call_service_bg"
                        android:paddingStart="@dimen/dimen_dp_3"
                        android:paddingEnd="@dimen/dimen_dp_3"
                        android:text="处理更快"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dimen_dp_8"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@+id/tv_call_service"
                        app:layout_constraintStart_toStartOf="@+id/tv_call_service" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.ybmmarket20.view.DrawableTextView
                    android:id="@+id/tv_kefu"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/icon_order_phone"
                    android:drawablePadding="7dp"
                    android:gravity="center_vertical"
                    android:text="@string/kefuPhone_order_detail"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_10"
        android:background="@color/color_F5F5F5" />
</LinearLayout>