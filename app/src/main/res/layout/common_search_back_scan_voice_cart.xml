<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_search_container"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:background="@color/white"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_20"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:src="@drawable/icon_common_back"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toStartOf="@+id/v_search_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />


    <View
        android:id="@+id/v_search_bg"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_34"
        android:layout_marginLeft="@dimen/dimen_dp_42"
        android:layout_marginRight="@dimen/dimen_dp_42"
        android:background="@color/color_F5F5F5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_scan"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginLeft="@dimen/dimen_dp_11"
        android:src="@drawable/icon_home_steady_scan"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintStart_toStartOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />


    <View
        android:id="@+id/v_splite_line"
        android:layout_width="1dp"
        android:layout_height="18dp"
        android:layout_centerVertical="true"
        android:layout_marginLeft="8dp"
        android:layout_toRightOf="@id/iv_scan"
        android:background="@color/gray_C4C4C4"
        app:layout_constraintBottom_toBottomOf="@id/v_search_bg"
        app:layout_constraintLeft_toRightOf="@id/iv_scan"
        app:layout_constraintTop_toTopOf="@id/v_search_bg" />

    <EditText
        android:id="@+id/tv_title_et"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginRight="6dp"
        android:background="@color/color_F5F5F5"
        android:gravity="left|center_vertical"
        android:hint="搜索此商家药品"
        android:imeOptions="actionSearch"
        android:lines="1"
        android:paddingLeft="3dp"
        android:singleLine="true"
        android:textColor="#FF292933"
        android:textColorHint="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/v_splite_line"
        app:layout_constraintRight_toLeftOf="@id/iv_voice"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />

    <ImageView
        android:id="@+id/iv_voice"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginEnd="@dimen/dimen_dp_6"
        android:src="@drawable/icon_home_steady_voice"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toEndOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />

    <ImageView
        android:id="@+id/iv_clear_edit"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginEnd="@dimen/dimen_dp_6"
        android:src="@drawable/clear_sousou"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toEndOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rl_cart"
        style="@style/header_layout_right_img"
        android:layout_width="42dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_cart"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:src="@drawable/icon_title_cart"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="4dp"
            android:background="@drawable/bg_message"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="9+"
            tools:visibility="visible" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_search_history_cancel"
        android:layout_height="match_parent"
        android:layout_width="42dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="取消"
        android:textSize="@dimen/dimen_dp_15"
        android:textColor="@color/color_292933"
        android:layout_marginStart="@dimen/dimen_dp_3"
        android:gravity="center_vertical" />


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_f6f6f6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>