<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F5F5F5"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colors_fff7ef"
        android:gravity="center"
        android:minHeight="40dp"
        android:padding="2dp"
        android:text="如果您有多个账号(多个店),可以添加账号方便切换"
        android:textColor="@color/colors_99664D"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="57dp"
        android:background="@drawable/base_header_default_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.ybmmarket20.common.widget.RoundRelativeLayout
            android:id="@+id/rel_search"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:rv_backgroundColor="@color/color_F5F5F5"
            app:rv_cornerRadius="2dp">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:src="@drawable/icon_a_magnifying_glass" />

            <com.ybmmarket20.view.EditTextWithDel
                android:id="@+id/title_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="34dp"
                android:layout_toLeftOf="@+id/iv_clear"
                android:background="@null"
                android:drawableLeft="@drawable/manufacturers_search"
                android:hint="@string/search_account_manager_hint"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/text_9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_clear"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@drawable/clear_sousou"
                android:visibility="invisible" />

        </com.ybmmarket20.common.widget.RoundRelativeLayout>

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="0dp"
        android:background="@color/white">

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/rv_account_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/divider" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@+id/ll_btn_list"
            android:background="#f5f5f5" />

        <LinearLayout
            android:id="@+id/ll_btn_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="15dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/update_account_list"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="完成"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:visibility="gone"
                app:rv_backgroundColor="@color/base_colors"
                app:rv_cornerRadius="2dp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_edit_account"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="编辑"
                android:textColor="@color/base_colors"
                android:textSize="16sp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="2dp"
                app:rv_strokeColor="@color/base_colors"
                app:rv_strokeWidth="1dp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_add_account"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center"
                android:text="添加新账号"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:rv_backgroundColor="@color/base_colors"
                app:rv_cornerRadius="2dp" />
        </LinearLayout>

    </RelativeLayout>

</LinearLayout>
