<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_fff7ef"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_combo_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/icon_commodity_combo_bg"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="套餐价" />
    <!--加购按钮-->
    <com.ybmmarket20.view.ProductEditLayout3
        android:id="@+id/el_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="14dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_combo"
        android:layout_marginTop="10dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginEnd="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <com.ybmmarketkotlin.views.RvIndicatorView
        android:id="@+id/riv_combo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_gravity="center_horizontal"
        app:rv_indicator_layout_height="2dp"
        app:rv_indicator_layout_width="30dp"
        app:rv_indicator_height="2dp"
        app:rv_indicator_width="15dp"
        app:rv_indicator_layout_background="@drawable/shape_indicator_layout_bg"
        app:rv_indicator_background="@drawable/shape_indicator_bg"
        />
</LinearLayout>
