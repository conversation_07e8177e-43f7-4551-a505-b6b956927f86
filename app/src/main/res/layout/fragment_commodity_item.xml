<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="198dp"
    android:layout_gravity="center"
    android:layout_weight="1"
    android:background="@color/white"
    android:minWidth="178dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="198dp"
        android:gravity="center_horizontal"
        android:minWidth="178dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="120dp">

            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_centerInParent="true"
                app:rv_cornerRadius="5dp"
                app:rv_strokeColor="@color/tv_detail_recommend_image_bg"
                app:rv_strokeWidth="0.33dp">

                <ImageView
                    android:id="@+id/icon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerInParent="true" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>

            <TextView
                android:id="@+id/tv_recommend_mask_layer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:background="@drawable/shop_limit01"
                android:gravity="center"
                android:text=""
                android:textColor="#ffffff"
                android:textSize="12dp"
                android:visibility="visible" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_name_recommend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/tv_detail_recommend_name"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_description_recommend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tv_recommend_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:text="¥"
                android:textColor="@color/cart_tv_only_price"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tv_unit_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:background="@color/tv_unit_price_bg"
                android:paddingLeft="4dp"
                android:paddingTop="1dp"
                android:paddingRight="4dp"
                android:paddingBottom="1dp"
                android:textColor="@color/text_9494A6"
                android:textSize="10sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_audit_passed_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_7"
                android:text="@string/audit_passed_visible"
                android:textColor="#ffff982c"
                android:textSize="@dimen/dimen_dp_12"
                android:visibility="gone" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <!-- 拼团 -->
                <ImageView
                    android:id="@+id/iv_title_tag"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_dp_16"
                    android:scaleType="fitXY"
                    android:src="@drawable/icon_spell_group_goods_home_red"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_yellow"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:id="@+id/tv_price_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_dp_3"
                    android:text="拼团价"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dimen_dp_8"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_title_tag"
                    app:layout_constraintEnd_toStartOf="@+id/iv_yellow"
                    app:layout_constraintStart_toStartOf="@+id/iv_title_tag" />

                <ImageView
                    android:id="@+id/iv_yellow"
                    android:layout_width="@dimen/dimen_dp_50"
                    android:layout_height="@dimen/dimen_dp_18"
                    android:scaleType="fitXY"
                    android:src="@drawable/icon_spell_group_goods_home_yellow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvSpellPrice"
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_3"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="￥26.70"
                    android:textColor="#FE2021"
                    android:textSize="@dimen/dimen_dp_7"
                    app:layout_constraintBottom_toBottomOf="@id/iv_yellow"
                    app:layout_constraintEnd_toEndOf="@+id/iv_yellow"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@+id/iv_yellow"
                    app:layout_constraintTop_toTopOf="@+id/iv_yellow" />


                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupSpellPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="iv_title_tag, tv_price_title, iv_yellow, tvSpellPrice"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>
    </LinearLayout>
</FrameLayout>