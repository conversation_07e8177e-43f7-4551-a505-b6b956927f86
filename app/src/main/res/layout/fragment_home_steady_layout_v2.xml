<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_home_steady"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_home_steady"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_f7f7f8"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <View
            android:id="@+id/status_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/white"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 头部搜索   -->
        <com.ybmmarket20.view.homesteady.HomeSteadySearchView2
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingTop="@dimen/dimen_dp_7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/status_bar" />

        <View
            android:id="@+id/v_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_7"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@+id/searchView" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/v_divider"
            android:background="@color/white"
            app:layout_constraintVertical_weight="1">

            <com.ybmmarket20.view.HomeSteadyHeader
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_70" />

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/clList"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_f7f7f8"
                app:layout_constraintTop_toBottomOf="@+id/v_divider">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/abl"
                    app:layout_behavior="com.ybmmarket20.view.homesteady.HomeSteadyHeaderBehavior"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:elevation="@dimen/dimen_dp_0">

                    <include
                        layout="@layout/header_home_steady_v2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_scrollFlags="scroll"
                        android:background="@color/color_F5F5F5" />
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_10"
                        app:layout_scrollFlags="scroll"
                        android:background="@color/color_F5F5F5" />

                    <com.flyco.tablayout.SlidingHomeSteadyTabLayout
                        android:id="@+id/homeTabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_50"
                        android:background="@drawable/shape_home_steady_tab_layout"
                        android:orientation="horizontal"
                        app:tl_indicator_color="@color/color_00b377"
                        app:tl_indicator_corner_radius="2dp"
                        app:tl_indicator_height="4dp"
                        app:tl_indicator_width_equal_title="true"
                        app:tl_tab_space_equal="false"
                        app:tl_textAllCaps="true"
                        app:tl_textBold="BOTH"
                        app:tl_textSelectColor="@color/text_292933"
                        app:tl_textSelectSize="17sp"
                        app:tl_textUnselectColor="@color/text_292933"
                        app:tl_textsize="15sp" />
                    <View
                        android:id="@+id/tabLayoutShadow"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_5"
                        android:alpha="1.0"
                        android:visibility="gone"
                        android:background="@drawable/shape_home_steady_tablayout_shadow" />
                </com.google.android.material.appbar.AppBarLayout>
                <androidx.viewpager.widget.ViewPager
                    android:id="@+id/vp_home"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior"
                    app:layout_scrollFlags="scroll|enterAlways" />

            </androidx.coordinatorlayout.widget.CoordinatorLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <com.ybmmarket20.view.DialImageView
            android:id="@+id/iv_dial_suspension"
            android:layout_width="114dp"
            android:layout_height="76dp"
            android:layout_marginBottom="90dp"
            android:src="@drawable/transparent"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/iv_ad_suspension"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="110dp"
            android:src="@drawable/icon_ad_suspension"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="gone" />

        <LinearLayout
            android:id="@+id/ll_mask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent" />

        <LinearLayout
            android:id="@+id/ll_home_error_net"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0"
            app:layout_constraintTop_toBottomOf="@+id/searchView"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center"
            android:visibility="gone"
            android:background="@color/white"
            android:orientation="vertical">
            <ImageView
                android:layout_width="@dimen/dimen_dp_216"
                android:layout_height="@dimen/dimen_dp_216"
                android:src="@drawable/icon_error_net" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/dimen_dp_18"
                android:textColor="#A8A8B8"
                android:text="网络加载失败" />

            <TextView
                android:id="@+id/tv_home_error_net_btn"
                android:layout_width="@dimen/dimen_dp_180"
                android:layout_height="@dimen/dimen_dp_40"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:text="点击重新加载"
                android:gravity="center"
                android:textSize="@dimen/dimen_dp_19"
                android:textColor="@color/color_00b377"
                android:background="@drawable/shape_error_net_btn" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>