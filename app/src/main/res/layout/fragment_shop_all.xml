<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_shop_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:minHeight="@dimen/dimen_dp_86">

        <ImageView
            android:id="@+id/iv_shop_icon"
            android:layout_width="@dimen/dimen_dp_47"
            android:layout_height="@dimen/dimen_dp_47"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_20"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/logo" />


        <TextView
            android:id="@+id/tv_shop_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/iv_shop_icon"
            app:layout_constraintTop_toTopOf="@id/iv_shop_icon"
            tools:text="小妖妖自营旗舰店 小妖妖自营旗舰店" />


        <TextView
            android:id="@+id/tv_shop_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_10"
            android:drawablePadding="@dimen/dimen_dp_5"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_11"
            app:layout_constraintBottom_toBottomOf="@id/iv_shop_icon"
            app:layout_constraintLeft_toRightOf="@id/iv_shop_icon"
            tools:text="xxx元起送，xxx元包邮" />


        <ImageView
            android:id="@+id/iv_share_shop"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_marginRight="@dimen/dimen_dp_15"
            android:src="@drawable/icon_shop_share"
            app:layout_constraintBottom_toBottomOf="@id/tv_shop_name"
            app:layout_constraintRight_toLeftOf="@id/iv_link_shop_user"
            app:layout_constraintTop_toTopOf="@id/tv_shop_name" />

        <ImageView
            android:id="@+id/iv_link_shop_user"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_marginRight="@dimen/dimen_dp_10"
            android:src="@drawable/icon_link_shop_user"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_shop_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_shop_name"
            tools:visibility="gone" />

        <ScrollView
            android:id="@+id/sv_shop_tag1"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/iv_shop_icon"
            app:layout_constraintTop_toBottomOf="@+id/iv_shop_icon"
            tools:visibility="visible">

            <com.ybmmarket20.view.ShopNameWithTagView
                android:id="@+id/snwtv_shop_tag1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </ScrollView>

        <TextView
            android:id="@+id/tv_shop_tag2"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_3"
            android:layout_marginBottom="@dimen/dimen_dp_17"
            android:singleLine="true"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/iv_shop_icon"
            app:layout_constraintTop_toBottomOf="@+id/sv_shop_tag1"
            tools:visibility="visible" />

        <Space
            android:id="@+id/space_shop_tag"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_10"
            app:layout_constraintTop_toBottomOf="@+id/tv_shop_tag2" />

        <View
            android:id="@+id/v_tag_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/colors_f5f5f5"
            app:layout_constraintTop_toBottomOf="@+id/space_shop_tag" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/colors_f5f5f5"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_37"
            android:orientation="horizontal">

            <com.flyco.tablayout.SlidingTabLayout
                android:id="@+id/tabLayout"
                android:layout_width="@dimen/dimen_dp_150"
                android:layout_height="37dp"
                app:tl_indicator_color="@color/color_00b377"
                app:tl_indicator_corner_radius="2dp"
                app:tl_indicator_height="@dimen/dimen_dp_3"
                app:tl_indicator_width="@dimen/dimen_dp_20"
                app:tl_tab_space_equal="true"
                app:tl_textBold="SELECT"
                app:tl_textSelectColor="@color/color_292933"
                app:tl_textSelectSize="@dimen/dimen_dp_15"
                app:tl_textUnselectColor="@color/color_676773"
                app:tl_textsize="@dimen/dimen_dp_14" />

            <View
                android:layout_width="@dimen/dimen_dp_0"
                android:layout_height="@dimen/dimen_dp_0"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_qualification_afterSale"
                android:layout_width="@dimen/dimen_dp_60"
                android:layout_height="@dimen/dimen_dp_22"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:background="@drawable/shape_shop_btn"
                android:gravity="center"
                android:text="资质/配送"
                android:textColor="#01B377"
                android:textSize="@dimen/dimen_dp_12"/>
            <TextView
                android:id="@+id/tv_on_line"
                android:layout_width="@dimen/dimen_dp_60"
                android:layout_height="@dimen/dimen_dp_22"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:background="@drawable/shape_shop_btn"
                android:gravity="center"
                android:text="在线客服"
                android:textColor="#01B377"
                android:textSize="@dimen/dimen_dp_12" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="@color/colors_f5f5f5" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

</LinearLayout>