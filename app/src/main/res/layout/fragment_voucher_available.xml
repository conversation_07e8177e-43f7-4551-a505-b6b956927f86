<?xml version="1.0" encoding="utf-8"?><!--VoucherAvailableFragment和VoucherAvailableForShopFragment复用-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!--顶部提示文案-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F7F7F8">

        <TextView
            android:id="@+id/tv_voucher_title"
            android:layout_width="0dp"
            android:layout_height="@dimen/couponmember_title_h"
            android:gravity="left|center_vertical"
            android:paddingLeft="@dimen/dimen_dp_10"
            android:textColor="#676773"
            android:textSize="@dimen/dimen_dp_13"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="已使用23张，已优惠2222元"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_select_Optimal"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_30"
            android:layout_marginTop="@dimen/dimen_dp_7"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="@dimen/dimen_dp_7"
            android:background="#00B377"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="使用推荐优惠券"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_14"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--显示列表项-->
    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/available_lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/colors_f5f5f5" />
    <!--功能按钮-->
    <LinearLayout
        android:id="@+id/ll_not"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:background="@color/white"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_not"
            style="@style/voucher_available_style"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="7dp"
            android:text="不使用优惠券"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            app:rv_strokeColor="@color/colors_E4E4EB"
            app:rv_strokeWidth="1dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_btn_confirm"
            style="@style/voucher_available_style"
            android:layout_marginLeft="7dp"
            android:layout_marginRight="10dp"
            android:text="确定"
            android:textColor="@color/white"
            app:rv_backgroundColor="@color/base_colors"
            app:rv_cornerRadius="2dp" />
    </LinearLayout>

</LinearLayout>