<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_no_start_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="45dp"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/bg_goods_detail_no_start_spell_group"
        android:paddingStart="10dp"
        android:paddingTop="5dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="拼团价"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_no_start_spell_group_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="¥15.25" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@color/color_00A76E"
        android:gravity="center"
        android:minWidth="@dimen/dimen_dp_138"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingRight="12dp">

        <TextView
            android:id="@+id/tv_no_start_spell_group_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_F4E984"
            android:textSize="12sp"
            tools:text="03月21日 18:00 开抢" />

    </LinearLayout>

</LinearLayout>