<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_120">

    <ImageView
        android:id="@+id/iv_agent_order_goods"
        android:layout_width="@dimen/dimen_dp_90"
        android:layout_height="@dimen/dimen_dp_90"
        android:layout_marginTop="@dimen/dimen_dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/iv_agent_order_goods_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_agent_order_goods"
        tools:text="白云山 清开灵颗粒" />

    <TextView
        android:id="@+id/iv_agent_order_goods_spec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order_goods"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order_goods_name"
        tools:text="6g*20袋" />

    <TextView
        android:id="@+id/iv_agent_order_goods_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order_goods"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order_goods_spec"
        tools:text="单价:¥2.35" />

    <TextView
        android:id="@+id/iv_agent_order_goods_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order_goods_price"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order_goods_spec"
        tools:text="毛利率:30%" />

    <TextView
        android:id="@+id/tv_agent_order_goods_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintStart_toEndOf="@+id/iv_agent_order_goods"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order_goods_rate"
        tools:text="小计：¥1850.00" />

    <TextView
        android:id="@+id/tv_agent_order_goods_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:layout_marginEnd="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_agent_order_goods_name"
        tools:text="x50" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/color_F5F5F5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_agent_order_goods_amount" />

</androidx.constraintlayout.widget.ConstraintLayout>