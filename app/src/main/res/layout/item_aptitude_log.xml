<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/ll_log_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/ll_log_line"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingTop="5dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_audit_times"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minWidth="75dp"
                android:textColor="@color/text_color_333333"
                android:textSize="15dp"
                android:textStyle="bold"
                tools:text="一审" />

            <TextView
                android:id="@+id/tv_auditTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_color_333333"
                android:textSize="15dp"
                android:textStyle="bold"
                tools:text="11111111111" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:minWidth="75dp"
                android:text="操作人："
                android:textColor="@color/color_text_little_base_color"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="start"
                android:textColor="@color/color_text_base_color"
                android:textSize="14dp"
                tools:text="操作人：" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:minWidth="75dp"
                android:text="审批状态："
                android:textColor="@color/color_text_little_base_color"

                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_operation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="start"
                android:textColor="@color/color_text_base_color"
                android:textSize="14dp"
                tools:text="驳回" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_cause_c"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="visible">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:minWidth="75dp"
                android:textColor="@color/color_text_little_base_color"
                android:textSize="14dp"
                android:text="@string/str_aptitude_log_reject_cause"/>

            <TextView
                android:id="@+id/tv_cause"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="start"
                android:textColor="@color/color_text_base_color"
                android:textSize="14dp"
                tools:text="驳回详情" />
        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="20dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_log_line"
        android:layout_width="26dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/ll_log_content"
        android:layout_alignBottom="@id/ll_log_content"
        android:layout_marginTop="2dp"
        android:orientation="vertical"
        android:paddingTop="5dp">

        <ImageView
            android:id="@+id/iv_circle"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="5dp"
            android:src="@drawable/shap_aptitude_statues_circle_unchecked" />

        <ImageView
            android:id="@+id/iv_line"
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:background="@color/color_divider_circle_log_bg" />
    </LinearLayout>

</RelativeLayout>