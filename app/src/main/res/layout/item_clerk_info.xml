<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvPhoneNo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_16"
        android:textColor="@color/color_292933"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_17"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        android:layout_marginTop="@dimen/dimen_dp_15"
        app:layout_constraintTop_toBottomOf="@+id/tvPhoneNo" />
</androidx.constraintlayout.widget.ConstraintLayout>