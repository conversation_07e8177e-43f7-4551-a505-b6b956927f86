<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_find_same_goods_item_bg"
    android:layout_marginBottom="@dimen/dimen_dp_10"
    android:layout_marginStart="@dimen/dimen_dp_5"
    android:layout_marginEnd="@dimen/dimen_dp_5"
    tools:layout_height="wrap_content"
    tools:layout_width="match_parent">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/dimen_dp_91"
        android:layout_height="@dimen/dimen_dp_91"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_shop_mark"
        android:layout_width="@dimen/dimen_dp_91"
        android:layout_height="@dimen/dimen_dp_91"
        app:layout_constraintLeft_toLeftOf="@id/icon"
        app:layout_constraintTop_toTopOf="@id/icon" />

    <com.ybmmarket20.view.PromotionTagView
        android:id="@+id/view_ptv"
        android:layout_width="@dimen/dimen_dp_91"
        android:layout_height="@dimen/dimen_dp_91"
        app:contentTextSize="7dp"
        app:layout_constraintLeft_toLeftOf="@id/icon"
        app:layout_constraintTop_toTopOf="@id/icon"
        app:shopBottomTextSize="6dp"
        app:shopContentTextSize="9dp"
        app:shopTimeTextSize="6dp"
        app:shopTopTextSize="@dimen/dimen_dp_7"
        app:subTitleTextSize="4dp" />

    <TextView
        android:id="@+id/shop_no_limit_tv01"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:background="@drawable/bg_goods_sold_out"
        android:gravity="center"
        android:textColor="@color/color_fdfdfd"
        android:textSize="@dimen/dimen_dp_13"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/icon"
        app:layout_constraintLeft_toLeftOf="@id/icon"
        app:layout_constraintRight_toRightOf="@id/icon"
        app:layout_constraintTop_toTopOf="@id/icon"
        tools:text="售罄"
        tools:visibility="invisible" />

    <TextView
        android:id="@+id/rtv_buy_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:paddingStart="3dp"
        android:paddingEnd="3dp"
        android:textColor="@color/text_676773"
        android:textSize="@dimen/dimen_dp_10"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/icon"
        app:layout_constraintRight_toRightOf="@id/icon"
        app:layout_constraintTop_toBottomOf="@id/icon"
        tools:background="@color/base_colors"
        tools:text="买过1次"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_coupon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_promotion_more, bg_coupon_left, bg_coupon_middle, bg_coupon_Right, bg_coupon" />

    <View
        android:id="@+id/bg_coupon"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_22"
        android:background="#fff9f9"
        android:layout_marginTop="@dimen/dimen_dp_10"
        app:layout_constraintTop_toBottomOf="@+id/icon" />

    <TextView
        android:id="@+id/tv_coupon_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textColor="#f82324"
        android:textSize="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_7"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toStartOf="@+id/iv_promotion_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon"
        tools:text="券 | 每满1500减20" />

    <com.ybmmarket20.view.ShopNameWithTagView
        android:id="@+id/rl_icon_type_top"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_dp_20"
        android:visibility="gone"
        android:layout_marginStart="@dimen/dimen_dp_9"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toStartOf="@+id/iv_promotion_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon"
        tools:visibility="visible" />

    <View
        android:id="@+id/bg_coupon_Right"
        android:layout_width="@dimen/dimen_dp_3"
        android:layout_height="@dimen/dimen_dp_3"
        android:layout_marginEnd="@dimen/dimen_dp_12"
        android:background="@drawable/shape_coupon_btn_dot_small"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon" />

    <View
        android:id="@+id/bg_coupon_middle"
        android:layout_width="@dimen/dimen_dp_3"
        android:layout_height="@dimen/dimen_dp_3"
        android:layout_marginEnd="@dimen/dimen_dp_3"
        android:background="@drawable/shape_coupon_btn_dot_big"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toStartOf="@+id/bg_coupon_Right"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon" />

    <View
        android:id="@+id/bg_coupon_left"
        android:layout_width="@dimen/dimen_dp_3"
        android:layout_height="@dimen/dimen_dp_3"
        android:layout_marginEnd="@dimen/dimen_dp_3"
        android:background="@drawable/shape_coupon_btn_dot_small"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toStartOf="@+id/bg_coupon_middle"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon" />

    <ImageView
        android:id="@+id/iv_promotion_more"
        android:layout_width="@dimen/dimen_dp_21"
        android:layout_height="@dimen/dimen_dp_0"
        android:layout_marginEnd="@dimen/dimen_dp_9"
        app:layout_constraintBottom_toBottomOf="@+id/bg_coupon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_coupon" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        app:constraint_referenced_ids="bg_coupon,rtv_buy_count,icon"
        android:layout_width="wrap_content"
        app:barrierDirection="bottom"
        android:layout_height="wrap_content"/>

    <!--  商品信息区 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_7"
        android:layout_marginEnd="@dimen/dimen_dp_7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrier">

        <!--商品名字  如 阿莫西林胶囊-->
        <TextView
            android:id="@+id/shop_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_14"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="【拼团】20盒包邮 念慈菴蜜炼川贝枇杷膏念慈菴蜜炼川贝枇杷膏念慈菴蜜炼川贝枇杷膏/12颗*1盒" />

        <!--商品规格-->
        <TextView
            android:id="@+id/tv_goods_spec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/colors_676773"
            android:textSize="@dimen/dimen_dp_9"
            app:layout_constraintLeft_toLeftOf="@id/shop_name"
            app:layout_constraintTop_toBottomOf="@id/shop_name"
            tools:text="2.5mg*7s*2板 / "
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_divider_of_spec_name"
            android:layout_width="2dp"
            android:layout_height="3dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="3dp"
            android:background="@drawable/divider_line_vertial_spec_goodsname"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tv_goods_spec"
            app:layout_constraintLeft_toRightOf="@id/tv_goods_spec"
            app:layout_constraintRight_toLeftOf="@id/tv_chang_name"
            app:layout_constraintTop_toTopOf="@id/tv_goods_spec" />

        <!--商品出厂-->
        <TextView
            android:id="@+id/tv_chang_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/colors_676773"
            android:textSize="@dimen/dimen_dp_9"
            app:layout_constraintLeft_toRightOf="@id/iv_divider_of_spec_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/shop_name"
            tools:text="施慧达药业集团(吉林)有限公..."
            tools:visibility="visible" />

        <!-- 有效期-->
        <TextView
            android:id="@+id/tv_validity_period"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:textColor="@color/colors_676773"
            android:textSize="@dimen/dimen_dp_9"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="@id/shop_name"
            app:layout_constraintTop_toBottomOf="@id/tv_chang_name"
            tools:text="有效期:2025.09.11/2029.11.22"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--  价格标签区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_tag_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_7"
        app:layout_constraintLeft_toLeftOf="@id/cl_goods_info"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_goods_info">

        <LinearLayout
            android:id="@+id/llTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="gone"
            android:orientation="vertical">
            <!--标签 比上次购买时降   60天最低价   区域毛利榜  -->
            <com.ybmmarket20.view.ShopNameWithTagView
                android:id="@+id/data_tag_list_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_5"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.ybmmarket20.view.ShopNameWithTagView
                android:id="@+id/rl_icon_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_retail_price"
                android:layout_marginTop="@dimen/dimen_dp_4"
                android:layout_marginRight="@dimen/dimen_dp_20"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />
        </LinearLayout>

        <!-- 价格 -->
        <RelativeLayout
            android:id="@+id/ll_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llTag"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_audit_passed_visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="价格认证资质可见"
                android:textColor="@color/detail_tv_FF982C"
                android:textSize="@dimen/dimen_dp_15"
                android:visibility="gone"
                tools:text="价格认证资质可见"
                tools:visibility="gone" />

            <TextView
                android:id="@+id/shop_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:lines="1"
                android:singleLine="true"
                android:textColor="@color/record_red"
                android:textSize="11dp"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="¥55.00" />

            <TextView
                android:id="@+id/tv_original_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/shop_price"
                android:ellipsize="end"
                android:lines="1"
                android:singleLine="true"
                android:textColor="@color/color_ff2121"
                android:textSize="11dp"
                android:visibility="visible"
                tools:text="折后价 50"
                tools:visibility="visible" />

        </RelativeLayout>

        <!-- 拼团相关组件       -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_groupbooking"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:visibility="gone"
            android:background="@drawable/icon_spell_group_btn_active_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_product_price"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_groupbooking"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_676773"
                android:textSize="@dimen/dimen_dp_6"
                android:layout_marginTop="@dimen/dimen_dp_2"
                android:layout_marginStart="@dimen/dimen_dp_5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="已拼3210盒/20盒起拼" />

            <ProgressBar
                android:id="@+id/progress"
                style="@style/HorizontalProgressBarSpellGroup"
                android:layout_width="@dimen/dimen_dp_49"
                android:layout_height="@dimen/dimen_dp_5"
                android:progressDrawable="@drawable/bg_progressbar_groupbooking"
                android:layout_marginBottom="@dimen/dimen_dp_3"
                android:layout_marginStart="@dimen/dimen_dp_5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:progress="60" />

            <TextView
                android:id="@+id/tv_groupbooking_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dimen_dp_4"
                android:textColor="#FE572B"
                android:textSize="@dimen/dimen_dp_6"
                app:layout_constraintBottom_toBottomOf="@id/progress"
                app:layout_constraintLeft_toRightOf="@id/progress"
                app:layout_constraintTop_toTopOf="@id/progress"
                tools:text="1000%" />

            <TextView
                android:id="@+id/tv_join_groupbooking"
                android:layout_width="@dimen/dimen_dp_60"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_spell_group_btn_active"
                android:text="立即参团"
                android:gravity="center"
                android:paddingStart="@dimen/dimen_dp_5"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_11"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--控销价/零售价 && 毛利率-->
        <TextView
            android:id="@+id/tv_retail_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_product_price"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:lines="1"
            android:orientation="horizontal"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_11"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_groupbooking"
            tools:text="零售价 ¥19.00（毛利率25%）" />

        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/icon_countdown"
            android:drawablePadding="@dimen/dimen_dp_5"
            android:textColor="@color/color_FE5427"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:textSize="@dimen/dimen_dp_8"
            android:gravity="center_vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_groupbooking"
            tools:text="距离结束仅剩 6:12:30"
            tools:visibility="visible" />

        <com.ybmmarket20.view.ShopNameWithTagView
            android:id="@+id/tag_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:layout_marginEnd="@dimen/dimen_dp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_countdown" />

        <!--加购按钮-->
        <com.ybmmarket20.view.ProductEditLayoutNew
            android:id="@+id/el_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/ll_product_price"
            app:layout_constraintRight_toRightOf="parent"
            tools:visibility="visible" />

        <!-- 订阅-->
        <LinearLayout
            android:id="@+id/ll_subscribe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/tv_retail_price"
            app:layout_constraintRight_toRightOf="parent"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/iv_goods_subscribe"
                android:layout_width="@dimen/dimen_dp_22"
                android:layout_height="@dimen/dimen_dp_22"
                android:layout_gravity="center_horizontal"
                android:visibility="visible"
                tools:src="@drawable/icon_goods_subscribe"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_goods_subscribe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_292933"
                android:textSize="@dimen/dimen_dp_12"
                android:visibility="visible"
                tools:text="到货通知"
                tools:visibility="visible" />

        </LinearLayout>

        <!--   中包装-->
        <TextView
            android:id="@+id/shop_price_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="2dp"
            android:layout_marginBottom="3dp"
            android:singleLine="true"
            android:textColor="@color/brand_description_tv1"
            android:textSize="10sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@id/el_edit"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="中包装：10盒"
            tools:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--秒杀立即抢购-->
    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_seckill_commit"
        android:layout_width="44dp"
        android:layout_height="@dimen/dimen_dp_25"
        app:rv_backgroundColor="@color/color_ff2121"
        app:rv_cornerRadius="13dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintTop_toTopOf="@+id/cl_goods_tag_price"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="去抢购" />

    <View
        android:id="@+id/divider_shop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:background="#f1f1f1"
        app:layout_constraintBottom_toTopOf="@+id/cl_pop_company" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_pop_company"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:paddingTop="@dimen/dimen_dp_5"
        android:paddingBottom="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toStartOf="@+id/ll_shop_same_goods"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_goods_tag_price">

        <TextView
            android:id="@+id/tv_pop_company"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dimen_dp_2"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_11"
            android:visibility="visible"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintEnd_toStartOf="@id/tv_goto_shop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_goto_shop"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_pop_company"
            app:layout_constraintEnd_toStartOf="@id/iv_goto_shop"
            android:text="进店"
            android:textColor="@color/text_color_333333"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="7dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textSize="11dp"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_goto_shop"
            android:layout_width="12dp"
            app:layout_constraintStart_toEndOf="@id/tv_goto_shop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="1dp"
            android:background="@drawable/icon_goto_shop"
            android:layout_height="12dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_shop_same_goods"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/cl_pop_company"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cl_pop_company"
        app:layout_constraintTop_toTopOf="@+id/cl_pop_company">

        <View
            android:layout_width="@dimen/dimen_dp_1"
            android:layout_height="@dimen/dimen_dp_7"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dimen_dp_7"
            android:visibility="gone"
            android:background="@color/detail_tv_575766" />

        <TextView
            android:id="@+id/tv_shop_same_goods"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textSize="@dimen/dimen_dp_11"
            android:drawableEnd="@drawable/icon_goto_shop"
            android:drawablePadding="@dimen/dimen_dp_3"
            android:layout_marginStart="@dimen/dimen_dp_15"
            android:drawableStart="@drawable/icon_shop_same_goods"
            android:textColor="@color/color_676773"
            android:text="店铺同款商品" />
    </LinearLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_shop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="divider_shop, cl_pop_company" />

</androidx.constraintlayout.widget.ConstraintLayout>