<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dimen_dp_90"
    android:layout_height="@dimen/dimen_dp_110"
    android:layout_marginEnd="@dimen/dimen_dp_10"
    android:background="@drawable/shape_home_shop_goods_spell_group">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/detail_tv_wrap_74dp"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:layout_marginStart="@dimen/dimen_dp_2"
        android:layout_marginEnd="@dimen/dimen_dp_2"
        android:id="@+id/background_view"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:background="@drawable/shape_home_shop_goods_bg"/>

    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="@dimen/dimen_dp_80"
        android:layout_height="@dimen/dimen_dp_62"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginTop="@dimen/dimen_dp_9"/>

    <TextView
            android:id="@+id/tv_assemblePrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/background_view"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="3dp"
            android:layout_marginStart="4dp"
            android:textSize="8sp"
            android:textColor="@color/white"
            android:text="拼团价:"/>

    <TextView
        android:id="@+id/price_detail"
        android:text="TextView"
        android:layout_width="77dp"
        android:layout_height="15dp"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:layout_below="@id/tv_assemblePrice"
        android:layout_alignLeft="@id/tv_assemblePrice"
        android:layout_centerHorizontal="true" />


</RelativeLayout>