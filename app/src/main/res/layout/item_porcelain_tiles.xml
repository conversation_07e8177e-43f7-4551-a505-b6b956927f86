<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:background="@android:color/transparent"
        android:layout_marginTop="7dp"
        android:layout_height="wrap_content">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:shapeAppearanceOverlay="@style/ShapeableImageView_10dp_rounded_corner"
            android:background="@color/white"
            android:layout_width="0dp"
            android:scaleType="centerCrop"
            android:layout_height="0dp"/>

        <ImageView
            android:id="@+id/iv_title_tag"
            android:layout_width="20dp"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="6dp"
            android:layout_marginStart="8dp"
            tools:src="@drawable/icon_all_drug_search_black"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_height="20dp"/>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toEndOf="@id/iv_title_tag"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/iv_title_tag"
            app:layout_constraintBottom_toBottomOf="@id/iv_title_tag"
            android:layout_marginStart="1dp"
            app:layout_constraintEnd_toStartOf="@id/cl_describe"
            android:textColor="@color/color_292933"
            android:textSize="15dp"
            android:layout_marginEnd="3dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            tools:text="拼团推荐拼团推荐拼团推荐拼团推荐拼团推荐拼团推荐"
            android:textStyle="bold"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_img_title"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toEndOf="@id/iv_title_tag"
            android:layout_marginStart="3dp"
            app:layout_constraintTop_toTopOf="@id/iv_title_tag"
            app:layout_constraintBottom_toBottomOf="@id/iv_title_tag"
            android:visibility="gone"
            android:layout_width="70dp"
            android:layout_height="15dp"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_describe"
            android:layout_width="wrap_content"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            android:paddingEnd="5dp"
            android:layout_height="wrap_content">


            <TextView
                android:id="@+id/tv_describe"
                android:layout_width="wrap_content"
                android:textColor="@color/color_9A9A9A"
                android:textSize="11dp"
                tools:text="官方补贴"
                android:maxLines="1"
                android:ellipsize="end"
                android:maxWidth="50dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginEnd="5dp"
                app:layout_constraintEnd_toStartOf="@id/iv_arrow_right"
                android:layout_height="wrap_content"/>

            <ImageView
                android:id="@+id/iv_img_describe"
                android:scaleType="centerCrop"
                android:layout_marginStart="3dp"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@id/iv_arrow_right"
                android:layout_marginEnd="5dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="42dp"
                android:layout_height="11dp"/>

            <ImageView
                android:id="@+id/iv_arrow_right"
                android:layout_width="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:src="@drawable/ic_arrow"
                android:scaleType="fitCenter"
                app:layout_constraintTop_toTopOf="@id/tv_describe"
                app:layout_constraintBottom_toBottomOf="@id/tv_describe"
                android:layout_height="10dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_porcelain_tiles"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            tools:spanCount="2"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
