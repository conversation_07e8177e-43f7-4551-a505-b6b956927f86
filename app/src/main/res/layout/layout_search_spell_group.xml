<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_search_spell_group_head_bg"
        android:scaleType="fitXY"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_48"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_20"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_search_spell_group_title"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginTop="@dimen/dimen_dp_11"
        app:layout_constraintStart_toStartOf="@+id/iv_search_spell_group_head_bg"
        android:layout_marginStart="@dimen/dimen_dp_11"
        app:layout_constraintTop_toTopOf="@+id/iv_search_spell_group_head_bg" />

    <TextView
        android:id="@+id/tv_search_spell_group_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_13"
        android:textColor="@color/color_292933"
        android:drawableEnd="@drawable/icon_search_spell_group_arrow_right"
        android:drawablePadding="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/iv_search_spell_group_title"
        app:layout_constraintEnd_toEndOf="@+id/iv_search_spell_group_head_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_search_spell_group_title" />

    <ImageView
        android:id="@+id/iv_content_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintTop_toTopOf="@+id/rv_search_spell_group"
        app:layout_constraintBottom_toBottomOf="@+id/rv_search_spell_group"
        android:scaleType="centerCrop" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_spell_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/iv_search_spell_group_head_bg"
        app:spanCount="3"
        tools:itemCount="6"
        tools:listitem="@layout/item_search_spell_group" />

</androidx.constraintlayout.widget.ConstraintLayout>