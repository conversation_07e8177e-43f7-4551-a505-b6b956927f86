<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.view.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/rl_root"
        android:layout_width="match_parent"
        android:layout_height="116dp"
        android:layout_centerVertical="true"
        android:background="@color/white"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="15dp">

        <RelativeLayout
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!--<TextView-->
            <!--android:id="@+id/tv_product_standard"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="4dp"-->
            <!--android:ellipsize="end"-->
            <!--android:maxLines="1"-->
            <!--android:textColor="#9494A6"-->
            <!--android:textSize="16sp"-->
            <!--tools:text="2*12包/盒" />-->

            <TextView
                android:id="@+id/tv_product_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/iv_edit"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/color_292933"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="阿莫西林克拉维酸钾" />

            <LinearLayout
                android:id="@+id/ll_product_company"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_product_name"
                android:layout_marginTop="6dp"
                android:layout_toLeftOf="@+id/iv_edit"
                android:orientation="horizontal">

                <com.ybmmarket20.common.widget.RoundTextView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:gravity="center"
                    android:includeFontPadding="true"
                    android:text="厂"
                    android:textColor="@color/tag_head_text"
                    android:textSize="10sp"
                    app:rv_backgroundColor="@color/tag_head_background"
                    app:rv_cornerRadius="1dp"
                    app:rv_strokeColor="@color/tag_head_stroke"
                    app:rv_strokeWidth="1dp" />

                <TextView
                    android:id="@+id/tv_product_company"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:layout_marginRight="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_676773"
                    android:textSize="12sp"
                    tools:text="阿莫西林克拉维酸钾" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_product_num"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_below="@+id/ll_product_company"
                android:layout_marginTop="6dp"
                android:layout_toLeftOf="@+id/iv_edit"
                android:drawablePadding="2dp"
                android:textColor="@color/text_9494A6"
                android:textSize="12sp"
                tools:text="补货数量：2" />

            <TextView
                android:id="@+id/tv_product_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_below="@+id/tv_product_num"
                android:layout_marginTop="6dp"
                android:textColor="@color/text_9494A6"
                android:textSize="12sp"
                tools:text="¥9.00" />

            <TextView
                android:id="@+id/tv_audit_passed_visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_below="@+id/tv_product_num"
                android:layout_marginTop="6dp"
                android:textColor="@color/detail_tv_FF982C"
                android:textSize="@dimen/dimen_dp_14"
                android:text="@string/audit_passed_visible" />

            <ImageView
                android:id="@+id/iv_edit"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="58dp"
                android:src="@drawable/icon_edit" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/color_F5F5F5" />
        </RelativeLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        android:background="#eb4d3d"
        android:gravity="center"
        android:text="删除"
        android:textColor="@android:color/white"
        android:textSize="14sp" />
</com.ybmmarket20.view.SwipeMenuLayout>