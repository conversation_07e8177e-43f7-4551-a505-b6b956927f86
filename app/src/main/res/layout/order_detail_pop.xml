<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_2"
        android:scaleType="fitXY"
        android:src="@drawable/icon_order_detail_shadow"/>
    <!--有可能显示的过高-->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.ybmmarket20.common.widget.RoundLinearLayout
            style="@style/payment_item_layout"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:orientation="vertical"
            android:padding="0dp"
            android:paddingBottom="12dp"
            app:rv_strokeColor="@color/color_F5F5F5"
            app:rv_strokeWidth="1dp">

            <!--商品总金额-->
            <LinearLayout
                android:id="@+id/ll_order_total"
                style="@style/payment_item_layout_pop"
                android:paddingTop="@dimen/dimen_dp_15">

                <TextView
                    android:id="@+id/tv_total"
                    style="@style/payment_item_order_text_key_pop"
                    android:text="@string/payment_tv24" />

                <TextView
                    android:id="@+id/tv_total_num"
                    style="@style/payment_item_order_text_value"
                    android:textColor="@color/text_9494A6"
                    android:textSize="@dimen/dimen_dp_13" />
            </LinearLayout>

            <LinearLayout
                style="@style/payment_item_layout"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:orientation="vertical">
                <!--运费-->
                <LinearLayout
                    android:id="@+id/ll_order_freight"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_freight"
                        style="@style/payment_item_order_text_key_pop"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:drawableRight="@drawable/icon_freight_tip"
                        android:drawablePadding="@dimen/dimen_dp_3"
                        android:text="运费" />

                    <TextView
                        android:id="@+id/tv_freight_num"
                        style="@style/payment_item_order_text_value2"
                        android:layout_weight="1"
                        android:gravity="end|center_vertical"
                        tools:text="123" />
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/ll_order_limit_time_Discount"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_order_limit_time_Discount"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="限时加补" />

                    <TextView
                        android:id="@+id/tv_order_limit_time_Discount_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>
                <!--满减-->
                <LinearLayout
                    android:id="@+id/ll_order_fl"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_order_fl"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="满减" />

                    <TextView
                        android:id="@+id/tv_order_fl_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>
                <!--一口价-->
                <LinearLayout
                    android:id="@+id/ll_order_one_price"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_order_one_price_text"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="一口价优惠" />

                    <TextView
                        android:id="@+id/tv_order_one_price_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>
                <!--优惠券-->
                <LinearLayout
                    android:id="@+id/ll_order_coupon"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_order_coupon"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="优惠券" />

                    <TextView
                        android:id="@+id/tv_order_coupon_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>
                <!--余额抵扣-->
                <LinearLayout
                    android:id="@+id/ll_order_balance"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_order_balance"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="余额抵扣" />


                    <TextView
                        android:id="@+id/tv_order_balance_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_order_red_envelope"
                    style="@style/payment_item_order_text_layout_pop"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_order_red_envelope"
                        style="@style/payment_item_order_text_key_pop"
                        android:text="红包" />


                    <TextView
                        android:id="@+id/tv_order_red_envelope_num"
                        style="@style/payment_item_order_text_value2" />
                </LinearLayout>
                <!--支付优惠-->
                <LinearLayout
                    android:id="@+id/ll_order_pay_discount"
                    style="@style/payment_item_order_text_layout_pop">

                    <TextView
                        android:id="@+id/tv_pay_discount"
                        style="@style/payment_item_order_text_key_pop"
                        android:layout_width="wrap_content"
                        android:layout_weight="0"
                        android:drawablePadding="@dimen/dimen_dp_3"
                        android:text="支付优惠" />

                    <TextView
                        android:id="@+id/tv_pay_discount_num"
                        style="@style/payment_item_order_text_value2"
                        android:layout_weight="1"
                        android:gravity="end|center_vertical"
                        tools:text="123" />
                </LinearLayout>
            </LinearLayout>
        </com.ybmmarket20.common.widget.RoundLinearLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_15"
        android:background="@color/white">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginStart="@dimen/dimen_dp_11"
            android:layout_marginEnd="@dimen/dimen_dp_11"
            android:background="@color/color_F5F5F5"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    <View-->
    <!--        android:elevation="2dp"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="0.5dp"-->
    <!--        android:background="@color/color_F5F5F5" />-->

    <View
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="0dp" />
</LinearLayout>