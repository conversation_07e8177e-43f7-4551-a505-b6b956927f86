<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:listViewStyle">@style/ListView</item>
        <item name="android:scrollViewStyle">@style/ScrollView</item>
        <item name="android:dialogTheme">@style/AlertDialog</item>
        <item name="android:windowBackground">@color/white</item>
<!--        <item name="android:windowAnimationStyle">@style/AnimationActivity</item>-->
    </style>

    <!--activity 进出场动画-->
    <style name="AnimationActivity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style>

    <!-- 启动页theme.注意windowBackground -->
    <style name="AppTheme2" parent="AppTheme">
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <!--通用dialog 样式-->
    <style name="AlertDialog" parent="android:Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 圆角背景 -->
        <!-- 模糊 -->
    </style>

    <style name="HorizontalProgressBar" parent="@android:style/Widget.ProgressBar.Horizontal">
        <!--<item name="progressDrawable">@drawable/progress_horizontal</item>-->
        <!--<item name="indeterminateDrawable">@drawable/progress_indeterminate_horizontal</item>-->
        <!--<item name="minHeight">20dip</item>-->
        <!--<item name="maxHeight">20dip</item>-->
        <!--<item name="mirrorForRtl">true</item>-->
        <item name="android:indeterminateOnly">false</item>
        <item name="android:maxHeight">6dp</item>
        <item name="android:minHeight">6dp</item>
        <item name="android:progressDrawable">@drawable/bg_progressbar</item>

    </style>

    <style name="HorizontalProgressBarSpellGroup" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:maxHeight">5dp</item>
        <item name="android:minHeight">5dp</item>
        <item name="android:progressDrawable">@drawable/bg_progressbar_spell_group</item>

    </style>

    <style name="SeckillProgress" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:maxHeight">5dp</item>
        <item name="android:minHeight">5dp</item>
        <item name="android:progressDrawable">@drawable/bg_progressbar_seckill_listitem</item>

    </style>

    <style name="HorizontalProgressBarSpellGroup2" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/bg_progressbar_spell_group2</item>

    </style>

    <!--通用ListView样式 -->
    <style name="ListView" parent="android:Widget.AbsListView">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:fastScrollEnabled">false</item>
        <item name="android:footerDividersEnabled">false</item>
        <item name="android:fadingEdgeLength">0dp</item>
        <item name="android:fadingEdge">none</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:cacheColorHint">@android:color/transparent</item>
    </style>
    <!--通用scrollview 样式-->
    <style name="ScrollView" parent="android:Widget.ScrollView">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:fadingEdge">none</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:scrollbars">none</item>
    </style>

    <!--tabLayout-->
    <style name="MyCustomTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="MyWishTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="MyWishTabTextAppearance_collect" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="HomeTextView">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_9494A6</item>
    </style>

    <style name="MoreTextView">
        <item name="android:textSize">10sp</item>
        <item name="android:textColor">#ff676773</item>
        <item name="android:background">@color/white</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="AlertActivity_AlertStyle" parent="android:Theme.Dialog">
        <!--<item name="android:windowAnimationStyle">@style/AnimAlert</item>-->
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景透明 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="PopupWindowAnimation">
        <item name="android:windowEnterAnimation">@anim/popshow_anim</item>
        <item name="android:windowExitAnimation">@anim/pophidden_anim</item>
    </style>

    <style name="CustomCheckboxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/checkbox_style</item>
    </style>

    <style name="voucher_check_box_theme" parent="CustomCheckboxTheme">
        <item name="android:button">@drawable/check_box_style_voucher</item>
    </style>

    <style name="voucher_check_box_theme2" parent="CustomCheckboxTheme">
        <item name="android:button">@drawable/checkbox_style</item>
    </style>

    <style name="captureCheckboxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/checkbox_capture_style</item>
    </style>

    <style name="addressCheckboxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_address_new</item>
    </style>

    <style name="addressCheckboxTheme_new" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_address</item>
    </style>

    <style name="shoucangCheckboxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_common_shoucang2</item>
    </style>

    <style name="inventoryCheckboxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_common_inventory</item>
    </style>

    <style name="shoucangCheckboxTheme2" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_common_shoucang_detail</item>
    </style>

    <style name="shoucangCheckboxTheme3" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_common_shoucang3</item>
    </style>

    <style name="shoucangCheckboxTheme4" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/selector_common_shoucang4</item>
    </style>

    <!--  这个是加入的代码 -->
    <style name="mypopwindow_anim_style">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <!-- 指定显示的动画xml -->

        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>
        <!-- 指定消失的动画xml -->
    </style>


    <style name="anim_top_to_view">
        <item name="android:windowEnterAnimation">@anim/push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/push_bottom_out</item>
    </style>

    <!--弹出底部  -->
    <style name="AnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/push_bottom_out</item>
    </style>

    <!--中间弹出  -->
    <style name="AnimCanter" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/pay_result_center_in</item>
        <item name="android:windowExitAnimation">@anim/pay_result_center_out</item>
    </style>

    <style name="pop_ani_left_right">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_right</item>
    </style>

    <style name="pop_ani_bottom_top">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>
    </style>

    <declare-styleable name="FlowLayout">
        <attr name="horizontal_spacing" format="dimension" />
        <attr name="vertical_spacing" format="dimension" />
    </declare-styleable>

    <style name="TagGroup">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="atg_isAppendMode">false</item>
        <item name="atg_inputHint">添加tag</item>
        <item name="atg_borderColor">#49C120</item>
        <item name="atg_textColor">#49C120</item>
        <item name="atg_backgroundColor">#FFFFFF</item>
        <item name="atg_dashBorderColor">#AAAAAA</item>
        <item name="atg_inputHintColor">#80000000</item>
        <item name="atg_inputTextColor">#DE000000</item>
        <item name="atg_checkedBorderColor">#49C120</item>
        <item name="atg_checkedTextColor">#FFFFFF</item>
        <item name="atg_checkedMarkerColor">#FFFFFF</item>
        <item name="atg_checkedBackgroundColor">#49C120</item>
        <item name="atg_pressedBackgroundColor">#EDEDED</item>
        <item name="atg_borderStrokeWidth">0.5dp</item>
        <item name="atg_textSize">13sp</item>
        <item name="atg_horizontalSpacing">8dp</item>
        <item name="atg_verticalSpacing">4dp</item>
        <item name="atg_horizontalPadding">12dp</item>
        <item name="atg_verticalPadding">3dp</item>
    </style>

    <style name="TagGroup.Small" parent="TagGroup">
        <item name="atg_textSize">10sp</item>
        <item name="atg_horizontalSpacing">6dp</item>
        <item name="atg_verticalSpacing">6dp</item>
        <item name="atg_horizontalPadding">8dp</item>
        <item name="atg_verticalPadding">2dp</item>
    </style>

    <style name="TagGroup.Large" parent="TagGroup">
        <item name="atg_borderStrokeWidth">0.7dp</item>
        <item name="atg_textSize">15sp</item>
        <item name="atg_horizontalSpacing">9dp</item>
        <item name="atg_verticalSpacing">5dp</item>
        <item name="atg_horizontalPadding">14dp</item>
        <item name="atg_verticalPadding">4dp</item>
    </style>

    <style name="TagGroup.Beauty_Red" parent="TagGroup">
        <item name="atg_borderColor">#FF3D7F</item>
        <item name="atg_textColor">#FF3D7F</item>
        <item name="atg_checkedBorderColor">#FF3D7F</item>
        <item name="atg_checkedBackgroundColor">#FF3D7F</item>
    </style>

    <style name="TagGroup.Beauty_Red.Inverse" parent="TagGroup">
        <item name="atg_borderColor">#FF3D7F</item>
        <item name="atg_textColor">#FFFFFF</item>
        <item name="atg_backgroundColor">#FF3D7F</item>
        <item name="atg_inputHintColor">#80FFFFFF</item>
        <item name="atg_inputTextColor">#DEFFFFFF</item>
        <item name="atg_checkedBorderColor">#FF3D7F</item>
        <item name="atg_checkedTextColor">#FF3D7F</item>
        <item name="atg_checkedMarkerColor">#FF3D7F</item>
        <item name="atg_checkedBackgroundColor">#FFFFFF</item>
        <item name="atg_pressedBackgroundColor">#FF99B1</item>
    </style>

    <!-- 自适应宽高 -->
    <style name="wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="TagGroup_Hot" parent="TagGroup">
        <item name="atg_borderColor">#31ca94</item>
        <item name="atg_textColor">#31ca94</item>
        <item name="atg_backgroundColor">#FFFFFFFF</item>
        <item name="atg_borderStrokeWidth">0.5dp</item>
        <item name="atg_textSize">13sp</item>
        <item name="atg_horizontalSpacing">8dp</item>
        <item name="atg_verticalSpacing">8dp</item>
        <item name="atg_horizontalPadding">12dp</item>
        <item name="atg_verticalPadding">3dp</item>
    </style>

    <style name="TagGroup_Recommend" parent="TagGroup">
        <item name="atg_borderColor">#EDEDED</item>
        <item name="atg_textColor">#676773</item>
        <item name="atg_backgroundColor">#FFFFFFFF</item>
        <item name="atg_borderStrokeWidth">0.5dp</item>
        <item name="atg_textSize">13sp</item>
        <item name="atg_horizontalSpacing">8dp</item>
        <item name="atg_verticalSpacing">8dp</item>
        <item name="atg_horizontalPadding">12dp</item>
        <item name="atg_verticalPadding">3dp</item>
    </style>

    <style name="TagGroup_History" parent="TagGroup">
        <item name="atg_borderColor">#00000000</item>
        <item name="atg_textColor">#292933</item>
        <item name="atg_backgroundColor">#E5E5E5</item>
        <item name="atg_radius">16.5dp</item>
        <item name="atg_pressedBackgroundColor">@color/transparent</item>
        <item name="atg_borderStrokeWidth">0.5dp</item>
        <item name="atg_textSize">15sp</item>
        <item name="atg_horizontalSpacing">8dp</item>
        <item name="atg_verticalSpacing">8dp</item>
        <item name="atg_horizontalPadding">10dp</item>
        <item name="atg_verticalPadding">5dp</item>
    </style>
    <!--收银台 支付方式选择-->
    <style name="apply_refund_activiy_title">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="refund_detail_activiy_title">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">35dp</item>
        <item name="android:minHeight">35dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="payway_activiy_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">42dp</item>
        <item name="android:paddingLeft">28dp</item>
        <item name="android:layout_marginTop">0.5dp</item>
        <item name="android:paddingRight">28dp</item>
        <item name="android:drawablePadding">6dp</item>
        <item name="android:drawableRight">@drawable/payway_bg_selector</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">15sp</item>
        <item name="android:background">@color/white</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="refund_optimize_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:layout_marginTop">0.5dp</item>
        <item name="android:paddingRight">18dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">15sp</item>
        <item name="android:background">@color/white</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="tab">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:padding">10dp</item>
        <item name="android:text">请选择</item>
        <item name="android:textColor">@color/selector_text_color_tab</item>
        <item name="android:textSize">13sp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="bottom_dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation_Bottom_Dialog</item>
    </style>

    <style name="Animation_Bottom_Dialog">
        <item name="android:windowEnterAnimation">@anim/bottom_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/bottom_dialog_exit</item>
    </style>

    <style name="AD_Dialog">
        <item name="android:windowEnterAnimation">@anim/ad_dialog_anim</item>
        <item name="android:windowExitAnimation">@anim/fade_out_center</item>
    </style>

    <style name="payment_item_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="payment_item_layout_pop">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="order_detail_item_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">38.5dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="payment_item_layout_text">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="payment_item_layout_text_left">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14sp</item>
        <item name="android:drawablePadding">1dp</item>
        <item name="android:drawableRight">@drawable/common_more</item>
    </style>

    <style name="payment_item_order_text_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingTop">6dp</item>
    </style>

    <style name="payment_item_order_text_layout_pop">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_15</item>
    </style>

    <style name="payment_item_order_ll_key">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="payment_item_order_text_key3" parent="payment_item_order_text_key2">
        <item name="android:textColor">@color/color_676773</item>
    </style>

    <style name="payment_item_order_text_key2" parent="payment_item_order_text_key">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="payment_item_order_text_key">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="payment_item_order_text_key_pop">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
    </style>

    <style name="payment_item_order_text_value4" parent="payment_item_order_text_value3">
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="payment_item_order_text_value3">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/color_676773</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="payment_item_order_text_value">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">16sp</item>
    </style>
    <!--是否返点列表样式-->
    <style name="payment_item_order_text_rebate">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="payment_item_order_text_value2" parent="payment_item_order_text_value">
        <item name="android:textColor">@color/text_9494A6</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
    </style>

    <style name="more_text_layout_style" parent="more_text_layout_style2">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_weight">1</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="more_image_msg_style">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
    </style>

    <style name="more_msg_tip_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:background">@drawable/bg_message_num</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">10sp</item>
    </style>
    <!--黑色字体的14号字-->
    <style name="more_text_black_size_14">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#292933</item>
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">center</item>
    </style>
    <!--灰色字体的12号字-->
    <style name="more_text_gray_size_12">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">12sp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="more_text_layout_style2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerInParent">true</item>
    </style>

    <style name="MoreTextView2" parent="MoreTextView">
        <item name="android:layout_marginTop">@dimen/more_top_01</item>
    </style>

    <style name="more_image_layout_style">
        <item name="android:layout_width">30dp</item>
        <item name="android:layout_height">30dp</item>
    </style>

    <style name="more_ll_layout_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="more_ll_layout_style2" parent="more_ll_layout_style">
        <item name="android:background">@null</item>
    </style>


    <style name="cart_layout_style">
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:scaleType">fitXY</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="cart_layout_style2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">bottom|right</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/shop_tv4</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="layout_pay_type_tips">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:text"></item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:textColor">#FF292933</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="layout_pay_type_cb">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:button">@drawable/payway_bg_selector</item>
        <item name="android:checked">false</item>
        <item name="android:clickable">false</item>
    </style>

    <style name="dialog_bottom_sheet_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">47dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="dialog_bottom_sheet_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/list_line_bg</item>
    </style>

    <!--dialog的显示样式-->
    <style name="dialog_confirm_style" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="command_dialog2_style">
        <item name="android:layout_marginTop">27dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">@color/text_plan_01</item>
        <item name="android:textSize">@dimen/text_plan_01</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="command_dialog2_style2" parent="command_dialog2_style">
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:textColor">@color/text_plan_02</item>
        <item name="android:textSize">@dimen/text_plan_02</item>
    </style>

    <style name="header_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/header_height</item>
        <item name="android:background">@drawable/base_header_default_bg</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="header_white_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="header_layout_left">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:minWidth">44dp</item>
        <item name="android:maxWidth">90dp</item>
        <item name="android:paddingRight">6dp</item>
    </style>

    <style name="header_layout_right_text" parent="header_layout_left">
        <item name="android:paddingRight">14dp</item>
        <item name="android:paddingLeft">0dp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14dp</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="header_layout_right_img" parent="header_layout_right_text">
        <item name="android:paddingRight">8dp</item>
    </style>

    <style name="header_layout_mid">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginLeft">90dp</item>
        <item name="android:layout_marginRight">90dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">17dp</item>
    </style>

    <!--购物车item优惠图片样式-->
    <style name="cart_item_layout_style">
        <item name="android:layout_width">13dp</item>
        <item name="android:layout_height">13dp</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginRight">5dp</item>
        <item name="android:visibility">visible</item>
    </style>

    <style name="cart_item_tv_discount">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">3dp</item>
        <item name="android:background">@drawable/bg_discount_cart</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">@dimen/dimen_dp_4</item>
        <item name="android:visibility">gone</item>
        <item name="android:paddingTop">@dimen/dimen_dp_1</item>
        <item name="android:paddingRight">@dimen/dimen_dp_4</item>
        <item name="android:paddingBottom">@dimen/dimen_dp_1</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/dimen_dp_10</item>
    </style>

    <!--login页面editView输入样式-->
    <style name="login_edit_view_layout">
        <item name="android:textColor">@color/login_base_et_hint</item>
        <item name="android:textColorHint">@color/login_base_et_hint</item>
        <item name="android:textSize">12sp</item>
    </style>
    <!--register页面editView输入样式-->
    <style name="register_edit_view_layout">
        <item name="android:textColor">@color/register_hint_tv</item>
        <item name="android:textColorHint">@color/register_hint_tv</item>
        <item name="android:textSize">12sp</item>
    </style>
    <!--登录-editText-->
    <style name="edit_text_common_parent_layout">
        <item name="android:background">@null</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:textColor">@color/login_base_et</item>
        <item name="android:textColorHint">@color/login_base_et_hint</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">17sp</item>
    </style>

    <style name="edit_text_common_layout" parent="edit_text_common_parent_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    <!--注册-editText-->
    <style name="edit_text_common_register_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@null</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:textColor">@color/register_tv</item>
        <item name="android:textColorHint">@color/register_hint_tv</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">17sp</item>
    </style>

    <style name="edit_text_mobile_common_layout" parent="edit_text_common_layout">
        <item name="android:maxLength">11</item>
        <item name="android:phoneNumber">true</item>
    </style>
    <!--登录注册-input-->
    <style name="text_input_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style>

    <style name="common_base_ll_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_marginLeft">20dp</item>
        <item name="android:layout_marginRight">20dp</item>
        <item name="android:layout_marginTop">23dp</item>
        <item name="android:background">@drawable/bg_login_editview</item>
        <item name="android:divider">@drawable/divider_line_base_1px</item>
        <item name="android:orientation">vertical</item>
        <item name="android:showDividers">middle</item>
    </style>

    <!--登录注册-btn-->
    <style name="common_base_btn_layout" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:layout_marginLeft">20dp</item>
        <item name="android:layout_marginRight">20dp</item>
        <item name="android:background">@drawable/round_corner_bg_new</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>

    </style>

    <style name="payment_iv_product">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:padding">5dp</item>
        <item name="android:visibility">invisible</item>
        <item name="android:background">@drawable/base_image_bg</item>
        <item name="android:layout_weight">1</item>
    </style>

    <style name="payment_product">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_weight">1</item>
    </style>

    <style name="payment_address_tv">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="order_detail_item_text_right">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="order_detail_item_time">
        <item name="android:layout_width">match_parent</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:layout_height">38.5dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:textSize">13sp</item>
    </style>

    <!--商品详情通用textview样式-->
    <style name="commodity_tv_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/tv_detail_color_333</item>
        <item name="android:textSize">@dimen/detail_tv_dimen_13sp</item>
    </style>

    <style name="commodity_tv_style_02" parent="commodity_tv_style">
        <item name="android:layout_width">@dimen/detail_tv_wrap_91dp</item>
        <item name="android:textColor">@color/detail_tv_color_9494A6</item>
    </style>

    <style name="commodity_tv_style_03" parent="commodity_tv_style">
        <item name="android:layout_width">@dimen/detail_tv_wrap_51dp</item>
        <item name="android:textColor">@color/color_292933</item>
    </style>

    <style name="commodity_tv_style_04" parent="commodity_tv_style">
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="commodity_tv_style_05" parent="commodity_tv_style_04">
        <item name="android:layout_width">@dimen/detail_tv_wrap_74dp</item>
    </style>

    <style name="refund_detail_title">
        <item name="android:textStyle">bold</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">18sp</item>
    </style>
    <!--商品详情-小点-->
    <style name="detail_service_spot_layout">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:src">@drawable/detail_actionbar</item>
    </style>

    <!--电子计划单-扫描补货登记-->
    <style name="replenishment_tv_style">
        <item name="android:textColorHint">@color/text_replenishment_aaa</item>
        <item name="android:textSize">@dimen/text_replenishment_12sp</item>
    </style>

    <style name="replenishment_et_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@null</item>
        <item name="android:textColor">@color/text_replenishment_333</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">@dimen/text_replenishment_16sp</item>
    </style>

    <style name="dynamic_layout_text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_292933</item>
    </style>

    <style name="about_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/common_item_h</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="about_layout_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:drawablePadding">12dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">@dimen/common_empty_tv</item>
    </style>

    <style name="about_layout_iv">
        <item name="android:layout_width">15dp</item>
        <item name="android:layout_height">15dp</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:src">@drawable/right</item>
    </style>

    <style name="address_title_style">
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/color_292933</item>
    </style>

    <style name="address_layout_style">
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
    </style>

    <style name="address_input_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textColorHint">@color/text_9494A6</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="address_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:background">@color/color_F5F5F5</item>
    </style>

    <style name="checkboxText">
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/product_rb_category_selector</item>
        <item name="android:textColor">@color/selector_checkbox_text_color</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
    </style>

    <style name="home_seckill_style">
        <item name="android:background">@drawable/home_seckill_bg</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">22dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">13sp</item>
        <item name="android:layout_height">22dp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
    </style>

    <style name="home_seckill_style_02">
        <item name="android:background">@drawable/home_seckill_bg</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">20dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">13sp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
    </style>

    <style name="home_seckill_point_02">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">#FF272727</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="home_seckill_point">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">#FF272727</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <!--选择优惠券页面btn-->
    <style name="voucher_available_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:textSize">16sp</item>

    </style>

    <style name="TabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">15sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="OrderListTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">15sp</item>
    </style>

    <!--发票-->
    <style name="invoice_information_ll_style">
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_marginTop">15dp</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="invoice_information_ll2_style">
        <item name="android:background">@drawable/icon_bg_title_invoice_information</item>
        <item name="android:paddingLeft">17dp</item>
        <item name="android:paddingRight">17dp</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="invoice_information_tv_style" parent="invoice_base_01">
        <item name="android:drawablePadding">5dp</item>
    </style>

    <style name="invoice_information_ll3_style">
        <item name="android:background">@drawable/icon_bg_invoice_information</item>
        <item name="android:orientation">vertical</item>
        <item name="android:paddingLeft">17dp</item>
        <item name="android:paddingRight">17dp</item>
        <item name="android:paddingTop">13dp</item>
    </style>

    <style name="invoice_information_tv2_style">
        <item name="android:gravity">center</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="invoice_information_tv3_style">
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="invoice_list_tv_style">
        <item name="android:layout_marginBottom">6dp</item>
        <item name="android:layout_marginTop">6dp</item>
        <item name="android:textColor">#676773</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="invoice_base_01">
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">#292933</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="invoice_list_tv2_style" parent="invoice_base_01">

    </style>

    <style name="invoice_list_tv3_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:textColor">@color/invoice_tv_292933</item>
        <item name="android:singleLine">true</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="invoice_list_tv4_style">
        <item name="android:minWidth">85dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/invoice_tv_292933</item>
        <item name="android:singleLine">true</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="drug_list_tv_01">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/product_category_selector_01</item>
        <item name="android:textSize">@dimen/brand_rb</item>
    </style>

    <style name="drug_list_tv_02" parent="drug_list_tv_01">
        <item name="android:drawablePadding">-5dp</item>
        <item name="android:drawableRight">@drawable/manufacturers_tab_selector</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingRight">5dp</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="drug_list_tv_03">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@drawable/product_rb_selector_textcolor</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/brand_rb</item>
    </style>

    <style name="simple_filter_btn_item">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@drawable/filter_item_btn_textcolor</item>
        <item name="android:background">@drawable/filter_btn_bg_selector</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/brand_rb</item>
    </style>

    <style name="simple_filter_btn_item2">
        <item name="android:layout_width">54dp</item>
        <item name="android:layout_height">24dp</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@drawable/filter_item_btn_textcolor2</item>
        <item name="android:background">@drawable/filter_btn_bg_selector3</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="simple_filter_btn_item3">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_dp_25</item>
        <item name="android:minWidth">@dimen/dimen_dp_50</item>
        <item name="android:button">@null</item>
        <item name="android:textColor">@drawable/filter_item_btn_textcolor2</item>
        <item name="android:background">@drawable/filter_btn_bg_selector3</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:paddingStart">@dimen/dimen_dp_5</item>
        <item name="android:paddingEnd">@dimen/dimen_dp_5</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_9</item>
    </style>


    <style name="drug_list_tv_04" parent="drug_list_tv_03">
        <item name="android:drawablePadding">-15dp</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="product_category_selecter">
        <item name="android:drawablePadding">2dp</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@drawable/product_rb_selector_textcolor</item>
        <item name="android:textSize">@dimen/brand_rb_01</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
    </style>

    <style name="drug_list_tv_05">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="android:layout_marginLeft">18dp</item>
        <item name="android:layout_marginRight">22dp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:drawablePadding">-5dp</item>
        <item name="android:drawableRight">@drawable/manufacturers_tab_selector</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingRight">5dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/product_category_selector_01</item>
        <item name="android:textSize">@dimen/brand_rb</item>
    </style>

    <style name="drug_list_tv_06" parent="drug_list_tv_05">
        <item name="android:background">@color/white</item>
        <item name="android:textColor">@drawable/product_rb_selector_textcolor</item>
        <item name="android:layout_marginLeft">6dp</item>
        <item name="android:layout_marginRight">6dp</item>
    </style>

    <style name="filtrate_classify_pop_base_tv">
        <item name="android:background">@drawable/selector_brand_pop_window_tv2</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@drawable/product_rb_selector_textcolor</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="filtrate_classify_pop_tv" parent="filtrate_classify_pop_base_tv">
        <item name="android:layout_width">78dp</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_marginTop">19dp</item>
        <item name="android:layout_marginBottom">19dp</item>
    </style>

    <style name="filtrate_classify_pop_tv2" parent="filtrate_classify_pop_tv">
        <item name="android:background">@drawable/selector_brand_pop_window_tv3</item>
    </style>

    <style name="filtrate_classify_pop_tv3" parent="filtrate_classify_pop_tv">
        <item name="android:background">@drawable/selector_brand_pop_window_tv3</item>
        <item name="android:layout_marginBottom">@dimen/dimen_dp_0</item>
    </style>

    <style name="filtrate_classify_pop_tv_01" parent="filtrate_classify_pop_base_tv">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">33dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginTop">19dp</item>
        <item name="android:layout_marginBottom">5dp</item>
    </style>

    <style name="filtrate_classify_pop_tv_06" parent="filtrate_classify_pop_base_tv">
        <item name="android:layout_width">66dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:button">@null</item>
    </style>

    <style name="filtrate_classify_pop_tv_05" parent="filtrate_classify_pop_base_tv">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">33dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginBottom">19dp</item>
    </style>

    <style name="filtrate_classify_pop_tv_02">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">26dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">@dimen/filtrate_classify_padding_left</item>
        <item name="android:textColor">@color/filtrate_classify_tv01</item>
        <item name="android:textSize">@dimen/filtrate_classify_tv01</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="filtrate_classify_pop_tv_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_dp_40</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="filtrate_classify_pop_fl">
        <item name="android:layout_weight">1</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">32.6dp</item>
        <item name="android:layout_marginBottom">13dp</item>
        <item name="android:layout_marginTop">13dp</item>
        <item name="android:background">@drawable/bg_brand_available_promotion_normal</item>
    </style>

    <style name="filtrate_classify_pop_et">
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:background">@null</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">4</item>
        <item name="android:numeric">integer</item>
        <item name="android:textColor">@color/filtrate_classify_tv01</item>
        <item name="android:textColorHint">#c0c0c0</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="filtrate_classify_pop_tv_03">
        <item name="android:textStyle">bold</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/filtrate_classify_tv01</item>
        <item name="android:textSize">@dimen/filtrate_classify_tv01</item>
    </style>

    <style name="filtrate_classify_pop_ll_01">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:paddingLeft">9dp</item>
        <item name="android:paddingRight">9dp</item>
        <item name="android:visibility">gone</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="filtrate_classify_pop_tv_04">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawableRight">@drawable/icon_delete_x</item>
        <item name="android:drawablePadding">3dp</item>
        <item name="android:background">@drawable/bg_brand_available_promotion_normal</item>
        <item name="android:ellipsize">end</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:paddingBottom">6dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="filtrate_classify_pop_btn">
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="brand_item_kxj_lsj_base">
        <item name="android:layout_centerVertical">true</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">1.5dp</item>
        <item name="android:paddingRight">1.5dp</item>
        <item name="android:textSize">10dp</item>
    </style>

    <style name="detail_coupon_base">
        <item name="android:visibility">gone</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:paddingLeft">6dp</item>
        <item name="android:paddingRight">6dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">11sp</item>
    </style>

    <style name="detail_iv_base">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:src">@drawable/icon_detail_right_b</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="detail_promotion_base">
        <item name="android:visibility">gone</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="detail_promotion_tv">
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_brand_item_type2</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">11sp</item>
    </style>

    <style name="detail_promotion_tv2">
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_brand_item_type5</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:textColor">@color/color_FF2121</item>
        <item name="android:textSize">11sp</item>
    </style>

    <style name="detail_promotion_iv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/transparent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:scaleType">fitXY</item>
    </style>

    <style name="detail_promotion_tv02">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="refund_rebate_text_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">12sp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    <!-- 待确认订单商品明细  入库价样式 -->
    <style name="confirm_order_product_detail" parent="refund_rebate_text_style">
        <item name="android:paddingLeft">6dp</item>
        <item name="android:gravity">center</item>
    </style>
    <!-- 待确认订单商品明细  入库价分割样式 -->
    <style name="confirm_order_product_detail_space">
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="confirm_order_product_price_text_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:paddingLeft">6dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="order_product_price_text_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:paddingLeft">6dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="view_line_icon">
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@drawable/divider_line_w_1px_9494a6</item>
        <item name="android:layout_height">0.33dp</item>
        <item name="android:layout_width">36dp</item>
    </style>

    <style name="plan_tab_line">
        <item name="android:layout_width">34dp</item>
        <item name="android:background">@drawable/bg_base_tab_layout</item>
        <item name="android:layout_gravity">center_horizontal|bottom</item>
        <item name="android:layout_height">4dp</item>
    </style>

    <style name="coupon_member_tab_line">
        <item name="android:layout_width">34dp</item>
        <item name="android:button">@null</item>
        <item name="android:background">@drawable/bg_coupon_member_tab_line</item>
        <item name="android:layout_gravity">center_horizontal|bottom</item>
        <item name="android:layout_height">4dp</item>
    </style>

    <style name="order_price_ll">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="order_price_optioin">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <!-- 商品明细 -->
    <style name="confirm_order_price_optioin">
        <item name="android:layout_width">20dp</item>
        <item name="android:textColor">@color/text_676773</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="activity_price">
        <item name="android:layout_gravity">bottom</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">9sp</item>
        <item name="android:layout_marginLeft">28dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">16dp</item>
    </style>

    <style name="goods_list_item_text_small">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/text_9494A6</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="RoundImage">
        <item name="android:layout_width">84dp</item>
        <item name="android:scaleType">centerCrop</item>
        <item name="android:layout_height">84dp</item>
    </style>

    <style name="Dialog_Empty" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="dialogWindowAnim" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit_anim</item>
    </style>

    <style name="TabLayoutTextStyle" parent="TextAppearance.AppCompat">
        <item name="android:textSize">15sp</item>
    </style>

    <style name="Dialog_PrivacyBtnStyle" parent="TextAppearance.AppCompat">
        <item name="android:textSize">16dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:layout_marginTop">26dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#676773</item>
    </style>

    <!--短信邀请页面页面editView输入样式-->
    <style name="sms_invitation_edit_view_layout">
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textColorHint">@color/text_9494A6</item>
        <item name="android:textSize">15sp</item>
    </style>

    <!-- 设置错误提示文字样式 -->
    <style name="errorAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">#FF2D2D</item>
    </style>

    <style name="sms_invitation_edit_layout">
        <item name="android:maxLines">1</item>
        <item name="android:maxLength">11</item>
        <item name="android:textColor">@color/login_base_et</item>
        <item name="android:textColorHint">@color/login_base_et_hint</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="MyEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/color_F5F5F5</item>
        <item name="colorControlActivated">@color/color_F5F5F5</item>
    </style>

    <style name="style_goods_error_correcttion">
        <item name="android:textColor">#ff292933</item>
        <item name="android:textSize">@dimen/detail_tv_dimen_14sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawableRight">@drawable/ic_arrow</item>
        <item name="android:paddingLeft">@dimen/dp_10</item>
        <item name="android:paddingRight">@dimen/dp_10</item>
        <item name="android:paddingTop">@dimen/dimen_dp_14</item>
        <item name="android:paddingBottom">@dimen/dimen_dp_14</item>
    </style>

    <style name="main_menu_photo_anim">
        <item name="android:windowEnterAnimation">@anim/photo_dialog_in_anim</item>
        <item name="android:windowExitAnimation">@anim/photo_dialog_out_anim</item>
    </style>

    <style name="SnapshotDetailTvTitleStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">#ff292933</item>
    </style>

    <style name="SnapshotDetailTvContentStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">#ff292933</item>
    </style>
    <!--分割线的样式-->
    <style name="cut_off_line_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/ling_bg</item>
    </style>

    <style name="normal_horizontal_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/color_divider_title_bottom</item>
    </style>

    <style name="alterpassword_layout_style">
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:background">@color/white</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
    </style>

    <style name="alterpassword_title_style">
        <item name="android:layout_width">95dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/color_292933</item>
    </style>

    <style name="alterpassword_input_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textColorHint">@color/colors_9595A6</item>
        <item name="android:textCursorDrawable">@drawable/color_cursor</item>
        <item name="android:textSize">15sp</item>
    </style>


    <!--登录-->
    <style name="inputLayoutLineColor" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/defaultTextColor</item>
        <item name="colorControlActivated">@color/focusTextColor</item>
    </style>

    <!--选择发票资质 核实专票Item-->
    <style name="select_invoice_license" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/transparent</item>
        <item name="colorControlActivated">@color/color_00b377</item>
    </style>

    <style name="inputLayoutHintAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/loginTextAppearance</item>
    </style>

    <style name="transparentInputLayoutLineColor" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/transparent</item>
        <item name="colorControlActivated">@color/transparent</item>
    </style>

    <style name="confirmButtonTextStyle">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="authorizationDetailTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_676773</item>
        <item name="android:layout_marginLeft">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_20</item>
    </style>

    <style name="authorizationDetailContent">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:layout_marginLeft">@dimen/dimen_dp_7</item>
    </style>

    <style name="agentOrderDetailTitle" parent="authorizationDetailTitle">
        <item name="android:layout_marginLeft">@dimen/dimen_dp_0</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_20</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
    </style>

    <style name="agentOrderDetailContent" parent="authorizationDetailContent" />

    <style name="radio_reject_reason">
        <item name="android:button">@null</item>
        <item name="android:padding">@dimen/dimen_dp_0</item>
        <item name="android:background">@null</item>
        <item name="android:drawableLeft">@drawable/selector_reject_reason_radio</item>
        <item name="android:drawablePadding">@dimen/dimen_dp_11</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_10</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
    </style>

    <style name="checkedTextView_pay_way_agree">
        <item name="android:button">@null</item>
        <item name="android:padding">@dimen/dimen_dp_0</item>
        <item name="android:background">@null</item>
        <item name="android:drawableLeft">@drawable/selector_payway_agree_checkedtv</item>
        <item name="android:drawablePadding">@dimen/dimen_dp_5</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_12</item>
    </style>

    <style name="search_hot_keyword">
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_292933</item>
    </style>

    <style name="medicineious_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/common_item_h</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="medicineious_layout_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:drawablePadding">12dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:textColor">@color/text_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_15</item>
    </style>

    <style name="medicineious_layout_iv">
        <item name="android:layout_width">15dp</item>
        <item name="android:layout_height">15dp</item>
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:src">@drawable/right</item>
    </style>

    <style name="shopping_guide_major_title">
        <item name="android:textSize">@dimen/dimen_dp_15</item>
        <item name="android:textColor">@color/color_30303c</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginEnd">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_12</item>
    </style>

    <style name="shopping_guide_minor_title">
        <item name="android:textSize">@dimen/dimen_dp_12</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginEnd">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_2</item>
    </style>

    <style name="shopping_guide_img">
        <item name="android:layout_height">@dimen/dimen_dp_55</item>
        <item name="android:layout_width">@dimen/dimen_dp_55</item>
        <item name="android:scaleType">center</item>
    </style>

    <style name="shopping_guide_tag">
        <item name="android:layout_height">@dimen/dimen_dp_43</item>
        <item name="android:layout_width">@dimen/dimen_dp_43</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_13</item>
        <item name="android:scaleType">center</item>
    </style>

    <style name="fast_entry_bg">
        <item name="android:layout_width">@dimen/dimen_dp_0</item>
        <item name="android:layout_height">@dimen/dimen_dp_120</item>
        <item name="layout_constraintHorizontal_weight">1</item>
        <item name="android:scaleType">centerCrop</item>
    </style>
    <!--小直播>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>-->
    <!-- Input dialog theme. -->
    <style name="InputDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="android:textColorHint">@color/c9</item>
        <item name="colorControlActivated">@color/colorAccent</item>
    </style>
    <!-- UGC Edit -->

    <style name="ConfirmDialogStyle" parent="NormalDialog">

        <!--<item name="windowMinWidthMinor">90%</item>-->


        <!--<item name="android:textColorPrimary">#000000</item>-->

        <!--<item name="colorAccent">#0accac</item>-->
    </style>

    <style name="NormalDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>

    </style>

    <style name="PlayerTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowAnimationStyle">@style/PlayerAnimation</item>
    </style>

    <style name="PlayerAnimation" parent="android:Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/anim_slice_in_right</item>
        <item name="android:activityCloseExitAnimation">@anim/anim_slice_out_right</item>
    </style>
    <!--小直播>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>-->

    <style name="InvoicePopText">
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_dp_40</item>
    </style>

    <style name="InvoicePopTitle">
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:textColor">@color/color_676773</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_dp_40</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_15</item>
    </style>

    <style name="ShopOpenAccountCompanyInfo">
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="ShopOpenAccountCompanyType">
        <item name="android:textColor">@color/color_676773</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="RejectRefundText">
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
    </style>
    
    <style name="setPayPwText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_15</item>
        <item name="android:textColor">#292933</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_13</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_15</item>
        <item name="layout_constraintStart_toStartOf">parent</item>
        <item name="singleLine">true</item>
    </style>

    <style name="setPayPwTextTips">
        <item name="android:layout_width">@dimen/dimen_dp_0</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_12</item>
        <item name="android:textColor">#FE2021</item>
        <item name="layout_constraintEnd_toEndOf">parent</item>
        <item name="android:layout_marginEnd">@dimen/dimen_dp_15</item>
        <item name="singleLine">true</item>
    </style>

    <style name="setPayPwEdit">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dimen_dp_44</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:textColor">#292933</item>
        <item name="hintTextColor">#9494A6</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_10</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_15</item>
        <item name="android:layout_marginEnd">@dimen/dimen_dp_15</item>
        <item name="android:background">@drawable/shape_set_pay_pw_bg</item>
        <item name="android:paddingStart">@dimen/dimen_dp_12</item>
        <item name="layout_constraintStart_toStartOf">parent</item>
    </style>

    <style name="inputPassword">
        <item name="android:layout_width">@dimen/dimen_dp_44</item>
        <item name="android:layout_height">@dimen/dimen_dp_44</item>
        <item name="android:background">@drawable/selector_input_password</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">@dimen/dimen_dp_24</item>
        <item name="android:textColor">#292933</item>
        <item name="android:maxLength">1</item>
        <item name="android:inputType">number</item>
        <item name="android:digits">0123456789</item>
        <item name="android:textCursorDrawable">@drawable/shape_input_view_cursor</item>
        <item name="android:cursorVisible">false</item>
    </style>

    <style name="OrderServiceLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dimen_dp_46</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="OrderServiceTitle">
        <item name="android:layout_width">@dimen/dimen_dp_90</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/color_333</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
    </style>

    <style name="OrderServiceContent">
        <item name="android:layout_width">@dimen/dimen_dp_0</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:gravity">end</item>
        <item name="android:textColor">@color/color_00b955</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:layout_marginStart">@dimen/dimen_dp_10</item>
        <item name="singleLine">true</item>
    </style>

    <style name="OrderServiceBtn">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/dimen_dp_22</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_11</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:paddingStart">@dimen/dimen_dp_5</item>
        <item name="android:paddingEnd">@dimen/dimen_dp_5</item>
        <item name="android:gravity">center_vertical</item>
        <item name="rv_cornerRadius">@dimen/dimen_dp_2</item>
        <item name="rv_strokeColor">#D1D1D6</item>
        <item name="rv_strokeWidth">@dimen/dimen_dp_1</item>
    </style>

    <style name="OrderServiceArrow">
        <item name="android:layout_width">@dimen/dimen_dp_15</item>
        <item name="android:layout_height">@dimen/dimen_dp_15</item>
        <item name="android:src">@drawable/icon_order_detail_arrow_right</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginEnd">@dimen/dimen_dp_5</item>
    </style>

    <style name="afterSalesItemTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/color_292933</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
    </style>

    <style name="orderActionButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_14</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingStart">@dimen/dimen_dp_8</item>
        <item name="android:paddingEnd">@dimen/dimen_dp_8</item>
        <item name="android:paddingTop">@dimen/dimen_dp_6</item>
        <item name="android:paddingBottom">@dimen/dimen_dp_6</item>
    </style>

    <style name="consultHistoryParent">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_10</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="consultHistoryTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_676773</item>
    </style>
    <style name="consultHistoryContent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_676773</item>
    </style>

    <style name="consultHistoryContentWithMarginTop">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/dimen_dp_13</item>
        <item name="android:textColor">@color/color_676773</item>
        <item name="android:layout_marginTop">@dimen/dimen_dp_10</item>
    </style>

    <style name="ShapeableImageView_10dp_rounded_corner">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <style name="ShapeableImageView_13dp_rounded_corner">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">13dp</item>
    </style>
</resources>
