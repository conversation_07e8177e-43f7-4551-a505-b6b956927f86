package com.ybm.app.bean;

public class NetError {
	public static final int GENERIC_ERROR = 1;
	public static final int HTTP_STATUS = -1;//网络请求发送正常，返回状态不是200
	public static final String NO_ERROR_MSG = "No Error";
	public static final int NO_NETWORK_ERROR = 2;//没有网络
	public static final int URL_ERROR = 3;//请求地址异常
	public static final int SERVICE_ERROR = 4;//500
	public static final int TIME_OUT_ERROR = 5;//请求超时
	public static final int REQUSET_ERROR = 6;//请求创建错误
	public static final int CONNECT_ERROR = 7;//建立连接错误
	public static final int UNKNOWNHOST_ERROR = 8;//域名解析错误
	public static final int PROTOCOL_ERROR = 9;//协议错误
	public int errorCode;
	public int statusCode;
	public String message;

	public NetError(int errorCode,int statusCode,  String errorMessage) {
		this.errorCode = errorCode;
		this.statusCode = statusCode;
		this.message = errorMessage;
	}


	public  static  NetError newNoNetwork(){
		return  new NetError(NO_NETWORK_ERROR,-1,"没有打开网络");
	}
}