package com.ybm.app.common

import android.util.Log
import com.ybm.app.utils.SpUtil
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Response

/**
 * <AUTHOR>
 * @date 2022/5/13
 * @description
 */
class RequestParamsInterceptor: Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        if (request.body is FormBody) {
            val builder = FormBody.Builder()
            val oldBody = request.body as FormBody
            var isContains = false
            for (i in 0 until oldBody.size) {
                builder.add(oldBody.name(i), oldBody.value(i))
                if (oldBody.name(i) == "merchantId") isContains = true
            }
            if (!isContains){
                builder.add("merchantId", SpUtil.readString("MERCHANTID", ""))
            }
            request = request.newBuilder().post(builder.build()).build()
        }
        return chain.proceed(request)
    }
}