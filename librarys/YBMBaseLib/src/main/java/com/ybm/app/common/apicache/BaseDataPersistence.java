package com.ybm.app.common.apicache;

import java.util.Map;
import java.util.Set;

/**
 * 基本接口
 */

public abstract class BaseDataPersistence<T> implements IDataPersistence {
    private IDataEncrypt encrypt;

    public BaseDataPersistence(IDataEncrypt encrypt) {
        if (encrypt == null) {
            throw new IllegalArgumentException("加密方式不能为空");
        }
        this.encrypt = encrypt;
    }

    /**
     * 获取缓存
     *
     * @param key
     * @return 加密后的String，没有缓存返回null
     */
    protected abstract String getCache4Iml(String key);

    /**
     * 添加缓存
     *
     * @param key
     * @param value 加密后的String
     * @return 成功失败
     */
    protected abstract boolean putCache4Iml(String key, String value);

    //一组批量添加数据的模版方法

    /**
     * 开始批量添加，一般开始事务，并返回SQLiteDatabase
     *
     * @return
     */
    protected abstract T putCachesStart();

    /**
     * 添加到事务中
     *
     * @param key
     * @param value
     * @param t
     */
    protected abstract void appendCache(String key, String value, T t);

    /**
     * 结束事务并提交任务
     *
     * @param t
     */
    protected abstract void putCachesEnd(T t);


    @Override
    public final String getCache(String key) {
        if (key == null) {
            return "";
        }
        return encrypt.decode(key, getCache4Iml(key));
    }

    @Override
    public final void putCache(String key, String value) {
        if (key == null) {
            return;
        }
        putCache4Iml(key, encrypt.encode(key, value));
    }

    @Override
    public void putCaches(Map<String, String> maps) {
        if (maps == null || maps.isEmpty()) {
            return;
        }
        T t = putCachesStart();
        Set<String> keys = maps.keySet();
        for (String key : keys) {
            appendCache(key, encrypt.encode(key, maps.get(key)), t);
        }
        putCachesEnd(t);
    }

    @Override
    public void deleteCache(String key) {
        if (key == null) {
            return;
        }
    }

}
