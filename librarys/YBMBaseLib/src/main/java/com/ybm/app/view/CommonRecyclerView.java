//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.ybm.app.view;

import android.annotation.SuppressLint;
import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ItemDecoration;
import androidx.recyclerview.widget.RecyclerView.LayoutManager;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter.RequestLoadMoreListener;
import com.ybm.app.R.id;
import com.ybm.app.R.layout;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;

public class CommonRecyclerView extends RecyclerRefreshLayout {
    private RecyclerView rv_list;
    private LayoutManager mLayoutManager;
    private BaseQuickAdapter adapter;
    private CommonRecyclerView.Listener mOnRefreshListener;
    private boolean showAutoRefresh;
    private boolean isFirstRefresh;
    private CommonRecyclerView.OnScrollListener mScrollListener;

    public CommonRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.showAutoRefresh = true;
        this.isFirstRefresh = false;
        this.initView();
    }

    public CommonRecyclerView(Context context) {
        this(context, (AttributeSet) null);
    }

    private void initView() {
        this.rv_list = (RecyclerView) LayoutInflater.from(this.getContext()).inflate(layout.list_view, (ViewGroup) null);
        this.addView(this.rv_list);
        this.setEnabled(true);
        this.mLayoutManager = new WrapLinearLayoutManager(this.getContext());
        this.rv_list.setLayoutManager(this.mLayoutManager);
        this.post(new Runnable() {
            public void run() {
                if (CommonRecyclerView.this != null) {
                    if (CommonRecyclerView.this.showAutoRefresh) {
                        CommonRecyclerView.this.setRefreshing(true);
                        if (CommonRecyclerView.this.mOnRefreshListener != null) {
                            CommonRecyclerView.this.mOnRefreshListener.onRefresh();
                        }
                    }

                }
            }
        });
        this.rv_list.addOnScrollListener(new androidx.recyclerview.widget.RecyclerView.OnScrollListener() {
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (CommonRecyclerView.this.mScrollListener != null) {
                    CommonRecyclerView.this.mScrollListener.onScrollState(newState);
                }
            }

            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                int scrollY = CommonRecyclerView.this.rv_list.computeVerticalScrollOffset();
                if (CommonRecyclerView.this.mScrollListener != null) {
                    CommonRecyclerView.this.mScrollListener.onScrollRollingDistance(scrollY, dy);
                }

            }
        });
    }

    public void setListener(CommonRecyclerView.Listener listener) {

        this.mOnRefreshListener = listener;

        // 解决该rv设置adapter之后，再设置listenter不生效的问题
        this.setOnRefreshListener(new OnRefreshListener() {
            public void onRefresh() {
                if (CommonRecyclerView.this.mOnRefreshListener != null) {
                    CommonRecyclerView.this.mOnRefreshListener.onRefresh();
                }

            }
        });
        if (adapter!=null && mOnRefreshListener != null) {
            adapter.setOnLoadMoreListener(new RequestLoadMoreListener() {
                public void onLoadMoreRequested() {
                    CommonRecyclerView.this.mOnRefreshListener.onLoadMore();
                }
            });
        }
    }

    public void setAdapter(BaseQuickAdapter adapter, LayoutManager layoutManager) {
        if (!(layoutManager instanceof WrapLinearLayoutManager) && !(layoutManager instanceof WrapGridLayoutManager) && BaseYBMApp.getApp().isDebug()) {
            BugUtil.sendBug(new NullPointerException("LayoutManager 设置错误,请设置成 WrapLinearLayoutManager 或者 WrapGridLayoutManager"));
        }

        this.mLayoutManager = layoutManager;
        this.setAdapter(adapter);
    }

    public void setAdapter(BaseQuickAdapter adapter) {
        if (adapter != null) {
            this.adapter = adapter;
            if (this.mLayoutManager == null) {
                this.mLayoutManager = new WrapLinearLayoutManager(this.getContext());
            }

            this.rv_list.setLayoutManager(this.mLayoutManager);
            this.rv_list.setAdapter(adapter);
            this.setOnRefreshListener(new OnRefreshListener() {
                public void onRefresh() {
                    if (CommonRecyclerView.this.mOnRefreshListener != null) {
                        CommonRecyclerView.this.mOnRefreshListener.onRefresh();
                    }

                }
            });
            if (mOnRefreshListener != null) {
                adapter.setOnLoadMoreListener(new RequestLoadMoreListener() {
                    public void onLoadMoreRequested() {
                        CommonRecyclerView.this.mOnRefreshListener.onLoadMore();
                    }
                });
            }
        }
    }

    @Deprecated
    @SuppressLint("WrongConstant")
    public void setEmptyView(View emptyView) {
        if (this.adapter != null && emptyView != null) {
            emptyView.setVisibility(8);
            this.adapter.setEmptyView(emptyView);
        }

    }

    @Deprecated
    public void setEmptyView(int layoutId) {
        this.setEmptyView(LayoutInflater.from(this.getContext()).inflate(layoutId, this, false));
    }

    @Deprecated
    public void setEmptyView(int layoutId, int resId, String text) {
        if (layoutId > 0) {
            try {
                View e = View.inflate(getContext(), layoutId, null);
                if (e == null) {
                    return;
                }
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                e.setLayoutParams(layoutParams);

                ImageView imageView = (ImageView) e.findViewById(id.iv);
                if (imageView != null) {
                    imageView.setImageResource(resId);
                }

                if (text != null) {
                    TextView textView = (TextView) e.findViewById(id.tv);
                    if (textView != null) {
                        textView.setText(text);
                    }
                }

                this.setEmptyView(e);
            } catch (Throwable var7) {
                LogUtils.e(var7);
            }

        }
    }

    public void setLayoutManager(LayoutManager layoutManager) {
        if (!(layoutManager instanceof WrapLinearLayoutManager) && !(layoutManager instanceof WrapGridLayoutManager) && BaseYBMApp.getApp().isDebug()) {
            BugUtil.sendBug(new NullPointerException("LayoutManager 设置错误,请设置成 WrapLinearLayoutManager 或者 WrapGridLayoutManager"));
            LogUtils.e("LayoutManager 设置错误,请设置成 WrapLinearLayoutManager 或者 WrapGridLayoutManager");
        }

        this.mLayoutManager = layoutManager;
        if (this.rv_list != null) {
            this.rv_list.setLayoutManager(this.mLayoutManager);
        }

    }

    public LayoutManager getLayoutManager() {
        return this.mLayoutManager;
    }

    public RecyclerView getRecyclerView() {
        return this.rv_list;
    }

    public void setRefreshing(boolean refreshing) {
        if (!refreshing && !this.isFirstRefresh) {
            this.isFirstRefresh = true;
            if (this.adapter != null && this.adapter.getEmptyView() != null) {
                this.adapter.getEmptyView().setVisibility(View.VISIBLE);
            }
        }

        super.setRefreshing(refreshing);
    }

    public void setRefreshEnable(boolean enable) {
        this.setEnabled(enable);
    }

    public void setLoadMoreEnable(boolean enable) {
        if (this.adapter != null) {
            this.adapter.setEnableLoadMore(enable);
        }
    }

    public void setShowAutoRefresh(boolean autoRefresh) {
        setEnabled(autoRefresh);
        this.showAutoRefresh = autoRefresh;
    }

    public void addItemDecoration(ItemDecoration decor) {
        this.rv_list.addItemDecoration(decor);
    }

    public void addItemDecoration(ItemDecoration decor, int index) {
        this.rv_list.addItemDecoration(decor, index);
    }

    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
    }

    public void setOnScrollListener(CommonRecyclerView.OnScrollListener onScrollListener) {
        this.mScrollListener = onScrollListener;
    }

    public interface OnScrollListener {
        void onScrollChanged(int var1, int var2);

        void onScrollRollingDistance(int var1, int dy);

        void onScrollState(int state);
    }

    public interface Listener {
        void onRefresh();

        void onLoadMore();
    }
}
