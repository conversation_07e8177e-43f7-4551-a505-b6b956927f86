package com.ybm.app.view.refresh;

import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.R;

/**
 * the default implementation class of the interface IRefreshStatus, and the class should always be rewritten
 */
public class RefreshView extends LinearLayout implements IRefreshStatus {
    private boolean mHasTriggeredRefresh;
    private ValueAnimator mRotateAnimatorStart;
    private ValueAnimator mRotateAnimatorRefresh;
    private ImageView imageView;
    private TextView tv;
    private ValueAnimator.AnimatorUpdateListener updateListener;

    public RefreshView(Context context) {
        this(context, null);
    }

    public RefreshView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RefreshView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER);
        initData();
    }

    private int oldPosition;

    private AnimationMan loadStart;

    private void initData() {
        View.inflate(getContext(), R.layout.base_refresh_loading, this);
        imageView = (ImageView) findViewById(R.id.iv_refresh);
        loadStart = new AnimationMan(imageView, R.drawable.ybmlib_load_start);
        loadEnd = new AnimationMan(imageView, R.drawable.ybmlib_load_end);
        loading = new AnimationMan(imageView, R.drawable.ybmlib_loading);

    }

    private AnimationMan loading;
    private AnimationMan loadEnd;

    private void startAnimator() {
        if (loadEnd != null) {
            loadEnd.setOneShot(false);
            loadEnd.setListener(new AnimationMan.Listener() {
                @Override
                public void onStart() {


                }

                @Override
                public void onStop() {
                    //执行
                    recyclerRefreshLayout.setRefreshingMyself(refreshing);

                }

                @Override
                public void onRepeat() {

                }
            });
        }

        if (loading != null) {
            loading.setListener(new AnimationMan.Listener() {
                @Override
                public void onStart() {

                }

                @Override
                public void onStop() {
                    if (loadEnd != null) {
                        loadEnd.start();
                    }
                }

                @Override
                public void onRepeat() {

                }
            });
            loading.start();
        }


    }


    @Override
    public void reset() {
        mHasTriggeredRefresh = false;
        if (loadStart != null) {
            loadStart.setFrame(0);
        }
    }

    @Override
    public void refreshFinish() {

    }

    @Override
    public void refreshing() {
        mHasTriggeredRefresh = true;
        startAnimator();
    }

    @Override
    public void pullToRefresh() {
    }

    @Override
    public void releaseToRefresh() {
        if (loadStart != null) {
            loadStart.setFrame(loadStart.getSize() - 1);
        }
    }

    @Override
    public void pullProgress(float pullDistance, float pullProgress) {
        if (!mHasTriggeredRefresh) {
            if (loadStart == null) {
                imageView = findViewById(R.id.iv_refresh);
                loadStart = new AnimationMan(imageView, R.drawable.ybmlib_load_start);
            }
            if (pullProgress >= 1) {
                if (loadStart != null) {
                    loadStart.setFrame(loadStart.getSize() - 1);
                }

            } else {

                int position = 0;
                if (loadStart != null) {
                    position = (int) (pullProgress * loadStart.getSize());
                }
                if (oldPosition == position) {
                    return;
                }
                oldPosition = position;
                if (loadStart != null) {
                    loadStart.setFrame(position);
                }

            }
        }
    }

    private boolean refreshing;

    @Override
    public void setRefreshing(boolean refreshing) {
        this.refreshing = refreshing;
        if (loading != null) {
            loading.stopNatural();
        } else {
            recyclerRefreshLayout.setRefreshingMyself(refreshing);
        }

    }

    private RecyclerRefreshLayout recyclerRefreshLayout;

    @Override
    public void setRecyclerLayout(RecyclerRefreshLayout recyclerRefreshLayout) {
        this.recyclerRefreshLayout = recyclerRefreshLayout;

    }

}
