package com.ybmmarket20.common.widget.dialog;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @author: yuhaibo
 * @time: 2019-08-15 10:48.
 * projectName: ybm-android.
 * Description: 更换手机号码
 */
public class DialogDescTextAttribute implements Parcelable {
    private int size;
    private int changeTxtColos;
    private int changeTxtStart;
    private int changeTxtEnd;


    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getChangeTxtColos() {
        return changeTxtColos;
    }

    public void setChangeTxtColos(int changeTxtColos) {
        this.changeTxtColos = changeTxtColos;
    }

    public int getChangeTxtStart() {
        return changeTxtStart;
    }

    public void setChangeTxtStart(int changeTxtStart) {
        this.changeTxtStart = changeTxtStart;
    }

    public int getChangeTxtEnd() {
        return changeTxtEnd;
    }

    public void setChangeTxtEnd(int changeTxtEnd) {
        this.changeTxtEnd = changeTxtEnd;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.size);
        dest.writeInt(this.changeTxtColos);
        dest.writeInt(this.changeTxtStart);
        dest.writeInt(this.changeTxtEnd);
    }

    public DialogDescTextAttribute(int size, int changeTxtColos, int changeTxtStart, int changeTxtEnd) {
        this.size = size;
        this.changeTxtColos = changeTxtColos;
        this.changeTxtStart = changeTxtStart;
        this.changeTxtEnd = changeTxtEnd;
    }

    public DialogDescTextAttribute() {
    }

    protected DialogDescTextAttribute(Parcel in) {
        this.size = in.readInt();
        this.changeTxtColos = in.readInt();
        this.changeTxtStart = in.readInt();
        this.changeTxtEnd = in.readInt();
    }

    public static final Creator<DialogDescTextAttribute> CREATOR = new Creator<DialogDescTextAttribute>() {
        @Override
        public DialogDescTextAttribute createFromParcel(Parcel source) {
            return new DialogDescTextAttribute(source);
        }

        @Override
        public DialogDescTextAttribute[] newArray(int size) {
            return new DialogDescTextAttribute[size];
        }
    };
}
