<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@id/status_view_btn_reload"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F8"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/status_view_iv_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:src="@drawable/icon_empty" />

    <TextView
        android:id="@+id/status_view_tv_empty"
        style="@style/status_view_message_text"
        android:layout_marginTop="6dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/status_view_empty_text" />
</LinearLayout>