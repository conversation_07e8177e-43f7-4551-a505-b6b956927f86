package com.ydmmarket.report;

import android.content.Context;

import androidx.test.runner.AndroidJUnit4;

import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.*;

import java.util.ArrayList;

/**
 * Instrumented test, which will execute on an Android device.
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
@RunWith(AndroidJUnit4.class)
public class ExampleInstrumentedTest {
    @Test
    public void useAppContext() {
        ReportJavaBean javaBean = new ReportJavaBean();
        javaBean.list = new ArrayList<String>();
        javaBean.list.add("a");
        javaBean.list.add("b");
        ReportManager.getInstance().report(javaBean);
    }
}