package com.luck.picture.lib.tools;


import android.content.Context;

import androidx.appcompat.app.AlertDialog;


public class PermissionDialogUtil {

    public static void showPermissionInfoDialog(Context context, String content, final Callback callback) {
        AlertDialog.Builder baseDialog = new AlertDialog.Builder(context);
        baseDialog.setOnCancelListener(dialog -> {
            if (callback != null) {
                callback.callback();
            }
        });
        baseDialog.setTitle("权限申请")
                .setMessage(content)
                .setPositiveButton("确认",(dialog, which) -> {
                    dialog.dismiss();
                    if (callback != null) {
                        callback.callback();
                    }
                }).show();
    }

    public interface Callback {
        void callback();
    }

}
