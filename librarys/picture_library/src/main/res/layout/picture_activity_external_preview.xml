<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="?attr/picture.ac_preview.title.bg">

        <ImageButton
            android:id="@+id/left_back"
            android:layout_width="48dp"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@color/transparent"
            android:src="?attr/picture.preview.leftBack.icon"
            android:visibility="visible" />

        <TextView
            android:id="@+id/picture_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:maxEms="11"
            android:textColor="?attr/picture.ac_preview.title.textColor"
            android:textSize="18sp" />

    </RelativeLayout>

    <com.luck.picture.lib.widget.PreviewViewPager
        android:id="@+id/preview_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>