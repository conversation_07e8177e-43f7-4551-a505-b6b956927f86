<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.luck.picture.lib.photoview.PhotoView
        android:id="@+id/preview_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp" />

    <com.luck.picture.lib.widget.longimage.SubsamplingScaleImageView
        android:id="@+id/longImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/ic_video_play"
        android:visibility="invisible" />
</FrameLayout>