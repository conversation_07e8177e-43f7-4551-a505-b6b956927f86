<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint 3.6.3" client="gradle" variant="release" version="3.6.3">

    <issue
        id="ObsoleteLintCustomCheck"
        message="<PERSON><PERSON> found an issue registry (`androidx.appcompat.AppCompatIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade">
        <location
            file="../../../../../.gradle/caches/transforms-2/files-2.1/4bcbea224e1f5a97ce3a89a5d02de9cd/appcompat-1.2.0/jars/lint.jar"/>
    </issue>

    <issue
        id="ExportedReceiver"
        message="Exported receiver does not require permission"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="4"
            column="10"/>
    </issue>

</issues>
