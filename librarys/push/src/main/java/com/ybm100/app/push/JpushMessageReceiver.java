package com.ybm100.app.push;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import cn.jpush.android.api.JPushInterface;

/**
 * 极光消息接收
 * 标题长度 16
 * 内容长度 22
 */
public class JpushMessageReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        Bundle bundle = intent.getExtras();
        if(bundle == null){
            return;
        }
       String message = bundle.getString(JPushInterface.EXTRA_EXTRA);
        if (JPushInterface.ACTION_REGISTRATION_ID.equals(intent.getAction())) {
            String regId = bundle.getString(JPushInterface.EXTRA_REGISTRATION_ID);
            PushManager.sendToken(regId);
        } else if (JPushInterface.ACTION_MESSAGE_RECEIVED.equals(intent.getAction())) {
//            LogUtils.d("自定义消息: " + message);
            PushNotificationManager.handlePassThroughNotify(context,PushManager.PushType.JPUSH,message);
        } else if (JPushInterface.ACTION_NOTIFICATION_RECEIVED.equals(intent.getAction())) {
//            LogUtils.d("推送通知: " + message);
            PushNotificationManager.handleNotificationMsg(context,PushManager.PushType.JPUSH,message);
        } else if (JPushInterface.ACTION_NOTIFICATION_OPENED.equals(intent.getAction())) {
//            LogUtils.d("用户打开了通知");
            PushNotificationManager.handleMsgClicked(context,PushManager.PushType.JPUSH,message);
        } else if (JPushInterface.ACTION_NOTIFICATION_CLICK_ACTION.equals(intent.getAction())) {
//            LogUtils.d("用户点击打开了通知");
            PushNotificationManager.handleMsgClicked(context,PushManager.PushType.JPUSH,message);
        }
    }

}
