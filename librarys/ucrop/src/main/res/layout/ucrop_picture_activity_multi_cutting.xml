<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ucrop_mulit_photobox"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ucrop_color_crop_background"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ucrop_color_toolbar"
        android:minHeight="?attr/actionBarSize">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/ucrop_label_edit_photo"
            android:textColor="@color/ucrop_color_toolbar_widget"
            android:textSize="18sp" />

    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:layout_alignParentBottom="true"
        android:background="@color/ucrop_color_toolbar_widget" />

    <FrameLayout
        android:id="@+id/ucrop_frame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/recyclerView"
        android:layout_below="@id/toolbar">

        <!--<ImageView-->
            <!--android:id="@+id/image_view_logo"-->
            <!--android:layout_width="200dp"-->
            <!--android:layout_height="200dp"-->
            <!--android:layout_gravity="center"-->
            <!--app:srcCompat="@drawable/ucrop_vector_ic_crop"-->
            <!--tools:background="@drawable/ucrop_vector_ic_crop"-->
            <!--tools:ignore="MissingPrefix" />-->

        <com.yalantis.ucrop.view.UCropView
            android:id="@+id/ucrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0" />

    </FrameLayout>

</RelativeLayout>
