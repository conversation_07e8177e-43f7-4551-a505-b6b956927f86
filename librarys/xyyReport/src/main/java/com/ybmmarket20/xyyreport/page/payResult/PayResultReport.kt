package com.ybmmarket20.xyyreport.page.payResult

import android.content.Context
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PayResultReport {

    @JvmStatic
    fun pvTrack(context: Context, orderNo: String?) {
        val spm = SpmUtil.getSpmPv("orderPaySuccess_${orderNo}-0_0")
        SpmLogUtil.print("支付结果页-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }
}