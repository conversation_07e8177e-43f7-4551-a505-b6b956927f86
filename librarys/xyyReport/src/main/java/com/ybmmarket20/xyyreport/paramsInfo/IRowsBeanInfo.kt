package com.ybmmarket20.xyyreport.paramsInfo

import android.content.Context
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

interface IRowsBeanInfo {

    fun getSpmProductId(): Long

    fun getSpmProductName(): String?

    fun getSpmQtSkuData(): String?

    fun getShopCode(): String?

    fun onOpSingleGoods(): Boolean

    fun getQtListData(): String?

    fun setQtListData(qtListData: String?)

    fun onOpGoods(): Boolean

    fun getOPRowsBeanInfo(): OPRowsBeanInfo?

    fun getGroupGoodsPlaceInfo(): GroupGoodsPlaceInfo?

    fun isNormalGoods(): Boolean

    /**
     * 组合购
     */
    fun isSingleGroupPurchase(): Boolean

    /**
     * 加价购
     */
    fun isMultipleGroupPurchase(): Boolean

    /**
     * 是否是加价购or组合购
     */
    fun isGroupPurchase(): Boolean

    /**
     * 加价购or组合购商品是否已经选中
     */
    fun getCombinationSelectedStatus(): Int

    fun getScmId(): String?

    fun isMainFrequently(): Boolean

    fun getListExpId(): String?

    fun isCart(): Boolean

}

interface IGoodsPlaceInfo {
    fun getRank(): Int
    fun getSubRank(): Int
    fun getCustomerGroupId(): String?
    fun getOperationExhibitionId(): String?
}

class OPRowsBeanInfo (
    private val rank: Int,
    private val operationRank: Int,
    private val customerGroupId: String?,
    private val operationExhibitionId: String?
): IGoodsPlaceInfo {
    override fun getRank(): Int {
        return rank
    }
    override fun getSubRank(): Int {
        return operationRank
    }
    override fun getCustomerGroupId(): String? {
        return customerGroupId
    }
    override fun getOperationExhibitionId(): String? {
        return operationExhibitionId
    }
}

class GroupGoodsPlaceInfo(
    private val rank: Int,
    private val subRank: Int,
    private val customerGroupId: String?,
    private val operationExhibitionId: String?
): IGoodsPlaceInfo {
    override fun getRank(): Int {
        return rank
    }

    override fun getSubRank(): Int {
        return subRank
    }

    override fun getCustomerGroupId(): String? {
        return customerGroupId
    }

    override fun getOperationExhibitionId(): String? {
        return operationExhibitionId
    }

    fun getRecordKey(context: Context): String {
        val pageCode = if (context !is XyyReportActivity) "" else context.getScmCnt()?.scmB
        return "${pageCode}_${rank}_$subRank"
    }
}